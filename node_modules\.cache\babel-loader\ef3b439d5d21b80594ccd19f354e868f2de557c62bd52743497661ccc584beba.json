{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich\\\\src\\\\AcademicDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiBook, FiCheckCircle, FiLoader, FiAlertCircle, FiPlus, FiSettings, FiStar, FiUser, FiCalendar, FiFileText, FiBookOpen, FiClipboard, FiTrendingUp, FiMaximize2, FiSave, FiCpu, FiX, FiSend, FiTarget, FiZap } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AcademicDashboard = () => {\n  _s();\n  // Chatbot states\n  const [chatbotOpen, setChatbotOpen] = useState(false);\n  const [chatMessages, setChatMessages] = useState([{\n    text: \"Hello! I'm your EduAI assistant. How can I help you with your academics today?\",\n    isUser: false\n  }]);\n  const [chatInput, setChatInput] = useState('');\n\n  // Modal states\n  const [showAddCourseModal, setShowAddCourseModal] = useState(false);\n  const [showAddExamModal, setShowAddExamModal] = useState(false);\n  const [showCourseDetailsModal, setShowCourseDetailsModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [showStudyMaterialsModal, setShowStudyMaterialsModal] = useState(false);\n  const [showAssignmentsModal, setShowAssignmentsModal] = useState(false);\n  const [showGradesModal, setShowGradesModal] = useState(false);\n  const [showScheduleModal, setShowScheduleModal] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n\n  // Filter states\n  const [courseFilter, setCourseFilter] = useState('All');\n\n  // Data states\n  const [courses, setCourses] = useState([{\n    id: 1,\n    subject: 'Computer Science',\n    title: 'Data Structures & Algorithms',\n    description: 'Master fundamental algorithms and problem-solving techniques',\n    professor: 'Prof. Smith',\n    duration: '12 Weeks',\n    rating: 4.8,\n    color: '#DC2626',\n    bgColor: '#FEE2E2',\n    status: 'Active',\n    progress: 82,\n    materials: ['Textbook Chapter 1-5', 'Video Lectures', 'Practice Problems'],\n    assignments: ['Assignment 1: Arrays', 'Assignment 2: Linked Lists']\n  }, {\n    id: 2,\n    subject: 'Mathematics',\n    title: 'Linear Algebra',\n    description: 'Vectors, matrices, and their applications in computer science',\n    professor: 'Prof. Johnson',\n    duration: '8 Weeks',\n    rating: 4.5,\n    color: '#7C3AED',\n    bgColor: '#EDE9FE',\n    status: 'Active',\n    progress: 90,\n    materials: ['Linear Algebra Textbook', 'Khan Academy Videos'],\n    assignments: ['Matrix Operations Quiz', 'Eigenvalues Problem Set']\n  }, {\n    id: 3,\n    subject: 'Physics',\n    title: 'Quantum Mechanics',\n    description: 'Introduction to quantum theory and its applications',\n    professor: 'Prof. Williams',\n    duration: '10 Weeks',\n    rating: 4.2,\n    color: '#EA580C',\n    bgColor: '#FED7AA',\n    status: 'Active',\n    progress: 65,\n    materials: ['Quantum Physics Textbook', 'Lab Manual'],\n    assignments: ['Wave Function Analysis', 'Quantum States Problem']\n  }, {\n    id: 4,\n    subject: 'Literature',\n    title: 'Modern Poetry',\n    description: 'Analysis of 20th century poetry and poetic techniques',\n    professor: 'Prof. Brown',\n    duration: '6 Weeks',\n    rating: 4.7,\n    color: '#6B7280',\n    bgColor: '#F3F4F6',\n    status: 'Completed',\n    progress: 100,\n    materials: ['Poetry Anthology', 'Critical Essays'],\n    assignments: ['Poetry Analysis Essay', 'Creative Writing Assignment']\n  }]);\n  const [exams, setExams] = useState([{\n    id: 1,\n    title: 'Midterm Exam - Data Structures',\n    description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n    date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n    timeLeft: '3 Days Left',\n    icon: FiFileText,\n    color: '#DC2626',\n    bgColor: '#FEE2E2',\n    courseId: 1\n  }, {\n    id: 2,\n    title: 'Quiz 2 - Linear Algebra',\n    description: 'Coverage: Matrix Operations, Determinants',\n    date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n    timeLeft: '1 Week Left',\n    icon: FiTarget,\n    color: '#7C3AED',\n    bgColor: '#EDE9FE',\n    courseId: 2\n  }, {\n    id: 3,\n    title: 'Final Exam - Quantum Mechanics',\n    description: 'Coverage: Entire Semester',\n    date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n    timeLeft: '2 Weeks Left',\n    icon: FiZap,\n    color: '#EA580C',\n    bgColor: '#FED7AA',\n    courseId: 3\n  }]);\n\n  // Form states\n  const [newCourse, setNewCourse] = useState({\n    subject: '',\n    title: '',\n    description: '',\n    professor: '',\n    duration: '',\n    color: '#DC2626'\n  });\n  const [newExam, setNewExam] = useState({\n    title: '',\n    description: '',\n    date: '',\n    courseId: ''\n  });\n\n  // Animation effect for mindmap nodes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      const nodes = document.querySelectorAll('.mindmap-node');\n      nodes.forEach((node, index) => {\n        node.style.opacity = '0';\n        setTimeout(() => {\n          node.style.opacity = '1';\n          node.style.transition = 'opacity 0.5s ease, transform 0.3s ease';\n        }, 200 * index);\n      });\n    }, 100);\n    return () => clearTimeout(timer);\n  }, []);\n  const handleChatSend = () => {\n    if (chatInput.trim()) {\n      setChatMessages(prev => [...prev, {\n        text: chatInput,\n        isUser: true\n      }]);\n      setChatInput('');\n\n      // Simulate bot response\n      setTimeout(() => {\n        const responses = [\"I can help you with that. What specific aspect do you need assistance with?\", \"That's an interesting question. Let me check my knowledge base...\", \"For that topic, I recommend reviewing chapter 3 of your textbook.\", \"I'm still learning about that subject, but here's what I know...\", \"Would you like me to find study resources for that topic?\"];\n        const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n        setChatMessages(prev => [...prev, {\n          text: randomResponse,\n          isUser: false\n        }]);\n      }, 1000);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleChatSend();\n    }\n  };\n\n  // Functionality handlers\n  const handleAddCourse = () => {\n    if (newCourse.title && newCourse.subject && newCourse.professor) {\n      const course = {\n        id: Date.now(),\n        ...newCourse,\n        rating: 0,\n        bgColor: newCourse.color + '20',\n        status: 'Active',\n        progress: 0,\n        materials: [],\n        assignments: [],\n        grade: 'N/A'\n      };\n      setCourses(prev => [...prev, course]);\n      setNewCourse({\n        subject: '',\n        title: '',\n        description: '',\n        professor: '',\n        duration: '',\n        color: '#DC2626'\n      });\n      setShowAddCourseModal(false);\n      alert('Course added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n  const handleAddExam = () => {\n    if (newExam.title && newExam.date && newExam.courseId) {\n      const exam = {\n        id: Date.now(),\n        ...newExam,\n        timeLeft: calculateTimeLeft(newExam.date),\n        icon: FiFileText,\n        color: '#DC2626',\n        bgColor: '#FEE2E2'\n      };\n      setExams(prev => [...prev, exam]);\n      setNewExam({\n        title: '',\n        description: '',\n        date: '',\n        courseId: ''\n      });\n      setShowAddExamModal(false);\n      alert('Exam added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n  const calculateTimeLeft = examDate => {\n    const today = new Date();\n    const exam = new Date(examDate);\n    const diffTime = exam - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays < 0) return 'Past Due';\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return '1 Day Left';\n    if (diffDays < 7) return `${diffDays} Days Left`;\n    if (diffDays < 14) return '1 Week Left';\n    return `${Math.ceil(diffDays / 7)} Weeks Left`;\n  };\n  const handleCourseClick = course => {\n    setSelectedCourse(course);\n    setShowCourseDetailsModal(true);\n  };\n  const handleFilterChange = filter => {\n    setCourseFilter(filter);\n  };\n  const getFilteredCourses = () => {\n    if (courseFilter === 'All') return courses;\n    return courses.filter(course => course.status === courseFilter);\n  };\n  const calculateStats = () => {\n    const total = courses.length;\n    const completed = courses.filter(c => c.status === 'Completed').length;\n    const active = courses.filter(c => c.status === 'Active').length;\n    const pending = courses.filter(c => c.progress === 0).length;\n    return {\n      total,\n      completed,\n      active,\n      pending\n    };\n  };\n  const stats = calculateStats();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n      padding: '2rem 1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '1200px',\n        margin: '0 auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '2rem',\n          flexWrap: 'wrap',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '2.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0,\n              marginBottom: '0.5rem'\n            },\n            children: \"Academic Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6B7280',\n              margin: 0\n            },\n            children: \"Track your academic progress and resources\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCourseModal(true),\n            style: {\n              background: '#DC2626',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#B91C1C',\n            onMouseLeave: e => e.target.style.background = '#DC2626',\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), \" Add New Course\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSettingsModal(true),\n            style: {\n              background: 'white',\n              color: '#DC2626',\n              border: '2px solid #DC2626',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#FEE2E2',\n            onMouseLeave: e => e.target.style.background = 'white',\n            children: [/*#__PURE__*/_jsxDEV(FiSettings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), \" Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        },\n        children: [{\n          icon: FiBook,\n          label: 'Total Courses',\n          value: stats.total.toString(),\n          color: '#DC2626',\n          bgColor: '#FEE2E2'\n        }, {\n          icon: FiCheckCircle,\n          label: 'Completed',\n          value: stats.completed.toString(),\n          color: '#7C3AED',\n          bgColor: '#EDE9FE'\n        }, {\n          icon: FiLoader,\n          label: 'In Progress',\n          value: stats.active.toString(),\n          color: '#EA580C',\n          bgColor: '#FED7AA'\n        }, {\n          icon: FiAlertCircle,\n          label: 'Pending',\n          value: stats.pending.toString(),\n          color: '#6B7280',\n          bgColor: '#F3F4F6'\n        }].map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '1rem',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n            padding: '1.5rem',\n            display: 'flex',\n            alignItems: 'center',\n            transition: 'transform 0.3s ease',\n            cursor: 'pointer'\n          },\n          onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n          onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: stat.bgColor,\n              padding: '0.75rem',\n              borderRadius: '50%',\n              marginRight: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              size: 24,\n              color: stat.color\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6B7280',\n                fontSize: '0.875rem',\n                margin: 0\n              },\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '2rem',\n                fontWeight: 'bold',\n                color: stat.color,\n                margin: 0\n              },\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '2fr 1fr',\n          gap: '2rem',\n          '@media (max-width: 1024px)': {\n            gridTemplateColumns: '1fr'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  margin: 0\n                },\n                children: \"Your Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '0.5rem'\n                },\n                children: ['All', 'Active', 'Completed'].map((filter, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleFilterChange(filter),\n                  style: {\n                    background: courseFilter === filter ? 'white' : '#B91C1C',\n                    color: courseFilter === filter ? '#DC2626' : 'white',\n                    padding: '0.5rem 1rem',\n                    borderRadius: '0.375rem',\n                    border: 'none',\n                    fontSize: '0.875rem',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: filter\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                  gap: '1rem'\n                },\n                children: getFilteredCourses().map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    padding: '1rem',\n                    transition: 'all 0.3s ease',\n                    cursor: 'pointer'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.borderColor = course.color;\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = '#E5E7EB';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      marginBottom: '0.75rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: course.bgColor,\n                        color: course.color,\n                        fontSize: '0.75rem',\n                        padding: '0.25rem 0.5rem',\n                        borderRadius: '0.25rem'\n                      },\n                      children: course.subject\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#F59E0B',\n                        fontSize: '0.875rem',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 480,\n                        columnNumber: 27\n                      }, this), \" \", course.rating]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      fontWeight: 'bold',\n                      fontSize: '1.125rem',\n                      marginBottom: '0.5rem'\n                    },\n                    children: course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      color: '#6B7280',\n                      fontSize: '0.875rem',\n                      marginBottom: '1rem'\n                    },\n                    children: course.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '2rem',\n                          height: '2rem',\n                          borderRadius: '50%',\n                          background: course.bgColor,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          marginRight: '0.5rem'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(FiUser, {\n                          size: 14,\n                          color: course.color\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 501,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 491,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontSize: '0.875rem'\n                        },\n                        children: course.professor\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        color: '#6B7280'\n                      },\n                      children: course.duration\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  marginTop: '1.5rem',\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '2px dashed #D1D5DB',\n                  borderRadius: '0.5rem',\n                  background: 'transparent',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                },\n                onMouseEnter: e => {\n                  e.target.style.borderColor = '#DC2626';\n                  e.target.style.color = '#DC2626';\n                },\n                onMouseLeave: e => {\n                  e.target.style.borderColor = '#D1D5DB';\n                  e.target.style.color = '#6B7280';\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this), \" Add More Courses\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  margin: 0\n                },\n                children: \"Upcoming Exams\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [{\n                  title: 'Midterm Exam - Data Structures',\n                  description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n                  date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n                  timeLeft: '3 Days Left',\n                  icon: FiFileText,\n                  color: '#DC2626',\n                  bgColor: '#FEE2E2'\n                }, {\n                  title: 'Quiz 2 - Linear Algebra',\n                  description: 'Coverage: Matrix Operations, Determinants',\n                  date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n                  timeLeft: '1 Week Left',\n                  icon: FiTarget,\n                  color: '#7C3AED',\n                  bgColor: '#EDE9FE'\n                }, {\n                  title: 'Final Exam - Quantum Mechanics',\n                  description: 'Coverage: Entire Semester',\n                  date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n                  timeLeft: '2 Weeks Left',\n                  icon: FiZap,\n                  color: '#EA580C',\n                  bgColor: '#FED7AA'\n                }].map((exam, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    padding: '1rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    transition: 'all 0.3s ease',\n                    cursor: 'pointer'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = exam.bgColor;\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'transparent';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: exam.bgColor,\n                      color: exam.color,\n                      padding: '0.75rem',\n                      borderRadius: '0.5rem',\n                      marginRight: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(exam.icon, {\n                      size: 20\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'flex-start',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          style: {\n                            fontWeight: 'bold',\n                            margin: 0,\n                            marginBottom: '0.25rem'\n                          },\n                          children: exam.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 615,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            color: '#6B7280',\n                            fontSize: '0.875rem',\n                            margin: 0\n                          },\n                          children: exam.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: exam.bgColor,\n                          color: exam.color,\n                          fontSize: '0.75rem',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '0.25rem'\n                        },\n                        children: exam.timeLeft\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 618,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        fontSize: '0.875rem',\n                        color: '#6B7280'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                        size: 14,\n                        style: {\n                          marginRight: '0.5rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 629,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: exam.date\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 628,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  marginTop: '1.5rem',\n                  width: '100%',\n                  padding: '0.75rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                },\n                onMouseEnter: e => e.target.style.background = '#B91C1C',\n                onMouseLeave: e => e.target.style.background = '#DC2626',\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 19\n                }, this), \" Add Exam Reminder\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: 'bold',\n                color: '#DC2626',\n                marginBottom: '1rem'\n              },\n              children: \"Academic Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center',\n                marginBottom: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative',\n                  width: '160px',\n                  height: '160px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  style: {\n                    width: '100%',\n                    height: '100%'\n                  },\n                  viewBox: \"0 0 100 100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"50\",\n                    r: \"40\",\n                    stroke: \"#E5E7EB\",\n                    strokeWidth: \"8\",\n                    fill: \"transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"50\",\n                    r: \"40\",\n                    stroke: \"#DC2626\",\n                    strokeWidth: \"8\",\n                    fill: \"transparent\",\n                    strokeDasharray: \"251.2\",\n                    strokeDashoffset: \"62.8\",\n                    strokeLinecap: \"round\",\n                    style: {\n                      transform: 'rotate(-90deg)',\n                      transformOrigin: '50% 50%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    inset: 0,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    flexDirection: 'column'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '2rem',\n                      fontWeight: 'bold',\n                      color: '#DC2626'\n                    },\n                    children: \"75%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#6B7280',\n                      fontSize: '0.875rem'\n                    },\n                    children: \"Overall\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.75rem'\n              },\n              children: [{\n                name: 'Data Structures',\n                progress: '82%',\n                color: '#DC2626'\n              }, {\n                name: 'Linear Algebra',\n                progress: '90%',\n                color: '#7C3AED'\n              }, {\n                name: 'Quantum Mechanics',\n                progress: '65%',\n                color: '#EA580C'\n              }, {\n                name: 'Modern Poetry',\n                progress: '45%',\n                color: '#6B7280'\n              }].map((subject, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '0.75rem',\n                      height: '0.75rem',\n                      borderRadius: '50%',\n                      background: subject.color,\n                      marginRight: '0.5rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: subject.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: subject.progress\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  color: '#DC2626',\n                  margin: 0\n                },\n                children: \"Course Mindmap\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiMaximize2, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '300px',\n                background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n                borderRadius: '1rem',\n                position: 'relative',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mindmap-node\",\n                style: {\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  background: '#DC2626',\n                  color: 'white',\n                  borderRadius: '0.5rem',\n                  padding: '0.5rem 1rem',\n                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  border: '2px solid transparent'\n                },\n                children: \"Computer Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this), [{\n                text: 'Data Structures',\n                top: '30%',\n                left: '20%'\n              }, {\n                text: 'Algorithms',\n                top: '50%',\n                left: '20%'\n              }, {\n                text: 'Databases',\n                top: '70%',\n                left: '20%'\n              }, {\n                text: 'AI/ML',\n                top: '30%',\n                left: '80%'\n              }, {\n                text: 'Networking',\n                top: '50%',\n                left: '80%'\n              }, {\n                text: 'Cybersecurity',\n                top: '70%',\n                left: '80%'\n              }].map((node, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mindmap-node\",\n                  style: {\n                    position: 'absolute',\n                    top: node.top,\n                    left: node.left,\n                    background: 'white',\n                    borderRadius: '0.5rem',\n                    padding: '0.5rem 1rem',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    border: '2px solid transparent',\n                    transform: 'translate(-50%, -50%)'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.transform = 'translate(-50%, -50%) scale(1.05)';\n                    e.target.style.borderColor = '#DC2626';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.transform = 'translate(-50%, -50%) scale(1)';\n                    e.target.style.borderColor = 'transparent';\n                  },\n                  children: node.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    background: '#FCA5A5',\n                    height: '2px',\n                    width: '100px',\n                    top: node.top,\n                    left: node.left === '20%' ? '30%' : '70%',\n                    transformOrigin: 'left center',\n                    transform: node.left === '20%' ? node.top === '30%' ? 'translateY(-50%) rotate(-30deg)' : node.top === '50%' ? 'translateY(-50%)' : 'translateY(-50%) rotate(30deg)' : node.top === '30%' ? 'translateY(-50%) rotate(30deg)' : node.top === '50%' ? 'translateY(-50%)' : 'translateY(-50%) rotate(-30deg)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '1rem',\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 850,\n                  columnNumber: 19\n                }, this), \" Add Node\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiSave, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 19\n                }, this), \" Save\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: 'bold',\n                color: '#DC2626',\n                marginBottom: '1rem'\n              },\n              children: \"Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(2, 1fr)',\n                gap: '0.75rem'\n              },\n              children: [{\n                icon: FiBookOpen,\n                label: 'Study Materials',\n                color: '#DC2626',\n                bgColor: '#FEE2E2'\n              }, {\n                icon: FiClipboard,\n                label: 'Assignments',\n                color: '#7C3AED',\n                bgColor: '#EDE9FE'\n              }, {\n                icon: FiTrendingUp,\n                label: 'Grades',\n                color: '#EA580C',\n                bgColor: '#FED7AA'\n              }, {\n                icon: FiCalendar,\n                label: 'Schedule',\n                color: '#6B7280',\n                bgColor: '#F3F4F6'\n              }].map((action, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: action.bgColor,\n                  color: action.color,\n                  padding: '0.75rem',\n                  borderRadius: '0.5rem',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                },\n                children: [/*#__PURE__*/_jsxDEV(action.icon, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '0.875rem'\n                  },\n                  children: action.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '2rem',\n        right: '2rem',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'flex-end',\n        gap: '1rem',\n        zIndex: 1000\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setChatbotOpen(!chatbotOpen),\n        style: {\n          width: '3.5rem',\n          height: '3.5rem',\n          borderRadius: '50%',\n          background: '#DC2626',\n          color: 'white',\n          border: 'none',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n          transition: 'all 0.3s ease',\n          animation: 'float 3s ease-in-out infinite'\n        },\n        onMouseEnter: e => e.target.style.background = '#B91C1C',\n        onMouseLeave: e => e.target.style.background = '#DC2626',\n        children: /*#__PURE__*/_jsxDEV(FiCpu, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 951,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 9\n      }, this), chatbotOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '350px',\n          height: '500px',\n          background: 'white',\n          borderRadius: '0.75rem',\n          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#DC2626',\n            color: 'white',\n            padding: '0.75rem 1rem',\n            fontWeight: 'bold',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"EduAI Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 976,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setChatbotOpen(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              cursor: 'pointer'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 967,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            padding: '1rem',\n            overflowY: 'auto',\n            background: '#F8FAFC'\n          },\n          children: chatMessages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem',\n              display: 'flex',\n              justifyContent: message.isUser ? 'flex-end' : 'flex-start'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: message.isUser ? '#DC2626' : '#FEE2E2',\n                color: message.isUser ? 'white' : '#1F2937',\n                padding: '0.75rem',\n                borderRadius: '0.5rem',\n                maxWidth: '75%'\n              },\n              children: message.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            padding: '0.75rem',\n            borderTop: '1px solid #E5E7EB',\n            background: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: chatInput,\n            onChange: e => setChatInput(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Type your question...\",\n            style: {\n              flex: 1,\n              padding: '0.5rem 0.75rem',\n              border: '1px solid #E5E7EB',\n              borderRadius: '1.25rem',\n              outline: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1023,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleChatSend,\n            style: {\n              marginLeft: '0.5rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '1.25rem',\n              padding: '0.5rem 1rem',\n              cursor: 'pointer'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiSend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1017,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 920,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes float {\n            0% { transform: translateY(0px); }\n            50% { transform: translateY(-10px); }\n            100% { transform: translateY(0px); }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1057,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 279,\n    columnNumber: 5\n  }, this);\n};\n_s(AcademicDashboard, \"i4vl/kCT9jsx9d5nfiJj4vdQvNY=\");\n_c = AcademicDashboard;\nexport default AcademicDashboard;\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiBook", "FiCheckCircle", "<PERSON><PERSON><PERSON><PERSON>", "FiAlertCircle", "FiPlus", "FiSettings", "FiStar", "FiUser", "FiCalendar", "FiFileText", "FiBookOpen", "FiClipboard", "FiTrendingUp", "FiMaximize2", "FiSave", "FiCpu", "FiX", "FiSend", "<PERSON><PERSON><PERSON><PERSON>", "FiZap", "jsxDEV", "_jsxDEV", "AcademicDashboard", "_s", "chatbotOpen", "setChatbotOpen", "chatMessages", "setChatMessages", "text", "isUser", "chatInput", "setChatInput", "showAddCourseModal", "setShowAddCourseModal", "showAddExamModal", "setShowAddExamModal", "showCourseDetailsModal", "setShowCourseDetailsModal", "showSettingsModal", "setShowSettingsModal", "showStudyMaterialsModal", "setShowStudyMaterialsModal", "showAssignmentsModal", "setShowAssignmentsModal", "showGradesModal", "setShowGradesModal", "showScheduleModal", "setShowScheduleModal", "selectedCourse", "setSelectedCourse", "courseFilter", "setCourseFilter", "courses", "setCourses", "id", "subject", "title", "description", "professor", "duration", "rating", "color", "bgColor", "status", "progress", "materials", "assignments", "exams", "setExams", "date", "timeLeft", "icon", "courseId", "newCourse", "setNewCourse", "newExam", "setNewExam", "timer", "setTimeout", "nodes", "document", "querySelectorAll", "for<PERSON>ach", "node", "index", "style", "opacity", "transition", "clearTimeout", "handleChatSend", "trim", "prev", "responses", "randomResponse", "Math", "floor", "random", "length", "handleKeyPress", "e", "key", "handleAddCourse", "course", "Date", "now", "grade", "alert", "handleAddExam", "exam", "calculateTimeLeft", "examDate", "today", "diffTime", "diffDays", "ceil", "handleCourseClick", "handleFilterChange", "filter", "getFilteredCourses", "calculateStats", "total", "completed", "c", "active", "pending", "stats", "minHeight", "background", "padding", "children", "max<PERSON><PERSON><PERSON>", "margin", "display", "justifyContent", "alignItems", "marginBottom", "flexWrap", "gap", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "borderRadius", "border", "cursor", "onMouseEnter", "target", "onMouseLeave", "gridTemplateColumns", "label", "value", "toString", "map", "stat", "boxShadow", "currentTarget", "transform", "marginRight", "size", "flexDirection", "overflow", "borderColor", "width", "height", "marginTop", "flex", "position", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeLinecap", "transform<PERSON><PERSON>in", "inset", "name", "className", "top", "left", "action", "bottom", "right", "zIndex", "animation", "overflowY", "message", "borderTop", "type", "onChange", "onKeyPress", "placeholder", "outline", "marginLeft", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich/src/AcademicDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>Book, FiCheckCircle, FiLoader, FiAlertCircle, FiPlus, FiSettings, FiStar, FiUser, FiCalendar, FiFileText, FiBookOpen, FiClipboard, FiTrendingUp, FiMaximize2, FiSave, FiCpu, FiX, FiSend, FiTarget, FiZap } from 'react-icons/fi';\n\nconst AcademicDashboard = () => {\n  // Chatbot states\n  const [chatbotOpen, setChatbotOpen] = useState(false);\n  const [chatMessages, setChatMessages] = useState([\n    { text: \"Hello! I'm your EduAI assistant. How can I help you with your academics today?\", isUser: false }\n  ]);\n  const [chatInput, setChatInput] = useState('');\n\n  // Modal states\n  const [showAddCourseModal, setShowAddCourseModal] = useState(false);\n  const [showAddExamModal, setShowAddExamModal] = useState(false);\n  const [showCourseDetailsModal, setShowCourseDetailsModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [showStudyMaterialsModal, setShowStudyMaterialsModal] = useState(false);\n  const [showAssignmentsModal, setShowAssignmentsModal] = useState(false);\n  const [showGradesModal, setShowGradesModal] = useState(false);\n  const [showScheduleModal, setShowScheduleModal] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n\n  // Filter states\n  const [courseFilter, setCourseFilter] = useState('All');\n\n  // Data states\n  const [courses, setCourses] = useState([\n    {\n      id: 1,\n      subject: 'Computer Science',\n      title: 'Data Structures & Algorithms',\n      description: 'Master fundamental algorithms and problem-solving techniques',\n      professor: 'Prof. Smith',\n      duration: '12 Weeks',\n      rating: 4.8,\n      color: '#DC2626',\n      bgColor: '#FEE2E2',\n      status: 'Active',\n      progress: 82,\n      materials: ['Textbook Chapter 1-5', 'Video Lectures', 'Practice Problems'],\n      assignments: ['Assignment 1: Arrays', 'Assignment 2: Linked Lists']\n    },\n    {\n      id: 2,\n      subject: 'Mathematics',\n      title: 'Linear Algebra',\n      description: 'Vectors, matrices, and their applications in computer science',\n      professor: 'Prof. Johnson',\n      duration: '8 Weeks',\n      rating: 4.5,\n      color: '#7C3AED',\n      bgColor: '#EDE9FE',\n      status: 'Active',\n      progress: 90,\n      materials: ['Linear Algebra Textbook', 'Khan Academy Videos'],\n      assignments: ['Matrix Operations Quiz', 'Eigenvalues Problem Set']\n    },\n    {\n      id: 3,\n      subject: 'Physics',\n      title: 'Quantum Mechanics',\n      description: 'Introduction to quantum theory and its applications',\n      professor: 'Prof. Williams',\n      duration: '10 Weeks',\n      rating: 4.2,\n      color: '#EA580C',\n      bgColor: '#FED7AA',\n      status: 'Active',\n      progress: 65,\n      materials: ['Quantum Physics Textbook', 'Lab Manual'],\n      assignments: ['Wave Function Analysis', 'Quantum States Problem']\n    },\n    {\n      id: 4,\n      subject: 'Literature',\n      title: 'Modern Poetry',\n      description: 'Analysis of 20th century poetry and poetic techniques',\n      professor: 'Prof. Brown',\n      duration: '6 Weeks',\n      rating: 4.7,\n      color: '#6B7280',\n      bgColor: '#F3F4F6',\n      status: 'Completed',\n      progress: 100,\n      materials: ['Poetry Anthology', 'Critical Essays'],\n      assignments: ['Poetry Analysis Essay', 'Creative Writing Assignment']\n    }\n  ]);\n\n  const [exams, setExams] = useState([\n    {\n      id: 1,\n      title: 'Midterm Exam - Data Structures',\n      description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n      date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n      timeLeft: '3 Days Left',\n      icon: FiFileText,\n      color: '#DC2626',\n      bgColor: '#FEE2E2',\n      courseId: 1\n    },\n    {\n      id: 2,\n      title: 'Quiz 2 - Linear Algebra',\n      description: 'Coverage: Matrix Operations, Determinants',\n      date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n      timeLeft: '1 Week Left',\n      icon: FiTarget,\n      color: '#7C3AED',\n      bgColor: '#EDE9FE',\n      courseId: 2\n    },\n    {\n      id: 3,\n      title: 'Final Exam - Quantum Mechanics',\n      description: 'Coverage: Entire Semester',\n      date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n      timeLeft: '2 Weeks Left',\n      icon: FiZap,\n      color: '#EA580C',\n      bgColor: '#FED7AA',\n      courseId: 3\n    }\n  ]);\n\n  // Form states\n  const [newCourse, setNewCourse] = useState({\n    subject: '',\n    title: '',\n    description: '',\n    professor: '',\n    duration: '',\n    color: '#DC2626'\n  });\n\n  const [newExam, setNewExam] = useState({\n    title: '',\n    description: '',\n    date: '',\n    courseId: ''\n  });\n\n  // Animation effect for mindmap nodes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      const nodes = document.querySelectorAll('.mindmap-node');\n      nodes.forEach((node, index) => {\n        node.style.opacity = '0';\n        setTimeout(() => {\n          node.style.opacity = '1';\n          node.style.transition = 'opacity 0.5s ease, transform 0.3s ease';\n        }, 200 * index);\n      });\n    }, 100);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const handleChatSend = () => {\n    if (chatInput.trim()) {\n      setChatMessages(prev => [...prev, { text: chatInput, isUser: true }]);\n      setChatInput('');\n\n      // Simulate bot response\n      setTimeout(() => {\n        const responses = [\n          \"I can help you with that. What specific aspect do you need assistance with?\",\n          \"That's an interesting question. Let me check my knowledge base...\",\n          \"For that topic, I recommend reviewing chapter 3 of your textbook.\",\n          \"I'm still learning about that subject, but here's what I know...\",\n          \"Would you like me to find study resources for that topic?\"\n        ];\n        const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n        setChatMessages(prev => [...prev, { text: randomResponse, isUser: false }]);\n      }, 1000);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter') {\n      handleChatSend();\n    }\n  };\n\n  // Functionality handlers\n  const handleAddCourse = () => {\n    if (newCourse.title && newCourse.subject && newCourse.professor) {\n      const course = {\n        id: Date.now(),\n        ...newCourse,\n        rating: 0,\n        bgColor: newCourse.color + '20',\n        status: 'Active',\n        progress: 0,\n        materials: [],\n        assignments: [],\n        grade: 'N/A'\n      };\n      setCourses(prev => [...prev, course]);\n      setNewCourse({\n        subject: '',\n        title: '',\n        description: '',\n        professor: '',\n        duration: '',\n        color: '#DC2626'\n      });\n      setShowAddCourseModal(false);\n      alert('Course added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n\n  const handleAddExam = () => {\n    if (newExam.title && newExam.date && newExam.courseId) {\n      const exam = {\n        id: Date.now(),\n        ...newExam,\n        timeLeft: calculateTimeLeft(newExam.date),\n        icon: FiFileText,\n        color: '#DC2626',\n        bgColor: '#FEE2E2'\n      };\n      setExams(prev => [...prev, exam]);\n      setNewExam({\n        title: '',\n        description: '',\n        date: '',\n        courseId: ''\n      });\n      setShowAddExamModal(false);\n      alert('Exam added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n\n  const calculateTimeLeft = (examDate) => {\n    const today = new Date();\n    const exam = new Date(examDate);\n    const diffTime = exam - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays < 0) return 'Past Due';\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return '1 Day Left';\n    if (diffDays < 7) return `${diffDays} Days Left`;\n    if (diffDays < 14) return '1 Week Left';\n    return `${Math.ceil(diffDays / 7)} Weeks Left`;\n  };\n\n  const handleCourseClick = (course) => {\n    setSelectedCourse(course);\n    setShowCourseDetailsModal(true);\n  };\n\n  const handleFilterChange = (filter) => {\n    setCourseFilter(filter);\n  };\n\n  const getFilteredCourses = () => {\n    if (courseFilter === 'All') return courses;\n    return courses.filter(course => course.status === courseFilter);\n  };\n\n  const calculateStats = () => {\n    const total = courses.length;\n    const completed = courses.filter(c => c.status === 'Completed').length;\n    const active = courses.filter(c => c.status === 'Active').length;\n    const pending = courses.filter(c => c.progress === 0).length;\n\n    return { total, completed, active, pending };\n  };\n\n  const stats = calculateStats();\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n      padding: '2rem 1rem'\n    }}>\n      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n        {/* Header */}\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '2rem',\n          flexWrap: 'wrap',\n          gap: '1rem'\n        }}>\n          <div>\n            <h1 style={{\n              fontSize: '2.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0,\n              marginBottom: '0.5rem'\n            }}>\n              Academic Dashboard\n            </h1>\n            <p style={{ color: '#6B7280', margin: 0 }}>\n              Track your academic progress and resources\n            </p>\n          </div>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={() => setShowAddCourseModal(true)}\n              style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              }}\n              onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n              onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n            >\n              <FiPlus /> Add New Course\n            </button>\n            <button\n              onClick={() => setShowSettingsModal(true)}\n              style={{\n                background: 'white',\n                color: '#DC2626',\n                border: '2px solid #DC2626',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              }}\n              onMouseEnter={(e) => e.target.style.background = '#FEE2E2'}\n              onMouseLeave={(e) => e.target.style.background = 'white'}\n            >\n              <FiSettings /> Settings\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Overview */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        }}>\n          {[\n            { icon: FiBook, label: 'Total Courses', value: stats.total.toString(), color: '#DC2626', bgColor: '#FEE2E2' },\n            { icon: FiCheckCircle, label: 'Completed', value: stats.completed.toString(), color: '#7C3AED', bgColor: '#EDE9FE' },\n            { icon: FiLoader, label: 'In Progress', value: stats.active.toString(), color: '#EA580C', bgColor: '#FED7AA' },\n            { icon: FiAlertCircle, label: 'Pending', value: stats.pending.toString(), color: '#6B7280', bgColor: '#F3F4F6' }\n          ].map((stat, index) => (\n            <div key={index} style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem',\n              display: 'flex',\n              alignItems: 'center',\n              transition: 'transform 0.3s ease',\n              cursor: 'pointer'\n            }}\n            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n            >\n              <div style={{\n                background: stat.bgColor,\n                padding: '0.75rem',\n                borderRadius: '50%',\n                marginRight: '1rem'\n              }}>\n                <stat.icon size={24} color={stat.color} />\n              </div>\n              <div>\n                <p style={{ color: '#6B7280', fontSize: '0.875rem', margin: 0 }}>{stat.label}</p>\n                <h3 style={{ fontSize: '2rem', fontWeight: 'bold', color: stat.color, margin: 0 }}>\n                  {stat.value}\n                </h3>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: '2fr 1fr',\n          gap: '2rem',\n          '@media (max-width: 1024px)': {\n            gridTemplateColumns: '1fr'\n          }\n        }}>\n          {/* Left Column */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n            {/* Courses Section */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '1rem'\n              }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', margin: 0 }}>Your Courses</h2>\n                <div style={{ display: 'flex', gap: '0.5rem' }}>\n                  {['All', 'Active', 'Completed'].map((filter, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleFilterChange(filter)}\n                      style={{\n                        background: courseFilter === filter ? 'white' : '#B91C1C',\n                        color: courseFilter === filter ? '#DC2626' : 'white',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '0.375rem',\n                        border: 'none',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease'\n                      }}\n                    >\n                      {filter}\n                    </button>\n                  ))}\n                </div>\n              </div>\n              <div style={{ padding: '1.5rem' }}>\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                  gap: '1rem'\n                }}>\n                  {getFilteredCourses().map((course, index) => (\n                    <div key={index} style={{\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      padding: '1rem',\n                      transition: 'all 0.3s ease',\n                      cursor: 'pointer'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.borderColor = course.color;\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderColor = '#E5E7EB';\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    }}\n                    >\n                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.75rem' }}>\n                        <span style={{\n                          background: course.bgColor,\n                          color: course.color,\n                          fontSize: '0.75rem',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '0.25rem'\n                        }}>\n                          {course.subject}\n                        </span>\n                        <span style={{ color: '#F59E0B', fontSize: '0.875rem', display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                          <FiStar size={14} /> {course.rating}\n                        </span>\n                      </div>\n                      <h3 style={{ fontWeight: 'bold', fontSize: '1.125rem', marginBottom: '0.5rem' }}>\n                        {course.title}\n                      </h3>\n                      <p style={{ color: '#6B7280', fontSize: '0.875rem', marginBottom: '1rem' }}>\n                        {course.description}\n                      </p>\n                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <div style={{ display: 'flex', alignItems: 'center' }}>\n                          <div style={{\n                            width: '2rem',\n                            height: '2rem',\n                            borderRadius: '50%',\n                            background: course.bgColor,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            marginRight: '0.5rem'\n                          }}>\n                            <FiUser size={14} color={course.color} />\n                          </div>\n                          <span style={{ fontSize: '0.875rem' }}>{course.professor}</span>\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>{course.duration}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <button style={{\n                  marginTop: '1.5rem',\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '2px dashed #D1D5DB',\n                  borderRadius: '0.5rem',\n                  background: 'transparent',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                }}\n                onMouseEnter={(e) => {\n                  e.target.style.borderColor = '#DC2626';\n                  e.target.style.color = '#DC2626';\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.borderColor = '#D1D5DB';\n                  e.target.style.color = '#6B7280';\n                }}\n                >\n                  <FiPlus /> Add More Courses\n                </button>\n              </div>\n            </div>\n\n            {/* Exams Section */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem'\n              }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', margin: 0 }}>Upcoming Exams</h2>\n              </div>\n              <div style={{ padding: '1.5rem' }}>\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  {[\n                    {\n                      title: 'Midterm Exam - Data Structures',\n                      description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n                      date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n                      timeLeft: '3 Days Left',\n                      icon: FiFileText,\n                      color: '#DC2626',\n                      bgColor: '#FEE2E2'\n                    },\n                    {\n                      title: 'Quiz 2 - Linear Algebra',\n                      description: 'Coverage: Matrix Operations, Determinants',\n                      date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n                      timeLeft: '1 Week Left',\n                      icon: FiTarget,\n                      color: '#7C3AED',\n                      bgColor: '#EDE9FE'\n                    },\n                    {\n                      title: 'Final Exam - Quantum Mechanics',\n                      description: 'Coverage: Entire Semester',\n                      date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n                      timeLeft: '2 Weeks Left',\n                      icon: FiZap,\n                      color: '#EA580C',\n                      bgColor: '#FED7AA'\n                    }\n                  ].map((exam, index) => (\n                    <div key={index} style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      padding: '1rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      transition: 'all 0.3s ease',\n                      cursor: 'pointer'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = exam.bgColor;\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = 'transparent';\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    }}\n                    >\n                      <div style={{\n                        background: exam.bgColor,\n                        color: exam.color,\n                        padding: '0.75rem',\n                        borderRadius: '0.5rem',\n                        marginRight: '1rem'\n                      }}>\n                        <exam.icon size={20} />\n                      </div>\n                      <div style={{ flex: 1 }}>\n                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '0.5rem' }}>\n                          <div>\n                            <h3 style={{ fontWeight: 'bold', margin: 0, marginBottom: '0.25rem' }}>{exam.title}</h3>\n                            <p style={{ color: '#6B7280', fontSize: '0.875rem', margin: 0 }}>{exam.description}</p>\n                          </div>\n                          <span style={{\n                            background: exam.bgColor,\n                            color: exam.color,\n                            fontSize: '0.75rem',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '0.25rem'\n                          }}>\n                            {exam.timeLeft}\n                          </span>\n                        </div>\n                        <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', color: '#6B7280' }}>\n                          <FiCalendar size={14} style={{ marginRight: '0.5rem' }} />\n                          <span>{exam.date}</span>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <button style={{\n                  marginTop: '1.5rem',\n                  width: '100%',\n                  padding: '0.75rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                }}\n                onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n                onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n                >\n                  <FiPlus /> Add Exam Reminder\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n            {/* Progress Overview */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', marginBottom: '1rem' }}>\n                Academic Progress\n              </h2>\n              <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '1.5rem' }}>\n                <div style={{ position: 'relative', width: '160px', height: '160px' }}>\n                  <svg style={{ width: '100%', height: '100%' }} viewBox=\"0 0 100 100\">\n                    <circle\n                      cx=\"50\"\n                      cy=\"50\"\n                      r=\"40\"\n                      stroke=\"#E5E7EB\"\n                      strokeWidth=\"8\"\n                      fill=\"transparent\"\n                    />\n                    <circle\n                      cx=\"50\"\n                      cy=\"50\"\n                      r=\"40\"\n                      stroke=\"#DC2626\"\n                      strokeWidth=\"8\"\n                      fill=\"transparent\"\n                      strokeDasharray=\"251.2\"\n                      strokeDashoffset=\"62.8\"\n                      strokeLinecap=\"round\"\n                      style={{ transform: 'rotate(-90deg)', transformOrigin: '50% 50%' }}\n                    />\n                  </svg>\n                  <div style={{\n                    position: 'absolute',\n                    inset: 0,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    flexDirection: 'column'\n                  }}>\n                    <span style={{ fontSize: '2rem', fontWeight: 'bold', color: '#DC2626' }}>75%</span>\n                    <span style={{ color: '#6B7280', fontSize: '0.875rem' }}>Overall</span>\n                  </div>\n                </div>\n              </div>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n                {[\n                  { name: 'Data Structures', progress: '82%', color: '#DC2626' },\n                  { name: 'Linear Algebra', progress: '90%', color: '#7C3AED' },\n                  { name: 'Quantum Mechanics', progress: '65%', color: '#EA580C' },\n                  { name: 'Modern Poetry', progress: '45%', color: '#6B7280' }\n                ].map((subject, index) => (\n                  <div key={index} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                    <div style={{ display: 'flex', alignItems: 'center' }}>\n                      <div style={{\n                        width: '0.75rem',\n                        height: '0.75rem',\n                        borderRadius: '50%',\n                        background: subject.color,\n                        marginRight: '0.5rem'\n                      }} />\n                      <span>{subject.name}</span>\n                    </div>\n                    <span style={{ fontWeight: 'bold' }}>{subject.progress}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Mind Map */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                  Course Mindmap\n                </h2>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer'\n                }}>\n                  <FiMaximize2 />\n                </button>\n              </div>\n              <div style={{\n                height: '300px',\n                background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n                borderRadius: '1rem',\n                position: 'relative',\n                overflow: 'hidden'\n              }}>\n                {/* Central Node */}\n                <div\n                  className=\"mindmap-node\"\n                  style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    background: '#DC2626',\n                    color: 'white',\n                    borderRadius: '0.5rem',\n                    padding: '0.5rem 1rem',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    border: '2px solid transparent'\n                  }}\n                >\n                  Computer Science\n                </div>\n\n                {/* Child Nodes */}\n                {[\n                  { text: 'Data Structures', top: '30%', left: '20%' },\n                  { text: 'Algorithms', top: '50%', left: '20%' },\n                  { text: 'Databases', top: '70%', left: '20%' },\n                  { text: 'AI/ML', top: '30%', left: '80%' },\n                  { text: 'Networking', top: '50%', left: '80%' },\n                  { text: 'Cybersecurity', top: '70%', left: '80%' }\n                ].map((node, index) => (\n                  <div key={index}>\n                    <div\n                      className=\"mindmap-node\"\n                      style={{\n                        position: 'absolute',\n                        top: node.top,\n                        left: node.left,\n                        background: 'white',\n                        borderRadius: '0.5rem',\n                        padding: '0.5rem 1rem',\n                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease',\n                        border: '2px solid transparent',\n                        transform: 'translate(-50%, -50%)'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.transform = 'translate(-50%, -50%) scale(1.05)';\n                        e.target.style.borderColor = '#DC2626';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.transform = 'translate(-50%, -50%) scale(1)';\n                        e.target.style.borderColor = 'transparent';\n                      }}\n                    >\n                      {node.text}\n                    </div>\n                    {/* Connector lines */}\n                    <div style={{\n                      position: 'absolute',\n                      background: '#FCA5A5',\n                      height: '2px',\n                      width: '100px',\n                      top: node.top,\n                      left: node.left === '20%' ? '30%' : '70%',\n                      transformOrigin: 'left center',\n                      transform: node.left === '20%' ?\n                        (node.top === '30%' ? 'translateY(-50%) rotate(-30deg)' :\n                         node.top === '50%' ? 'translateY(-50%)' :\n                         'translateY(-50%) rotate(30deg)') :\n                        (node.top === '30%' ? 'translateY(-50%) rotate(30deg)' :\n                         node.top === '50%' ? 'translateY(-50%)' :\n                         'translateY(-50%) rotate(-30deg)')\n                    }} />\n                  </div>\n                ))}\n              </div>\n              <div style={{ marginTop: '1rem', display: 'flex', justifyContent: 'space-between' }}>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <FiPlus size={14} /> Add Node\n                </button>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <FiSave size={14} /> Save\n                </button>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', marginBottom: '1rem' }}>\n                Quick Actions\n              </h2>\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(2, 1fr)',\n                gap: '0.75rem'\n              }}>\n                {[\n                  { icon: FiBookOpen, label: 'Study Materials', color: '#DC2626', bgColor: '#FEE2E2' },\n                  { icon: FiClipboard, label: 'Assignments', color: '#7C3AED', bgColor: '#EDE9FE' },\n                  { icon: FiTrendingUp, label: 'Grades', color: '#EA580C', bgColor: '#FED7AA' },\n                  { icon: FiCalendar, label: 'Schedule', color: '#6B7280', bgColor: '#F3F4F6' }\n                ].map((action, index) => (\n                  <button key={index} style={{\n                    background: action.bgColor,\n                    color: action.color,\n                    padding: '0.75rem',\n                    borderRadius: '0.5rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    transition: 'all 0.3s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                  >\n                    <action.icon size={24} />\n                    <span style={{ fontSize: '0.875rem' }}>{action.label}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Chatbot */}\n      <div style={{\n        position: 'fixed',\n        bottom: '2rem',\n        right: '2rem',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'flex-end',\n        gap: '1rem',\n        zIndex: 1000\n      }}>\n        {/* Chatbot Toggle Button */}\n        <button\n          onClick={() => setChatbotOpen(!chatbotOpen)}\n          style={{\n            width: '3.5rem',\n            height: '3.5rem',\n            borderRadius: '50%',\n            background: '#DC2626',\n            color: 'white',\n            border: 'none',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n            transition: 'all 0.3s ease',\n            animation: 'float 3s ease-in-out infinite'\n          }}\n          onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n          onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n        >\n          <FiCpu size={20} />\n        </button>\n\n        {/* Chatbot Container */}\n        {chatbotOpen && (\n          <div style={{\n            width: '350px',\n            height: '500px',\n            background: 'white',\n            borderRadius: '0.75rem',\n            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n            overflow: 'hidden',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            {/* Chatbot Header */}\n            <div style={{\n              background: '#DC2626',\n              color: 'white',\n              padding: '0.75rem 1rem',\n              fontWeight: 'bold',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            }}>\n              <span>EduAI Assistant</span>\n              <button\n                onClick={() => setChatbotOpen(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: 'white',\n                  cursor: 'pointer'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            {/* Messages */}\n            <div style={{\n              flex: 1,\n              padding: '1rem',\n              overflowY: 'auto',\n              background: '#F8FAFC'\n            }}>\n              {chatMessages.map((message, index) => (\n                <div key={index} style={{\n                  marginBottom: '1rem',\n                  display: 'flex',\n                  justifyContent: message.isUser ? 'flex-end' : 'flex-start'\n                }}>\n                  <div style={{\n                    background: message.isUser ? '#DC2626' : '#FEE2E2',\n                    color: message.isUser ? 'white' : '#1F2937',\n                    padding: '0.75rem',\n                    borderRadius: '0.5rem',\n                    maxWidth: '75%'\n                  }}>\n                    {message.text}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Input */}\n            <div style={{\n              display: 'flex',\n              padding: '0.75rem',\n              borderTop: '1px solid #E5E7EB',\n              background: 'white'\n            }}>\n              <input\n                type=\"text\"\n                value={chatInput}\n                onChange={(e) => setChatInput(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Type your question...\"\n                style={{\n                  flex: 1,\n                  padding: '0.5rem 0.75rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '1.25rem',\n                  outline: 'none'\n                }}\n              />\n              <button\n                onClick={handleChatSend}\n                style={{\n                  marginLeft: '0.5rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '1.25rem',\n                  padding: '0.5rem 1rem',\n                  cursor: 'pointer'\n                }}\n              >\n                <FiSend />\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* CSS Animation */}\n      <style>\n        {`\n          @keyframes float {\n            0% { transform: translateY(0px); }\n            50% { transform: translateY(-10px); }\n            100% { transform: translateY(0px); }\n          }\n        `}\n      </style>\n    </div>\n  );\n};\n\nexport default AcademicDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7O,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,CAC/C;IAAE8B,IAAI,EAAE,gFAAgF;IAAEC,MAAM,EAAE;EAAM,CAAC,CAC1G,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0C,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,CACrC;IACEwD,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,kBAAkB;IAC3BC,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,8DAA8D;IAC3EC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;IAC1EC,WAAW,EAAE,CAAC,sBAAsB,EAAE,4BAA4B;EACpE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,+DAA+D;IAC5EC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,yBAAyB,EAAE,qBAAqB,CAAC;IAC7DC,WAAW,EAAE,CAAC,wBAAwB,EAAE,yBAAyB;EACnE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,qDAAqD;IAClEC,SAAS,EAAE,gBAAgB;IAC3BC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,0BAA0B,EAAE,YAAY,CAAC;IACrDC,WAAW,EAAE,CAAC,wBAAwB,EAAE,wBAAwB;EAClE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,uDAAuD;IACpEC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;IAClDC,WAAW,EAAE,CAAC,uBAAuB,EAAE,6BAA6B;EACtE,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtE,QAAQ,CAAC,CACjC;IACEwD,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,gCAAgC;IACvCC,WAAW,EAAE,gDAAgD;IAC7DY,IAAI,EAAE,mCAAmC;IACzCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE9D,UAAU;IAChBoD,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,2CAA2C;IACxDY,IAAI,EAAE,kCAAkC;IACxCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAErD,QAAQ;IACd2C,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,gCAAgC;IACvCC,WAAW,EAAE,2BAA2B;IACxCY,IAAI,EAAE,mCAAmC;IACzCC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAEpD,KAAK;IACX0C,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC;IACzCyD,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC;IACrC0D,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfY,IAAI,EAAE,EAAE;IACRG,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACAzE,SAAS,CAAC,MAAM;IACd,MAAM8E,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7B,MAAMC,KAAK,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,eAAe,CAAC;MACxDF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC7BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;QACxBR,UAAU,CAAC,MAAM;UACfK,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;UACxBH,IAAI,CAACE,KAAK,CAACE,UAAU,GAAG,wCAAwC;QAClE,CAAC,EAAE,GAAG,GAAGH,KAAK,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMI,YAAY,CAACX,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI3D,SAAS,CAAC4D,IAAI,CAAC,CAAC,EAAE;MACpB/D,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE/D,IAAI,EAAEE,SAAS;QAAED,MAAM,EAAE;MAAK,CAAC,CAAC,CAAC;MACrEE,YAAY,CAAC,EAAE,CAAC;;MAEhB;MACA+C,UAAU,CAAC,MAAM;QACf,MAAMc,SAAS,GAAG,CAChB,6EAA6E,EAC7E,mEAAmE,EACnE,mEAAmE,EACnE,kEAAkE,EAClE,2DAA2D,CAC5D;QACD,MAAMC,cAAc,GAAGD,SAAS,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,SAAS,CAACK,MAAM,CAAC,CAAC;QAC9EtE,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAE/D,IAAI,EAAEiE,cAAc;UAAEhE,MAAM,EAAE;QAAM,CAAC,CAAC,CAAC;MAC7E,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMqE,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBX,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI5B,SAAS,CAACjB,KAAK,IAAIiB,SAAS,CAAClB,OAAO,IAAIkB,SAAS,CAACf,SAAS,EAAE;MAC/D,MAAM4C,MAAM,GAAG;QACbhD,EAAE,EAAEiD,IAAI,CAACC,GAAG,CAAC,CAAC;QACd,GAAG/B,SAAS;QACZb,MAAM,EAAE,CAAC;QACTE,OAAO,EAAEW,SAAS,CAACZ,KAAK,GAAG,IAAI;QAC/BE,MAAM,EAAE,QAAQ;QAChBC,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,EAAE;QACfuC,KAAK,EAAE;MACT,CAAC;MACDpD,UAAU,CAACsC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEW,MAAM,CAAC,CAAC;MACrC5B,YAAY,CAAC;QACXnB,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZE,KAAK,EAAE;MACT,CAAC,CAAC;MACF5B,qBAAqB,CAAC,KAAK,CAAC;MAC5ByE,KAAK,CAAC,4BAA4B,CAAC;IACrC,CAAC,MAAM;MACLA,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIhC,OAAO,CAACnB,KAAK,IAAImB,OAAO,CAACN,IAAI,IAAIM,OAAO,CAACH,QAAQ,EAAE;MACrD,MAAMoC,IAAI,GAAG;QACXtD,EAAE,EAAEiD,IAAI,CAACC,GAAG,CAAC,CAAC;QACd,GAAG7B,OAAO;QACVL,QAAQ,EAAEuC,iBAAiB,CAAClC,OAAO,CAACN,IAAI,CAAC;QACzCE,IAAI,EAAE9D,UAAU;QAChBoD,KAAK,EAAE,SAAS;QAChBC,OAAO,EAAE;MACX,CAAC;MACDM,QAAQ,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEiB,IAAI,CAAC,CAAC;MACjChC,UAAU,CAAC;QACTpB,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfY,IAAI,EAAE,EAAE;QACRG,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFrC,mBAAmB,CAAC,KAAK,CAAC;MAC1BuE,KAAK,CAAC,0BAA0B,CAAC;IACnC,CAAC,MAAM;MACLA,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAIC,QAAQ,IAAK;IACtC,MAAMC,KAAK,GAAG,IAAIR,IAAI,CAAC,CAAC;IACxB,MAAMK,IAAI,GAAG,IAAIL,IAAI,CAACO,QAAQ,CAAC;IAC/B,MAAME,QAAQ,GAAGJ,IAAI,GAAGG,KAAK;IAC7B,MAAME,QAAQ,GAAGnB,IAAI,CAACoB,IAAI,CAACF,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,UAAU;IACnC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,YAAY;IACvC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,aAAa;IACvC,OAAO,GAAGnB,IAAI,CAACoB,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,aAAa;EAChD,CAAC;EAED,MAAME,iBAAiB,GAAIb,MAAM,IAAK;IACpCrD,iBAAiB,CAACqD,MAAM,CAAC;IACzBjE,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAM+E,kBAAkB,GAAIC,MAAM,IAAK;IACrClE,eAAe,CAACkE,MAAM,CAAC;EACzB,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIpE,YAAY,KAAK,KAAK,EAAE,OAAOE,OAAO;IAC1C,OAAOA,OAAO,CAACiE,MAAM,CAACf,MAAM,IAAIA,MAAM,CAACvC,MAAM,KAAKb,YAAY,CAAC;EACjE,CAAC;EAED,MAAMqE,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAGpE,OAAO,CAAC6C,MAAM;IAC5B,MAAMwB,SAAS,GAAGrE,OAAO,CAACiE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAC3D,MAAM,KAAK,WAAW,CAAC,CAACkC,MAAM;IACtE,MAAM0B,MAAM,GAAGvE,OAAO,CAACiE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAC3D,MAAM,KAAK,QAAQ,CAAC,CAACkC,MAAM;IAChE,MAAM2B,OAAO,GAAGxE,OAAO,CAACiE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAC1D,QAAQ,KAAK,CAAC,CAAC,CAACiC,MAAM;IAE5D,OAAO;MAAEuB,KAAK;MAAEC,SAAS;MAAEE,MAAM;MAAEC;IAAQ,CAAC;EAC9C,CAAC;EAED,MAAMC,KAAK,GAAGN,cAAc,CAAC,CAAC;EAE9B,oBACElG,OAAA;IAAKgE,KAAK,EAAE;MACVyC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,gBACA5G,OAAA;MAAKgE,KAAK,EAAE;QAAE6C,QAAQ,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAAF,QAAA,gBAEnD5G,OAAA;QAAKgE,KAAK,EAAE;UACV+C,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE,MAAM;UACpBC,QAAQ,EAAE,MAAM;UAChBC,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,gBACA5G,OAAA;UAAA4G,QAAA,gBACE5G,OAAA;YAAIgE,KAAK,EAAE;cACTqD,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,MAAM;cAClB9E,KAAK,EAAE,SAAS;cAChBsE,MAAM,EAAE,CAAC;cACTI,YAAY,EAAE;YAChB,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1H,OAAA;YAAGgE,KAAK,EAAE;cAAExB,KAAK,EAAE,SAAS;cAAEsE,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAE3C;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN1H,OAAA;UAAKgE,KAAK,EAAE;YAAE+C,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBAC3C5G,OAAA;YACE2H,OAAO,EAAEA,CAAA,KAAM/G,qBAAqB,CAAC,IAAI,CAAE;YAC3CoD,KAAK,EAAE;cACL0C,UAAU,EAAE,SAAS;cACrBlE,KAAK,EAAE,OAAO;cACdmE,OAAO,EAAE,gBAAgB;cACzBiB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBf,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbC,QAAQ,EAAE,MAAM;cAChBnD,UAAU,EAAE;YACd,CAAE;YACF6D,YAAY,EAAGjD,CAAC,IAAKA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0C,UAAU,GAAG,SAAU;YAC3DuB,YAAY,EAAGnD,CAAC,IAAKA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0C,UAAU,GAAG,SAAU;YAAAE,QAAA,gBAE3D5G,OAAA,CAACjB,MAAM;cAAAwI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1H,OAAA;YACE2H,OAAO,EAAEA,CAAA,KAAMzG,oBAAoB,CAAC,IAAI,CAAE;YAC1C8C,KAAK,EAAE;cACL0C,UAAU,EAAE,OAAO;cACnBlE,KAAK,EAAE,SAAS;cAChBqF,MAAM,EAAE,mBAAmB;cAC3BlB,OAAO,EAAE,gBAAgB;cACzBiB,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE,SAAS;cACjBf,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbC,QAAQ,EAAE,MAAM;cAChBnD,UAAU,EAAE;YACd,CAAE;YACF6D,YAAY,EAAGjD,CAAC,IAAKA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0C,UAAU,GAAG,SAAU;YAC3DuB,YAAY,EAAGnD,CAAC,IAAKA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0C,UAAU,GAAG,OAAQ;YAAAE,QAAA,gBAEzD5G,OAAA,CAAChB,UAAU;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAChB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1H,OAAA;QAAKgE,KAAK,EAAE;UACV+C,OAAO,EAAE,MAAM;UACfmB,mBAAmB,EAAE,sCAAsC;UAC3Dd,GAAG,EAAE,QAAQ;UACbF,YAAY,EAAE;QAChB,CAAE;QAAAN,QAAA,EACC,CACC;UAAE1D,IAAI,EAAEvE,MAAM;UAAEwJ,KAAK,EAAE,eAAe;UAAEC,KAAK,EAAE5B,KAAK,CAACL,KAAK,CAACkC,QAAQ,CAAC,CAAC;UAAE7F,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EAC7G;UAAES,IAAI,EAAEtE,aAAa;UAAEuJ,KAAK,EAAE,WAAW;UAAEC,KAAK,EAAE5B,KAAK,CAACJ,SAAS,CAACiC,QAAQ,CAAC,CAAC;UAAE7F,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EACpH;UAAES,IAAI,EAAErE,QAAQ;UAAEsJ,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAE5B,KAAK,CAACF,MAAM,CAAC+B,QAAQ,CAAC,CAAC;UAAE7F,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EAC9G;UAAES,IAAI,EAAEpE,aAAa;UAAEqJ,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAE5B,KAAK,CAACD,OAAO,CAAC8B,QAAQ,CAAC,CAAC;UAAE7F,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,CACjH,CAAC6F,GAAG,CAAC,CAACC,IAAI,EAAExE,KAAK,kBAChB/D,OAAA;UAAiBgE,KAAK,EAAE;YACtB0C,UAAU,EAAE,OAAO;YACnBkB,YAAY,EAAE,MAAM;YACpBY,SAAS,EAAE,mCAAmC;YAC9C7B,OAAO,EAAE,QAAQ;YACjBI,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpB/C,UAAU,EAAE,qBAAqB;YACjC4D,MAAM,EAAE;UACV,CAAE;UACFC,YAAY,EAAGjD,CAAC,IAAKA,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0E,SAAS,GAAG,kBAAmB;UAC1ET,YAAY,EAAGnD,CAAC,IAAKA,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0E,SAAS,GAAG,eAAgB;UAAA9B,QAAA,gBAErE5G,OAAA;YAAKgE,KAAK,EAAE;cACV0C,UAAU,EAAE6B,IAAI,CAAC9F,OAAO;cACxBkE,OAAO,EAAE,SAAS;cAClBiB,YAAY,EAAE,KAAK;cACnBe,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,eACA5G,OAAA,CAACuI,IAAI,CAACrF,IAAI;cAAC0F,IAAI,EAAE,EAAG;cAACpG,KAAK,EAAE+F,IAAI,CAAC/F;YAAM;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACN1H,OAAA;YAAA4G,QAAA,gBACE5G,OAAA;cAAGgE,KAAK,EAAE;gBAAExB,KAAK,EAAE,SAAS;gBAAE6E,QAAQ,EAAE,UAAU;gBAAEP,MAAM,EAAE;cAAE,CAAE;cAAAF,QAAA,EAAE2B,IAAI,CAACJ;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF1H,OAAA;cAAIgE,KAAK,EAAE;gBAAEqD,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAE9E,KAAK,EAAE+F,IAAI,CAAC/F,KAAK;gBAAEsE,MAAM,EAAE;cAAE,CAAE;cAAAF,QAAA,EAC/E2B,IAAI,CAACH;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GA1BE3D,KAAK;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1H,OAAA;QAAKgE,KAAK,EAAE;UACV+C,OAAO,EAAE,MAAM;UACfmB,mBAAmB,EAAE,SAAS;UAC9Bd,GAAG,EAAE,MAAM;UACX,4BAA4B,EAAE;YAC5Bc,mBAAmB,EAAE;UACvB;QACF,CAAE;QAAAtB,QAAA,gBAEA5G,OAAA;UAAKgE,KAAK,EAAE;YAAE+C,OAAO,EAAE,MAAM;YAAE8B,aAAa,EAAE,QAAQ;YAAEzB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBAEtE5G,OAAA;YAAKgE,KAAK,EAAE;cACV0C,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBY,SAAS,EAAE,mCAAmC;cAC9CM,QAAQ,EAAE;YACZ,CAAE;YAAAlC,QAAA,gBACA5G,OAAA;cAAKgE,KAAK,EAAE;gBACV0C,UAAU,EAAE,SAAS;gBACrBlE,KAAK,EAAE,OAAO;gBACdmE,OAAO,EAAE,QAAQ;gBACjBI,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBE,QAAQ,EAAE,MAAM;gBAChBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBACA5G,OAAA;gBAAIgE,KAAK,EAAE;kBAAEqD,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAER,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAAY;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpF1H,OAAA;gBAAKgE,KAAK,EAAE;kBAAE+C,OAAO,EAAE,MAAM;kBAAEK,GAAG,EAAE;gBAAS,CAAE;gBAAAR,QAAA,EAC5C,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC0B,GAAG,CAAC,CAACtC,MAAM,EAAEjC,KAAK,kBAChD/D,OAAA;kBAEE2H,OAAO,EAAEA,CAAA,KAAM5B,kBAAkB,CAACC,MAAM,CAAE;kBAC1ChC,KAAK,EAAE;oBACL0C,UAAU,EAAE7E,YAAY,KAAKmE,MAAM,GAAG,OAAO,GAAG,SAAS;oBACzDxD,KAAK,EAAEX,YAAY,KAAKmE,MAAM,GAAG,SAAS,GAAG,OAAO;oBACpDW,OAAO,EAAE,aAAa;oBACtBiB,YAAY,EAAE,UAAU;oBACxBC,MAAM,EAAE,MAAM;oBACdR,QAAQ,EAAE,UAAU;oBACpBS,MAAM,EAAE,SAAS;oBACjB5D,UAAU,EAAE;kBACd,CAAE;kBAAA0C,QAAA,EAEDZ;gBAAM,GAbFjC,KAAK;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1H,OAAA;cAAKgE,KAAK,EAAE;gBAAE2C,OAAO,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAChC5G,OAAA;gBAAKgE,KAAK,EAAE;kBACV+C,OAAO,EAAE,MAAM;kBACfmB,mBAAmB,EAAE,sCAAsC;kBAC3Dd,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,EACCX,kBAAkB,CAAC,CAAC,CAACqC,GAAG,CAAC,CAACrD,MAAM,EAAElB,KAAK,kBACtC/D,OAAA;kBAAiBgE,KAAK,EAAE;oBACtB6D,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,QAAQ;oBACtBjB,OAAO,EAAE,MAAM;oBACfzC,UAAU,EAAE,eAAe;oBAC3B4D,MAAM,EAAE;kBACV,CAAE;kBACFC,YAAY,EAAGjD,CAAC,IAAK;oBACnBA,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC+E,WAAW,GAAG9D,MAAM,CAACzC,KAAK;oBAChDsC,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0E,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACFT,YAAY,EAAGnD,CAAC,IAAK;oBACnBA,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC+E,WAAW,GAAG,SAAS;oBAC7CjE,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0E,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAA9B,QAAA,gBAEA5G,OAAA;oBAAKgE,KAAK,EAAE;sBAAE+C,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEE,YAAY,EAAE;oBAAU,CAAE;oBAAAN,QAAA,gBACxF5G,OAAA;sBAAMgE,KAAK,EAAE;wBACX0C,UAAU,EAAEzB,MAAM,CAACxC,OAAO;wBAC1BD,KAAK,EAAEyC,MAAM,CAACzC,KAAK;wBACnB6E,QAAQ,EAAE,SAAS;wBACnBV,OAAO,EAAE,gBAAgB;wBACzBiB,YAAY,EAAE;sBAChB,CAAE;sBAAAhB,QAAA,EACC3B,MAAM,CAAC/C;oBAAO;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACP1H,OAAA;sBAAMgE,KAAK,EAAE;wBAAExB,KAAK,EAAE,SAAS;wBAAE6E,QAAQ,EAAE,UAAU;wBAAEN,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEG,GAAG,EAAE;sBAAU,CAAE;sBAAAR,QAAA,gBAC7G5G,OAAA,CAACf,MAAM;wBAAC2J,IAAI,EAAE;sBAAG;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,KAAC,EAACzC,MAAM,CAAC1C,MAAM;oBAAA;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN1H,OAAA;oBAAIgE,KAAK,EAAE;sBAAEsD,UAAU,EAAE,MAAM;sBAAED,QAAQ,EAAE,UAAU;sBAAEH,YAAY,EAAE;oBAAS,CAAE;oBAAAN,QAAA,EAC7E3B,MAAM,CAAC9C;kBAAK;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACL1H,OAAA;oBAAGgE,KAAK,EAAE;sBAAExB,KAAK,EAAE,SAAS;sBAAE6E,QAAQ,EAAE,UAAU;sBAAEH,YAAY,EAAE;oBAAO,CAAE;oBAAAN,QAAA,EACxE3B,MAAM,CAAC7C;kBAAW;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACJ1H,OAAA;oBAAKgE,KAAK,EAAE;sBAAE+C,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAAL,QAAA,gBACrF5G,OAAA;sBAAKgE,KAAK,EAAE;wBAAE+C,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAAL,QAAA,gBACpD5G,OAAA;wBAAKgE,KAAK,EAAE;0BACVgF,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdrB,YAAY,EAAE,KAAK;0BACnBlB,UAAU,EAAEzB,MAAM,CAACxC,OAAO;0BAC1BsE,OAAO,EAAE,MAAM;0BACfE,UAAU,EAAE,QAAQ;0BACpBD,cAAc,EAAE,QAAQ;0BACxB2B,WAAW,EAAE;wBACf,CAAE;wBAAA/B,QAAA,eACA5G,OAAA,CAACd,MAAM;0BAAC0J,IAAI,EAAE,EAAG;0BAACpG,KAAK,EAAEyC,MAAM,CAACzC;wBAAM;0BAAA+E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,eACN1H,OAAA;wBAAMgE,KAAK,EAAE;0BAAEqD,QAAQ,EAAE;wBAAW,CAAE;wBAAAT,QAAA,EAAE3B,MAAM,CAAC5C;sBAAS;wBAAAkF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACN1H,OAAA;sBAAKgE,KAAK,EAAE;wBAAEqD,QAAQ,EAAE,UAAU;wBAAE7E,KAAK,EAAE;sBAAU,CAAE;sBAAAoE,QAAA,EAAE3B,MAAM,CAAC3C;oBAAQ;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA,GArDE3D,KAAK;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsDV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1H,OAAA;gBAAQgE,KAAK,EAAE;kBACbkF,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE,MAAM;kBACbrC,OAAO,EAAE,SAAS;kBAClBkB,MAAM,EAAE,oBAAoB;kBAC5BD,YAAY,EAAE,QAAQ;kBACtBlB,UAAU,EAAE,aAAa;kBACzBlE,KAAK,EAAE,SAAS;kBAChBsF,MAAM,EAAE,SAAS;kBACjB5D,UAAU,EAAE,eAAe;kBAC3B6C,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBI,GAAG,EAAE;gBACP,CAAE;gBACFW,YAAY,EAAGjD,CAAC,IAAK;kBACnBA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC+E,WAAW,GAAG,SAAS;kBACtCjE,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAACxB,KAAK,GAAG,SAAS;gBAClC,CAAE;gBACFyF,YAAY,EAAGnD,CAAC,IAAK;kBACnBA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC+E,WAAW,GAAG,SAAS;kBACtCjE,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAACxB,KAAK,GAAG,SAAS;gBAClC,CAAE;gBAAAoE,QAAA,gBAEA5G,OAAA,CAACjB,MAAM;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1H,OAAA;YAAKgE,KAAK,EAAE;cACV0C,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBY,SAAS,EAAE,mCAAmC;cAC9CM,QAAQ,EAAE;YACZ,CAAE;YAAAlC,QAAA,gBACA5G,OAAA;cAAKgE,KAAK,EAAE;gBACV0C,UAAU,EAAE,SAAS;gBACrBlE,KAAK,EAAE,OAAO;gBACdmE,OAAO,EAAE;cACX,CAAE;cAAAC,QAAA,eACA5G,OAAA;gBAAIgE,KAAK,EAAE;kBAAEqD,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAER,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAAc;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACN1H,OAAA;cAAKgE,KAAK,EAAE;gBAAE2C,OAAO,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAChC5G,OAAA;gBAAKgE,KAAK,EAAE;kBAAE+C,OAAO,EAAE,MAAM;kBAAE8B,aAAa,EAAE,QAAQ;kBAAEzB,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EACnE,CACC;kBACEzE,KAAK,EAAE,gCAAgC;kBACvCC,WAAW,EAAE,gDAAgD;kBAC7DY,IAAI,EAAE,mCAAmC;kBACzCC,QAAQ,EAAE,aAAa;kBACvBC,IAAI,EAAE9D,UAAU;kBAChBoD,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,EACD;kBACEN,KAAK,EAAE,yBAAyB;kBAChCC,WAAW,EAAE,2CAA2C;kBACxDY,IAAI,EAAE,kCAAkC;kBACxCC,QAAQ,EAAE,aAAa;kBACvBC,IAAI,EAAErD,QAAQ;kBACd2C,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,EACD;kBACEN,KAAK,EAAE,gCAAgC;kBACvCC,WAAW,EAAE,2BAA2B;kBACxCY,IAAI,EAAE,mCAAmC;kBACzCC,QAAQ,EAAE,cAAc;kBACxBC,IAAI,EAAEpD,KAAK;kBACX0C,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,CACF,CAAC6F,GAAG,CAAC,CAAC/C,IAAI,EAAExB,KAAK,kBAChB/D,OAAA;kBAAiBgE,KAAK,EAAE;oBACtB+C,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,YAAY;oBACxBN,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,QAAQ;oBACtB1D,UAAU,EAAE,eAAe;oBAC3B4D,MAAM,EAAE;kBACV,CAAE;kBACFC,YAAY,EAAGjD,CAAC,IAAK;oBACnBA,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0C,UAAU,GAAGnB,IAAI,CAAC9C,OAAO;oBAC/CqC,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0E,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACFT,YAAY,EAAGnD,CAAC,IAAK;oBACnBA,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0C,UAAU,GAAG,aAAa;oBAChD5B,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0E,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAA9B,QAAA,gBAEA5G,OAAA;oBAAKgE,KAAK,EAAE;sBACV0C,UAAU,EAAEnB,IAAI,CAAC9C,OAAO;sBACxBD,KAAK,EAAE+C,IAAI,CAAC/C,KAAK;sBACjBmE,OAAO,EAAE,SAAS;sBAClBiB,YAAY,EAAE,QAAQ;sBACtBe,WAAW,EAAE;oBACf,CAAE;oBAAA/B,QAAA,eACA5G,OAAA,CAACuF,IAAI,CAACrC,IAAI;sBAAC0F,IAAI,EAAE;oBAAG;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACN1H,OAAA;oBAAKgE,KAAK,EAAE;sBAAEmF,IAAI,EAAE;oBAAE,CAAE;oBAAAvC,QAAA,gBACtB5G,OAAA;sBAAKgE,KAAK,EAAE;wBAAE+C,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEC,UAAU,EAAE,YAAY;wBAAEC,YAAY,EAAE;sBAAS,CAAE;sBAAAN,QAAA,gBACjH5G,OAAA;wBAAA4G,QAAA,gBACE5G,OAAA;0BAAIgE,KAAK,EAAE;4BAAEsD,UAAU,EAAE,MAAM;4BAAER,MAAM,EAAE,CAAC;4BAAEI,YAAY,EAAE;0BAAU,CAAE;0BAAAN,QAAA,EAAErB,IAAI,CAACpD;wBAAK;0BAAAoF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxF1H,OAAA;0BAAGgE,KAAK,EAAE;4BAAExB,KAAK,EAAE,SAAS;4BAAE6E,QAAQ,EAAE,UAAU;4BAAEP,MAAM,EAAE;0BAAE,CAAE;0BAAAF,QAAA,EAAErB,IAAI,CAACnD;wBAAW;0BAAAmF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpF,CAAC,eACN1H,OAAA;wBAAMgE,KAAK,EAAE;0BACX0C,UAAU,EAAEnB,IAAI,CAAC9C,OAAO;0BACxBD,KAAK,EAAE+C,IAAI,CAAC/C,KAAK;0BACjB6E,QAAQ,EAAE,SAAS;0BACnBV,OAAO,EAAE,gBAAgB;0BACzBiB,YAAY,EAAE;wBAChB,CAAE;wBAAAhB,QAAA,EACCrB,IAAI,CAACtC;sBAAQ;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN1H,OAAA;sBAAKgE,KAAK,EAAE;wBAAE+C,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEI,QAAQ,EAAE,UAAU;wBAAE7E,KAAK,EAAE;sBAAU,CAAE;sBAAAoE,QAAA,gBAC5F5G,OAAA,CAACb,UAAU;wBAACyJ,IAAI,EAAE,EAAG;wBAAC5E,KAAK,EAAE;0BAAE2E,WAAW,EAAE;wBAAS;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC1D1H,OAAA;wBAAA4G,QAAA,EAAOrB,IAAI,CAACvC;sBAAI;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA/CE3D,KAAK;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgDV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1H,OAAA;gBAAQgE,KAAK,EAAE;kBACbkF,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE,MAAM;kBACbrC,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBlE,KAAK,EAAE,OAAO;kBACdqF,MAAM,EAAE,MAAM;kBACdD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjB5D,UAAU,EAAE,eAAe;kBAC3B6C,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBI,GAAG,EAAE;gBACP,CAAE;gBACFW,YAAY,EAAGjD,CAAC,IAAKA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0C,UAAU,GAAG,SAAU;gBAC3DuB,YAAY,EAAGnD,CAAC,IAAKA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0C,UAAU,GAAG,SAAU;gBAAAE,QAAA,gBAEzD5G,OAAA,CAACjB,MAAM;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1H,OAAA;UAAKgE,KAAK,EAAE;YAAE+C,OAAO,EAAE,MAAM;YAAE8B,aAAa,EAAE,QAAQ;YAAEzB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBAEtE5G,OAAA;YAAKgE,KAAK,EAAE;cACV0C,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBY,SAAS,EAAE,mCAAmC;cAC9C7B,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA5G,OAAA;cAAIgE,KAAK,EAAE;gBAAEqD,QAAQ,EAAE,SAAS;gBAAEC,UAAU,EAAE,MAAM;gBAAE9E,KAAK,EAAE,SAAS;gBAAE0E,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,EAAC;YAEhG;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1H,OAAA;cAAKgE,KAAK,EAAE;gBAAE+C,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAEE,YAAY,EAAE;cAAS,CAAE;cAAAN,QAAA,eAChF5G,OAAA;gBAAKgE,KAAK,EAAE;kBAAEoF,QAAQ,EAAE,UAAU;kBAAEJ,KAAK,EAAE,OAAO;kBAAEC,MAAM,EAAE;gBAAQ,CAAE;gBAAArC,QAAA,gBACpE5G,OAAA;kBAAKgE,KAAK,EAAE;oBAAEgF,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE;kBAAO,CAAE;kBAACI,OAAO,EAAC,aAAa;kBAAAzC,QAAA,gBAClE5G,OAAA;oBACEsJ,EAAE,EAAC,IAAI;oBACPC,EAAE,EAAC,IAAI;oBACPC,CAAC,EAAC,IAAI;oBACNC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,IAAI,EAAC;kBAAa;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACF1H,OAAA;oBACEsJ,EAAE,EAAC,IAAI;oBACPC,EAAE,EAAC,IAAI;oBACPC,CAAC,EAAC,IAAI;oBACNC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,IAAI,EAAC,aAAa;oBAClBC,eAAe,EAAC,OAAO;oBACvBC,gBAAgB,EAAC,MAAM;oBACvBC,aAAa,EAAC,OAAO;oBACrB9F,KAAK,EAAE;sBAAE0E,SAAS,EAAE,gBAAgB;sBAAEqB,eAAe,EAAE;oBAAU;kBAAE;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1H,OAAA;kBAAKgE,KAAK,EAAE;oBACVoF,QAAQ,EAAE,UAAU;oBACpBY,KAAK,EAAE,CAAC;oBACRjD,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxB6B,aAAa,EAAE;kBACjB,CAAE;kBAAAjC,QAAA,gBACA5G,OAAA;oBAAMgE,KAAK,EAAE;sBAAEqD,QAAQ,EAAE,MAAM;sBAAEC,UAAU,EAAE,MAAM;sBAAE9E,KAAK,EAAE;oBAAU,CAAE;oBAAAoE,QAAA,EAAC;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnF1H,OAAA;oBAAMgE,KAAK,EAAE;sBAAExB,KAAK,EAAE,SAAS;sBAAE6E,QAAQ,EAAE;oBAAW,CAAE;oBAAAT,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1H,OAAA;cAAKgE,KAAK,EAAE;gBAAE+C,OAAO,EAAE,MAAM;gBAAE8B,aAAa,EAAE,QAAQ;gBAAEzB,GAAG,EAAE;cAAU,CAAE;cAAAR,QAAA,EACtE,CACC;gBAAEqD,IAAI,EAAE,iBAAiB;gBAAEtH,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAC9D;gBAAEyH,IAAI,EAAE,gBAAgB;gBAAEtH,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAC7D;gBAAEyH,IAAI,EAAE,mBAAmB;gBAAEtH,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAChE;gBAAEyH,IAAI,EAAE,eAAe;gBAAEtH,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,CAC7D,CAAC8F,GAAG,CAAC,CAACpG,OAAO,EAAE6B,KAAK,kBACnB/D,OAAA;gBAAiBgE,KAAK,EAAE;kBAAE+C,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAJ,QAAA,gBACjG5G,OAAA;kBAAKgE,KAAK,EAAE;oBAAE+C,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAL,QAAA,gBACpD5G,OAAA;oBAAKgE,KAAK,EAAE;sBACVgF,KAAK,EAAE,SAAS;sBAChBC,MAAM,EAAE,SAAS;sBACjBrB,YAAY,EAAE,KAAK;sBACnBlB,UAAU,EAAExE,OAAO,CAACM,KAAK;sBACzBmG,WAAW,EAAE;oBACf;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACL1H,OAAA;oBAAA4G,QAAA,EAAO1E,OAAO,CAAC+H;kBAAI;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN1H,OAAA;kBAAMgE,KAAK,EAAE;oBAAEsD,UAAU,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAE1E,OAAO,CAACS;gBAAQ;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAXtD3D,KAAK;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1H,OAAA;YAAKgE,KAAK,EAAE;cACV0C,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBY,SAAS,EAAE,mCAAmC;cAC9C7B,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA5G,OAAA;cAAKgE,KAAK,EAAE;gBAAE+C,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,gBAC3G5G,OAAA;gBAAIgE,KAAK,EAAE;kBAAEqD,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAE9E,KAAK,EAAE,SAAS;kBAAEsE,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAErF;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1H,OAAA;gBAAQgE,KAAK,EAAE;kBACb0C,UAAU,EAAE,MAAM;kBAClBmB,MAAM,EAAE,MAAM;kBACdrF,KAAK,EAAE,SAAS;kBAChBsF,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eACA5G,OAAA,CAACR,WAAW;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN1H,OAAA;cAAKgE,KAAK,EAAE;gBACViF,MAAM,EAAE,OAAO;gBACfvC,UAAU,EAAE,mDAAmD;gBAC/DkB,YAAY,EAAE,MAAM;gBACpBwB,QAAQ,EAAE,UAAU;gBACpBN,QAAQ,EAAE;cACZ,CAAE;cAAAlC,QAAA,gBAEA5G,OAAA;gBACEkK,SAAS,EAAC,cAAc;gBACxBlG,KAAK,EAAE;kBACLoF,QAAQ,EAAE,UAAU;kBACpBe,GAAG,EAAE,KAAK;kBACVC,IAAI,EAAE,KAAK;kBACX1B,SAAS,EAAE,uBAAuB;kBAClChC,UAAU,EAAE,SAAS;kBACrBlE,KAAK,EAAE,OAAO;kBACdoF,YAAY,EAAE,QAAQ;kBACtBjB,OAAO,EAAE,aAAa;kBACtB6B,SAAS,EAAE,mCAAmC;kBAC9ClB,UAAU,EAAE,KAAK;kBACjBQ,MAAM,EAAE,SAAS;kBACjB5D,UAAU,EAAE,eAAe;kBAC3B2D,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,EACH;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAGL,CACC;gBAAEnH,IAAI,EAAE,iBAAiB;gBAAE4J,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EACpD;gBAAE7J,IAAI,EAAE,YAAY;gBAAE4J,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC/C;gBAAE7J,IAAI,EAAE,WAAW;gBAAE4J,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC9C;gBAAE7J,IAAI,EAAE,OAAO;gBAAE4J,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC1C;gBAAE7J,IAAI,EAAE,YAAY;gBAAE4J,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC/C;gBAAE7J,IAAI,EAAE,eAAe;gBAAE4J,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,CACnD,CAAC9B,GAAG,CAAC,CAACxE,IAAI,EAAEC,KAAK,kBAChB/D,OAAA;gBAAA4G,QAAA,gBACE5G,OAAA;kBACEkK,SAAS,EAAC,cAAc;kBACxBlG,KAAK,EAAE;oBACLoF,QAAQ,EAAE,UAAU;oBACpBe,GAAG,EAAErG,IAAI,CAACqG,GAAG;oBACbC,IAAI,EAAEtG,IAAI,CAACsG,IAAI;oBACf1D,UAAU,EAAE,OAAO;oBACnBkB,YAAY,EAAE,QAAQ;oBACtBjB,OAAO,EAAE,aAAa;oBACtB6B,SAAS,EAAE,mCAAmC;oBAC9ClB,UAAU,EAAE,KAAK;oBACjBQ,MAAM,EAAE,SAAS;oBACjB5D,UAAU,EAAE,eAAe;oBAC3B2D,MAAM,EAAE,uBAAuB;oBAC/Ba,SAAS,EAAE;kBACb,CAAE;kBACFX,YAAY,EAAGjD,CAAC,IAAK;oBACnBA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0E,SAAS,GAAG,mCAAmC;oBAC9D5D,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC+E,WAAW,GAAG,SAAS;kBACxC,CAAE;kBACFd,YAAY,EAAGnD,CAAC,IAAK;oBACnBA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0E,SAAS,GAAG,gCAAgC;oBAC3D5D,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC+E,WAAW,GAAG,aAAa;kBAC5C,CAAE;kBAAAnC,QAAA,EAED9C,IAAI,CAACvD;gBAAI;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eAEN1H,OAAA;kBAAKgE,KAAK,EAAE;oBACVoF,QAAQ,EAAE,UAAU;oBACpB1C,UAAU,EAAE,SAAS;oBACrBuC,MAAM,EAAE,KAAK;oBACbD,KAAK,EAAE,OAAO;oBACdmB,GAAG,EAAErG,IAAI,CAACqG,GAAG;oBACbC,IAAI,EAAEtG,IAAI,CAACsG,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;oBACzCL,eAAe,EAAE,aAAa;oBAC9BrB,SAAS,EAAE5E,IAAI,CAACsG,IAAI,KAAK,KAAK,GAC3BtG,IAAI,CAACqG,GAAG,KAAK,KAAK,GAAG,iCAAiC,GACtDrG,IAAI,CAACqG,GAAG,KAAK,KAAK,GAAG,kBAAkB,GACvC,gCAAgC,GAChCrG,IAAI,CAACqG,GAAG,KAAK,KAAK,GAAG,gCAAgC,GACrDrG,IAAI,CAACqG,GAAG,KAAK,KAAK,GAAG,kBAAkB,GACvC;kBACL;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GA5CG3D,KAAK;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6CV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1H,OAAA;cAAKgE,KAAK,EAAE;gBAAEkF,SAAS,EAAE,MAAM;gBAAEnC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAgB,CAAE;cAAAJ,QAAA,gBAClF5G,OAAA;gBAAQgE,KAAK,EAAE;kBACb0C,UAAU,EAAE,MAAM;kBAClBmB,MAAM,EAAE,MAAM;kBACdrF,KAAK,EAAE,SAAS;kBAChBsF,MAAM,EAAE,SAAS;kBACjBf,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBACA5G,OAAA,CAACjB,MAAM;kBAAC6J,IAAI,EAAE;gBAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aACtB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1H,OAAA;gBAAQgE,KAAK,EAAE;kBACb0C,UAAU,EAAE,MAAM;kBAClBmB,MAAM,EAAE,MAAM;kBACdrF,KAAK,EAAE,SAAS;kBAChBsF,MAAM,EAAE,SAAS;kBACjBf,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBACA5G,OAAA,CAACP,MAAM;kBAACmJ,IAAI,EAAE;gBAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SACtB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1H,OAAA;YAAKgE,KAAK,EAAE;cACV0C,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBY,SAAS,EAAE,mCAAmC;cAC9C7B,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA5G,OAAA;cAAIgE,KAAK,EAAE;gBAAEqD,QAAQ,EAAE,SAAS;gBAAEC,UAAU,EAAE,MAAM;gBAAE9E,KAAK,EAAE,SAAS;gBAAE0E,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,EAAC;YAEhG;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1H,OAAA;cAAKgE,KAAK,EAAE;gBACV+C,OAAO,EAAE,MAAM;gBACfmB,mBAAmB,EAAE,gBAAgB;gBACrCd,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,EACC,CACC;gBAAE1D,IAAI,EAAE7D,UAAU;gBAAE8I,KAAK,EAAE,iBAAiB;gBAAE3F,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE;cAAU,CAAC,EACpF;gBAAES,IAAI,EAAE5D,WAAW;gBAAE6I,KAAK,EAAE,aAAa;gBAAE3F,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE;cAAU,CAAC,EACjF;gBAAES,IAAI,EAAE3D,YAAY;gBAAE4I,KAAK,EAAE,QAAQ;gBAAE3F,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE;cAAU,CAAC,EAC7E;gBAAES,IAAI,EAAE/D,UAAU;gBAAEgJ,KAAK,EAAE,UAAU;gBAAE3F,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE;cAAU,CAAC,CAC9E,CAAC6F,GAAG,CAAC,CAAC+B,MAAM,EAAEtG,KAAK,kBAClB/D,OAAA;gBAAoBgE,KAAK,EAAE;kBACzB0C,UAAU,EAAE2D,MAAM,CAAC5H,OAAO;kBAC1BD,KAAK,EAAE6H,MAAM,CAAC7H,KAAK;kBACnBmE,OAAO,EAAE,SAAS;kBAClBiB,YAAY,EAAE,QAAQ;kBACtBC,MAAM,EAAE,MAAM;kBACdC,MAAM,EAAE,SAAS;kBACjBf,OAAO,EAAE,MAAM;kBACf8B,aAAa,EAAE,QAAQ;kBACvB5B,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE,QAAQ;kBACblD,UAAU,EAAE;gBACd,CAAE;gBACF6D,YAAY,EAAGjD,CAAC,IAAK;kBACnBA,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0E,SAAS,GAAG,kBAAkB;kBACpD5D,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAACwE,SAAS,GAAG,mCAAmC;gBACvE,CAAE;gBACFP,YAAY,EAAGnD,CAAC,IAAK;kBACnBA,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAAC0E,SAAS,GAAG,eAAe;kBACjD5D,CAAC,CAAC2D,aAAa,CAACzE,KAAK,CAACwE,SAAS,GAAG,MAAM;gBAC1C,CAAE;gBAAA5B,QAAA,gBAEA5G,OAAA,CAACqK,MAAM,CAACnH,IAAI;kBAAC0F,IAAI,EAAE;gBAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzB1H,OAAA;kBAAMgE,KAAK,EAAE;oBAAEqD,QAAQ,EAAE;kBAAW,CAAE;kBAAAT,QAAA,EAAEyD,MAAM,CAAClC;gBAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAvBjD3D,KAAK;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBV,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1H,OAAA;MAAKgE,KAAK,EAAE;QACVoF,QAAQ,EAAE,OAAO;QACjBkB,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbxD,OAAO,EAAE,MAAM;QACf8B,aAAa,EAAE,QAAQ;QACvB5B,UAAU,EAAE,UAAU;QACtBG,GAAG,EAAE,MAAM;QACXoD,MAAM,EAAE;MACV,CAAE;MAAA5D,QAAA,gBAEA5G,OAAA;QACE2H,OAAO,EAAEA,CAAA,KAAMvH,cAAc,CAAC,CAACD,WAAW,CAAE;QAC5C6D,KAAK,EAAE;UACLgF,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE,QAAQ;UAChBrB,YAAY,EAAE,KAAK;UACnBlB,UAAU,EAAE,SAAS;UACrBlE,KAAK,EAAE,OAAO;UACdqF,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,SAAS;UACjBf,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBwB,SAAS,EAAE,mCAAmC;UAC9CtE,UAAU,EAAE,eAAe;UAC3BuG,SAAS,EAAE;QACb,CAAE;QACF1C,YAAY,EAAGjD,CAAC,IAAKA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0C,UAAU,GAAG,SAAU;QAC3DuB,YAAY,EAAGnD,CAAC,IAAKA,CAAC,CAACkD,MAAM,CAAChE,KAAK,CAAC0C,UAAU,GAAG,SAAU;QAAAE,QAAA,eAE3D5G,OAAA,CAACN,KAAK;UAACkJ,IAAI,EAAE;QAAG;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,EAGRvH,WAAW,iBACVH,OAAA;QAAKgE,KAAK,EAAE;UACVgF,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfvC,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,SAAS;UACvBY,SAAS,EAAE,gCAAgC;UAC3CM,QAAQ,EAAE,QAAQ;UAClB/B,OAAO,EAAE,MAAM;UACf8B,aAAa,EAAE;QACjB,CAAE;QAAAjC,QAAA,gBAEA5G,OAAA;UAAKgE,KAAK,EAAE;YACV0C,UAAU,EAAE,SAAS;YACrBlE,KAAK,EAAE,OAAO;YACdmE,OAAO,EAAE,cAAc;YACvBW,UAAU,EAAE,MAAM;YAClBP,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE;UACd,CAAE;UAAAL,QAAA,gBACA5G,OAAA;YAAA4G,QAAA,EAAM;UAAe;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5B1H,OAAA;YACE2H,OAAO,EAAEA,CAAA,KAAMvH,cAAc,CAAC,KAAK,CAAE;YACrC4D,KAAK,EAAE;cACL0C,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdrF,KAAK,EAAE,OAAO;cACdsF,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEF5G,OAAA,CAACL,GAAG;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN1H,OAAA;UAAKgE,KAAK,EAAE;YACVmF,IAAI,EAAE,CAAC;YACPxC,OAAO,EAAE,MAAM;YACf+D,SAAS,EAAE,MAAM;YACjBhE,UAAU,EAAE;UACd,CAAE;UAAAE,QAAA,EACCvG,YAAY,CAACiI,GAAG,CAAC,CAACqC,OAAO,EAAE5G,KAAK,kBAC/B/D,OAAA;YAAiBgE,KAAK,EAAE;cACtBkD,YAAY,EAAE,MAAM;cACpBH,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE2D,OAAO,CAACnK,MAAM,GAAG,UAAU,GAAG;YAChD,CAAE;YAAAoG,QAAA,eACA5G,OAAA;cAAKgE,KAAK,EAAE;gBACV0C,UAAU,EAAEiE,OAAO,CAACnK,MAAM,GAAG,SAAS,GAAG,SAAS;gBAClDgC,KAAK,EAAEmI,OAAO,CAACnK,MAAM,GAAG,OAAO,GAAG,SAAS;gBAC3CmG,OAAO,EAAE,SAAS;gBAClBiB,YAAY,EAAE,QAAQ;gBACtBf,QAAQ,EAAE;cACZ,CAAE;cAAAD,QAAA,EACC+D,OAAO,CAACpK;YAAI;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAbE3D,KAAK;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1H,OAAA;UAAKgE,KAAK,EAAE;YACV+C,OAAO,EAAE,MAAM;YACfJ,OAAO,EAAE,SAAS;YAClBiE,SAAS,EAAE,mBAAmB;YAC9BlE,UAAU,EAAE;UACd,CAAE;UAAAE,QAAA,gBACA5G,OAAA;YACE6K,IAAI,EAAC,MAAM;YACXzC,KAAK,EAAE3H,SAAU;YACjBqK,QAAQ,EAAGhG,CAAC,IAAKpE,YAAY,CAACoE,CAAC,CAACkD,MAAM,CAACI,KAAK,CAAE;YAC9C2C,UAAU,EAAElG,cAAe;YAC3BmG,WAAW,EAAC,uBAAuB;YACnChH,KAAK,EAAE;cACLmF,IAAI,EAAE,CAAC;cACPxC,OAAO,EAAE,gBAAgB;cACzBkB,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,SAAS;cACvBqD,OAAO,EAAE;YACX;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF1H,OAAA;YACE2H,OAAO,EAAEvD,cAAe;YACxBJ,KAAK,EAAE;cACLkH,UAAU,EAAE,QAAQ;cACpBxE,UAAU,EAAE,SAAS;cACrBlE,KAAK,EAAE,OAAO;cACdqF,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,SAAS;cACvBjB,OAAO,EAAE,aAAa;cACtBmB,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEF5G,OAAA,CAACJ,MAAM;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1H,OAAA;MAAA4G,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACxH,EAAA,CAxiCID,iBAAiB;AAAAkL,EAAA,GAAjBlL,iBAAiB;AA0iCvB,eAAeA,iBAAiB;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}