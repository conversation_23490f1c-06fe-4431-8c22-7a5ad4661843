{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich\\\\src\\\\AcademicDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiBook, FiCheckCircle, FiLoader, FiAlertCircle, FiPlus, FiSettings, FiStar, FiUser, FiCalendar, FiFileText, FiBookOpen, FiClipboard, FiTrendingUp, FiMaximize2, FiSave, FiCpu, FiX, FiSend, FiTarget, FiZap, FiDownload, FiTrash2, FiUpload } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AcademicDashboard = () => {\n  _s();\n  // Chatbot states\n  const [chatbotOpen, setChatbotOpen] = useState(false);\n  const [chatMessages, setChatMessages] = useState([{\n    text: \"Hello! I'm your EduAI assistant. How can I help you with your academics today?\",\n    isUser: false\n  }]);\n  const [chatInput, setChatInput] = useState('');\n\n  // Modal states\n  const [showAddCourseModal, setShowAddCourseModal] = useState(false);\n  const [showAddExamModal, setShowAddExamModal] = useState(false);\n  const [showCourseDetailsModal, setShowCourseDetailsModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [showStudyMaterialsModal, setShowStudyMaterialsModal] = useState(false);\n  const [showAssignmentsModal, setShowAssignmentsModal] = useState(false);\n  const [showGradesModal, setShowGradesModal] = useState(false);\n  const [showScheduleModal, setShowScheduleModal] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n\n  // Filter states\n  const [courseFilter, setCourseFilter] = useState('All');\n\n  // Data states\n  const [courses, setCourses] = useState([{\n    id: 1,\n    subject: 'Computer Science',\n    title: 'Data Structures & Algorithms',\n    description: 'Master fundamental algorithms and problem-solving techniques',\n    professor: 'Prof. Smith',\n    duration: '12 Weeks',\n    rating: 4.8,\n    color: '#DC2626',\n    bgColor: '#FEE2E2',\n    status: 'Active',\n    progress: 82,\n    materials: ['Textbook Chapter 1-5', 'Video Lectures', 'Practice Problems'],\n    assignments: ['Assignment 1: Arrays', 'Assignment 2: Linked Lists']\n  }, {\n    id: 2,\n    subject: 'Mathematics',\n    title: 'Linear Algebra',\n    description: 'Vectors, matrices, and their applications in computer science',\n    professor: 'Prof. Johnson',\n    duration: '8 Weeks',\n    rating: 4.5,\n    color: '#7C3AED',\n    bgColor: '#EDE9FE',\n    status: 'Active',\n    progress: 90,\n    materials: ['Linear Algebra Textbook', 'Khan Academy Videos'],\n    assignments: ['Matrix Operations Quiz', 'Eigenvalues Problem Set']\n  }, {\n    id: 3,\n    subject: 'Physics',\n    title: 'Quantum Mechanics',\n    description: 'Introduction to quantum theory and its applications',\n    professor: 'Prof. Williams',\n    duration: '10 Weeks',\n    rating: 4.2,\n    color: '#EA580C',\n    bgColor: '#FED7AA',\n    status: 'Active',\n    progress: 65,\n    materials: ['Quantum Physics Textbook', 'Lab Manual'],\n    assignments: ['Wave Function Analysis', 'Quantum States Problem']\n  }, {\n    id: 4,\n    subject: 'Literature',\n    title: 'Modern Poetry',\n    description: 'Analysis of 20th century poetry and poetic techniques',\n    professor: 'Prof. Brown',\n    duration: '6 Weeks',\n    rating: 4.7,\n    color: '#6B7280',\n    bgColor: '#F3F4F6',\n    status: 'Completed',\n    progress: 100,\n    materials: ['Poetry Anthology', 'Critical Essays'],\n    assignments: ['Poetry Analysis Essay', 'Creative Writing Assignment']\n  }]);\n  const [exams, setExams] = useState([{\n    id: 1,\n    title: 'Midterm Exam - Data Structures',\n    description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n    date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n    timeLeft: '3 Days Left',\n    icon: FiFileText,\n    color: '#DC2626',\n    bgColor: '#FEE2E2',\n    courseId: 1\n  }, {\n    id: 2,\n    title: 'Quiz 2 - Linear Algebra',\n    description: 'Coverage: Matrix Operations, Determinants',\n    date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n    timeLeft: '1 Week Left',\n    icon: FiTarget,\n    color: '#7C3AED',\n    bgColor: '#EDE9FE',\n    courseId: 2\n  }, {\n    id: 3,\n    title: 'Final Exam - Quantum Mechanics',\n    description: 'Coverage: Entire Semester',\n    date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n    timeLeft: '2 Weeks Left',\n    icon: FiZap,\n    color: '#EA580C',\n    bgColor: '#FED7AA',\n    courseId: 3\n  }]);\n\n  // Form states\n  const [newCourse, setNewCourse] = useState({\n    subject: '',\n    title: '',\n    description: '',\n    professor: '',\n    duration: '',\n    color: '#DC2626'\n  });\n  const [newExam, setNewExam] = useState({\n    title: '',\n    description: '',\n    date: '',\n    courseId: ''\n  });\n\n  // File upload states\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [uploadCourseId, setUploadCourseId] = useState('');\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Admin states\n  const [isAdmin, setIsAdmin] = useState(false); // Set to false by default - only admins can access\n  const [showAdminPanel, setShowAdminPanel] = useState(false);\n  const [adminUploadType, setAdminUploadType] = useState('course'); // 'course' or 'global'\n  const [showAdminLogin, setShowAdminLogin] = useState(false);\n  const [adminPassword, setAdminPassword] = useState('');\n\n  // User suggestion states\n  const [showSuggestionModal, setShowSuggestionModal] = useState(false);\n  const [suggestions, setSuggestions] = useState([{\n    id: 1,\n    userName: 'Rahul Kumar',\n    email: '<EMAIL>',\n    course: 'Mathematics',\n    materialType: 'Textbook',\n    title: 'Advanced Calculus by James Stewart',\n    description: 'Comprehensive calculus textbook with detailed examples and exercises',\n    priority: 'High',\n    status: 'Pending',\n    date: '2023-11-15'\n  }, {\n    id: 2,\n    userName: 'Priya Sharma',\n    email: '<EMAIL>',\n    course: 'Physics',\n    materialType: 'Reference Book',\n    title: 'Concepts of Physics by H.C. Verma',\n    description: 'Essential physics reference for competitive exams',\n    priority: 'Medium',\n    status: 'Approved',\n    date: '2023-11-12'\n  }]);\n  const [newSuggestion, setNewSuggestion] = useState({\n    userName: '',\n    email: '',\n    course: '',\n    materialType: '',\n    title: '',\n    description: '',\n    priority: 'Medium'\n  });\n  const [globalMaterials, setGlobalMaterials] = useState([{\n    id: 1,\n    name: 'Complete Mathematics Guide.pdf',\n    type: 'pdf',\n    size: '15.2 MB',\n    uploadDate: '2023-11-10',\n    category: 'Mathematics',\n    description: 'Comprehensive mathematics guide for all students',\n    downloads: 245,\n    url: '/study-materials/mathematics/Complete Mathematics Guide.pdf',\n    fileName: 'Complete Mathematics Guide.pdf',\n    folderPath: 'mathematics'\n  }, {\n    id: 2,\n    name: 'Physics Formula Sheet.pdf',\n    type: 'pdf',\n    size: '3.8 MB',\n    uploadDate: '2023-11-08',\n    category: 'Physics',\n    description: 'Essential physics formulas and concepts',\n    downloads: 189,\n    url: '/study-materials/physics/Physics Formula Sheet.pdf',\n    fileName: 'Physics Formula Sheet.pdf',\n    folderPath: 'physics'\n  }, {\n    id: 3,\n    name: 'Computer Science Fundamentals.pdf',\n    type: 'pdf',\n    size: '22.1 MB',\n    uploadDate: '2023-11-05',\n    category: 'Computer Science',\n    description: 'Core computer science concepts and algorithms',\n    downloads: 312,\n    url: '/study-materials/computer-science/Computer Science Fundamentals.pdf',\n    fileName: 'Computer Science Fundamentals.pdf',\n    folderPath: 'computer-science'\n  }]);\n\n  // Animation effect for mindmap nodes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      const nodes = document.querySelectorAll('.mindmap-node');\n      nodes.forEach((node, index) => {\n        node.style.opacity = '0';\n        setTimeout(() => {\n          node.style.opacity = '1';\n          node.style.transition = 'opacity 0.5s ease, transform 0.3s ease';\n        }, 200 * index);\n      });\n    }, 100);\n    return () => clearTimeout(timer);\n  }, []);\n  const handleChatSend = () => {\n    if (chatInput.trim()) {\n      setChatMessages(prev => [...prev, {\n        text: chatInput,\n        isUser: true\n      }]);\n      setChatInput('');\n\n      // Simulate bot response\n      setTimeout(() => {\n        const responses = [\"I can help you with that. What specific aspect do you need assistance with?\", \"That's an interesting question. Let me check my knowledge base...\", \"For that topic, I recommend reviewing chapter 3 of your textbook.\", \"I'm still learning about that subject, but here's what I know...\", \"Would you like me to find study resources for that topic?\"];\n        const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n        setChatMessages(prev => [...prev, {\n          text: randomResponse,\n          isUser: false\n        }]);\n      }, 1000);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleChatSend();\n    }\n  };\n\n  // Functionality handlers\n  const handleAddCourse = () => {\n    if (newCourse.title && newCourse.subject && newCourse.professor) {\n      const course = {\n        id: Date.now(),\n        ...newCourse,\n        rating: 0,\n        bgColor: newCourse.color + '20',\n        status: 'Active',\n        progress: 0,\n        materials: [],\n        assignments: [],\n        grade: 'N/A'\n      };\n      setCourses(prev => [...prev, course]);\n      setNewCourse({\n        subject: '',\n        title: '',\n        description: '',\n        professor: '',\n        duration: '',\n        color: '#DC2626'\n      });\n      setShowAddCourseModal(false);\n      alert('Course added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n  const handleAddExam = () => {\n    if (newExam.title && newExam.date && newExam.courseId) {\n      const exam = {\n        id: Date.now(),\n        ...newExam,\n        timeLeft: calculateTimeLeft(newExam.date),\n        icon: FiFileText,\n        color: '#DC2626',\n        bgColor: '#FEE2E2'\n      };\n      setExams(prev => [...prev, exam]);\n      setNewExam({\n        title: '',\n        description: '',\n        date: '',\n        courseId: ''\n      });\n      setShowAddExamModal(false);\n      alert('Exam added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n  const calculateTimeLeft = examDate => {\n    const today = new Date();\n    const exam = new Date(examDate);\n    const diffTime = exam - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays < 0) return 'Past Due';\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return '1 Day Left';\n    if (diffDays < 7) return `${diffDays} Days Left`;\n    if (diffDays < 14) return '1 Week Left';\n    return `${Math.ceil(diffDays / 7)} Weeks Left`;\n  };\n  const handleCourseClick = course => {\n    setSelectedCourse(course);\n    setShowCourseDetailsModal(true);\n  };\n  const handleFilterChange = filter => {\n    setCourseFilter(filter);\n  };\n  const getFilteredCourses = () => {\n    if (courseFilter === 'All') return courses;\n    return courses.filter(course => course.status === courseFilter);\n  };\n  const calculateStats = () => {\n    const total = courses.length;\n    const completed = courses.filter(c => c.status === 'Completed').length;\n    const active = courses.filter(c => c.status === 'Active').length;\n    const pending = courses.filter(c => c.progress === 0).length;\n    return {\n      total,\n      completed,\n      active,\n      pending\n    };\n  };\n  const stats = calculateStats();\n\n  // File upload handlers\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      // Check if file is PDF\n      if (file.type !== 'application/pdf') {\n        alert('Please select a PDF file only');\n        return;\n      }\n\n      // Check file size (max 10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        alert('File size should be less than 10MB');\n        return;\n      }\n      setSelectedFile(file);\n    }\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile || !uploadCourseId) {\n      alert('Please select a file and course');\n      return;\n    }\n    setIsUploading(true);\n    setUploadProgress(0);\n    try {\n      // Simulate file upload progress\n      const uploadInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(uploadInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Create file URL for preview (in real app, this would be uploaded to server)\n      const fileURL = URL.createObjectURL(selectedFile);\n\n      // Add file to course materials\n      setTimeout(() => {\n        setCourses(prev => prev.map(course => {\n          if (course.id.toString() === uploadCourseId) {\n            return {\n              ...course,\n              materials: [...(course.materials || []), {\n                name: selectedFile.name,\n                type: 'pdf',\n                size: (selectedFile.size / 1024 / 1024).toFixed(2) + ' MB',\n                uploadDate: new Date().toLocaleDateString(),\n                url: fileURL\n              }]\n            };\n          }\n          return course;\n        }));\n        setUploadProgress(100);\n        setTimeout(() => {\n          setIsUploading(false);\n          setShowUploadModal(false);\n          setSelectedFile(null);\n          setUploadCourseId('');\n          setUploadProgress(0);\n          alert('PDF uploaded successfully!');\n        }, 500);\n      }, 2000);\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Upload failed. Please try again.');\n      setIsUploading(false);\n    }\n  };\n  const handleDownloadFile = material => {\n    if (material.url) {\n      // Create download link\n      const link = document.createElement('a');\n      link.href = material.url;\n      link.download = material.name;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } else {\n      alert('File not available for download');\n    }\n  };\n  const handleDeleteFile = (courseId, materialIndex) => {\n    if (window.confirm('Are you sure you want to delete this file?')) {\n      setCourses(prev => prev.map(course => {\n        if (course.id === courseId) {\n          const newMaterials = [...course.materials];\n          newMaterials.splice(materialIndex, 1);\n          return {\n            ...course,\n            materials: newMaterials\n          };\n        }\n        return course;\n      }));\n      alert('File deleted successfully!');\n    }\n  };\n\n  // Admin functionality handlers\n  const handleAdminUpload = async () => {\n    if (!selectedFile) {\n      alert('Please select a file');\n      return;\n    }\n    setIsUploading(true);\n    setUploadProgress(0);\n    try {\n      // Simulate file upload progress\n      const uploadInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(uploadInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Create file URL for preview\n      const fileURL = URL.createObjectURL(selectedFile);\n      setTimeout(() => {\n        if (adminUploadType === 'global') {\n          // Add to global materials (available to all users)\n          const newMaterial = {\n            id: Date.now(),\n            name: selectedFile.name,\n            type: 'pdf',\n            size: (selectedFile.size / 1024 / 1024).toFixed(2) + ' MB',\n            uploadDate: new Date().toLocaleDateString(),\n            category: 'General',\n            description: 'Admin uploaded material',\n            downloads: 0,\n            url: fileURL\n          };\n          setGlobalMaterials(prev => [...prev, newMaterial]);\n        } else {\n          // Add to specific course\n          setCourses(prev => prev.map(course => {\n            if (course.id.toString() === uploadCourseId) {\n              return {\n                ...course,\n                materials: [...(course.materials || []), {\n                  name: selectedFile.name,\n                  type: 'pdf',\n                  size: (selectedFile.size / 1024 / 1024).toFixed(2) + ' MB',\n                  uploadDate: new Date().toLocaleDateString(),\n                  url: fileURL,\n                  isAdminUploaded: true\n                }]\n              };\n            }\n            return course;\n          }));\n        }\n        setUploadProgress(100);\n        setTimeout(() => {\n          setIsUploading(false);\n          setShowUploadModal(false);\n          setSelectedFile(null);\n          setUploadCourseId('');\n          setUploadProgress(0);\n          alert('PDF uploaded successfully for all users!');\n        }, 500);\n      }, 2000);\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Upload failed. Please try again.');\n      setIsUploading(false);\n    }\n  };\n  const handleGlobalDownload = async material => {\n    try {\n      // Increment download count\n      setGlobalMaterials(prev => prev.map(mat => mat.id === material.id ? {\n        ...mat,\n        downloads: mat.downloads + 1\n      } : mat));\n\n      // Check if file exists and download\n      const response = await fetch(material.url);\n      if (response.ok) {\n        // Create download link\n        const link = document.createElement('a');\n        link.href = material.url;\n        link.download = material.fileName || material.name;\n        link.target = '_blank'; // Open in new tab if direct download fails\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        // Show success message\n        alert(`Download started: ${material.name}`);\n      } else {\n        // File not found, show instructions\n        alert(`File not found! Please add \"${material.fileName}\" to the \"public/study-materials/${material.folderPath}/\" folder in your project directory.`);\n      }\n    } catch (error) {\n      console.error('Download error:', error);\n      alert(`To download this file, please add \"${material.fileName}\" to the \"public/study-materials/${material.folderPath}/\" folder in your project directory.`);\n    }\n  };\n  const handleDeleteGlobalMaterial = materialId => {\n    if (window.confirm('Are you sure you want to delete this material? It will be removed for all users.')) {\n      setGlobalMaterials(prev => prev.filter(mat => mat.id !== materialId));\n      alert('Material deleted successfully!');\n    }\n  };\n\n  // Function to add material from local folder\n  const addMaterialFromFolder = (fileName, category, description = '') => {\n    const folderMap = {\n      'Mathematics': 'mathematics',\n      'Physics': 'physics',\n      'Computer Science': 'computer-science',\n      'General': 'general'\n    };\n    const folderPath = folderMap[category] || 'general';\n    const newMaterial = {\n      id: Date.now(),\n      name: fileName,\n      type: 'pdf',\n      size: 'Unknown',\n      // Size will be determined when file is accessed\n      uploadDate: new Date().toLocaleDateString(),\n      category: category,\n      description: description || `${category} study material`,\n      downloads: 0,\n      url: `/study-materials/${folderPath}/${fileName}`,\n      fileName: fileName,\n      folderPath: folderPath\n    };\n    setGlobalMaterials(prev => [...prev, newMaterial]);\n    return newMaterial;\n  };\n\n  // Admin login handler\n  const handleAdminLogin = () => {\n    const correctPassword = 'admin123'; // Change this to your desired password\n    if (adminPassword === correctPassword) {\n      setIsAdmin(true);\n      setShowAdminLogin(false);\n      setAdminPassword('');\n      alert('Admin access granted!');\n    } else {\n      alert('Incorrect password! Please try again.');\n      setAdminPassword('');\n    }\n  };\n\n  // Admin logout handler\n  const handleAdminLogout = () => {\n    setIsAdmin(false);\n    setShowAdminPanel(false);\n    alert('Admin logged out successfully!');\n  };\n\n  // Suggestion handlers\n  const handleSubmitSuggestion = () => {\n    if (!newSuggestion.userName || !newSuggestion.email || !newSuggestion.title) {\n      alert('Please fill in all required fields (Name, Email, Title)');\n      return;\n    }\n    const suggestion = {\n      id: Date.now(),\n      ...newSuggestion,\n      status: 'Pending',\n      date: new Date().toLocaleDateString()\n    };\n    setSuggestions(prev => [...prev, suggestion]);\n    setNewSuggestion({\n      userName: '',\n      email: '',\n      course: '',\n      materialType: '',\n      title: '',\n      description: '',\n      priority: 'Medium'\n    });\n    alert('Suggestion submitted successfully! Admin will review it soon.');\n    setShowSuggestionModal(false);\n  };\n  const handleUpdateSuggestionStatus = (suggestionId, newStatus) => {\n    setSuggestions(prev => prev.map(suggestion => suggestion.id === suggestionId ? {\n      ...suggestion,\n      status: newStatus\n    } : suggestion));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n      padding: '2rem 1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '1200px',\n        margin: '0 auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '2rem',\n          flexWrap: 'wrap',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '2.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0,\n              marginBottom: '0.5rem'\n            },\n            children: \"Academic Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6B7280',\n              margin: 0\n            },\n            children: \"Track your academic progress and resources\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            flexWrap: 'wrap'\n          },\n          children: [isAdmin ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowAdminPanel(true),\n              style: {\n                background: '#7C3AED',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => e.target.style.background = '#6D28D9',\n              onMouseLeave: e => e.target.style.background = '#7C3AED',\n              children: [/*#__PURE__*/_jsxDEV(FiCpu, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 19\n              }, this), \" Admin Panel\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAdminLogout,\n              style: {\n                background: '#EF4444',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => e.target.style.background = '#DC2626',\n              onMouseLeave: e => e.target.style.background = '#EF4444',\n              children: [/*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this), \" Logout Admin\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAdminLogin(true),\n            style: {\n              background: '#6B7280',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#4B5563',\n            onMouseLeave: e => e.target.style.background = '#6B7280',\n            children: [/*#__PURE__*/_jsxDEV(FiCpu, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 17\n            }, this), \" Admin Login\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSuggestionModal(true),\n            style: {\n              background: '#059669',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#047857',\n            onMouseLeave: e => e.target.style.background = '#059669',\n            children: [/*#__PURE__*/_jsxDEV(FiSend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 15\n            }, this), \" Suggest Materials\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCourseModal(true),\n            style: {\n              background: '#DC2626',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#B91C1C',\n            onMouseLeave: e => e.target.style.background = '#DC2626',\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 15\n            }, this), \" Add New Course\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSettingsModal(true),\n            style: {\n              background: 'white',\n              color: '#DC2626',\n              border: '2px solid #DC2626',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#FEE2E2',\n            onMouseLeave: e => e.target.style.background = 'white',\n            children: [/*#__PURE__*/_jsxDEV(FiSettings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 15\n            }, this), \" Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 691,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        },\n        children: [{\n          icon: FiBook,\n          label: 'Total Courses',\n          value: stats.total.toString(),\n          color: '#DC2626',\n          bgColor: '#FEE2E2'\n        }, {\n          icon: FiCheckCircle,\n          label: 'Completed',\n          value: stats.completed.toString(),\n          color: '#7C3AED',\n          bgColor: '#EDE9FE'\n        }, {\n          icon: FiLoader,\n          label: 'In Progress',\n          value: stats.active.toString(),\n          color: '#EA580C',\n          bgColor: '#FED7AA'\n        }, {\n          icon: FiAlertCircle,\n          label: 'Pending',\n          value: stats.pending.toString(),\n          color: '#6B7280',\n          bgColor: '#F3F4F6'\n        }].map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '1rem',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n            padding: '1.5rem',\n            display: 'flex',\n            alignItems: 'center',\n            transition: 'transform 0.3s ease',\n            cursor: 'pointer'\n          },\n          onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n          onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: stat.bgColor,\n              padding: '0.75rem',\n              borderRadius: '50%',\n              marginRight: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              size: 24,\n              color: stat.color\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6B7280',\n                fontSize: '0.875rem',\n                margin: 0\n              },\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '2rem',\n                fontWeight: 'bold',\n                color: stat.color,\n                margin: 0\n              },\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '2fr 1fr',\n          gap: '2rem',\n          '@media (max-width: 1024px)': {\n            gridTemplateColumns: '1fr'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  margin: 0\n                },\n                children: \"Your Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '0.5rem'\n                },\n                children: ['All', 'Active', 'Completed'].map((filter, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleFilterChange(filter),\n                  style: {\n                    background: courseFilter === filter ? 'white' : '#B91C1C',\n                    color: courseFilter === filter ? '#DC2626' : 'white',\n                    padding: '0.5rem 1rem',\n                    borderRadius: '0.375rem',\n                    border: 'none',\n                    fontSize: '0.875rem',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: filter\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                  gap: '1rem'\n                },\n                children: getFilteredCourses().map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => handleCourseClick(course),\n                  style: {\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    padding: '1rem',\n                    transition: 'all 0.3s ease',\n                    cursor: 'pointer'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.borderColor = course.color;\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = '#E5E7EB';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      marginBottom: '0.75rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: course.bgColor,\n                        color: course.color,\n                        fontSize: '0.75rem',\n                        padding: '0.25rem 0.5rem',\n                        borderRadius: '0.25rem'\n                      },\n                      children: course.subject\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 965,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#F59E0B',\n                        fontSize: '0.875rem',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 975,\n                        columnNumber: 27\n                      }, this), \" \", course.rating]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 974,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 964,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      fontWeight: 'bold',\n                      fontSize: '1.125rem',\n                      marginBottom: '0.5rem'\n                    },\n                    children: course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 978,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      color: '#6B7280',\n                      fontSize: '0.875rem',\n                      marginBottom: '1rem'\n                    },\n                    children: course.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '2rem',\n                          height: '2rem',\n                          borderRadius: '50%',\n                          background: course.bgColor,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          marginRight: '0.5rem'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(FiUser, {\n                          size: 14,\n                          color: course.color\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 996,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 986,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontSize: '0.875rem'\n                        },\n                        children: course.professor\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 998,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        color: '#6B7280'\n                      },\n                      children: course.duration\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1000,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowAddCourseModal(true),\n                style: {\n                  marginTop: '1.5rem',\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '2px dashed #D1D5DB',\n                  borderRadius: '0.5rem',\n                  background: 'transparent',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                },\n                onMouseEnter: e => {\n                  e.target.style.borderColor = '#DC2626';\n                  e.target.style.color = '#DC2626';\n                },\n                onMouseLeave: e => {\n                  e.target.style.borderColor = '#D1D5DB';\n                  e.target.style.color = '#6B7280';\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 19\n                }, this), \" Add More Courses\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  margin: 0\n                },\n                children: \"Upcoming Exams\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [{\n                  title: 'Midterm Exam - Data Structures',\n                  description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n                  date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n                  timeLeft: '3 Days Left',\n                  icon: FiFileText,\n                  color: '#DC2626',\n                  bgColor: '#FEE2E2'\n                }, {\n                  title: 'Quiz 2 - Linear Algebra',\n                  description: 'Coverage: Matrix Operations, Determinants',\n                  date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n                  timeLeft: '1 Week Left',\n                  icon: FiTarget,\n                  color: '#7C3AED',\n                  bgColor: '#EDE9FE'\n                }, {\n                  title: 'Final Exam - Quantum Mechanics',\n                  description: 'Coverage: Entire Semester',\n                  date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n                  timeLeft: '2 Weeks Left',\n                  icon: FiZap,\n                  color: '#EA580C',\n                  bgColor: '#FED7AA'\n                }].map((exam, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    padding: '1rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    transition: 'all 0.3s ease',\n                    cursor: 'pointer'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = exam.bgColor;\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'transparent';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: exam.bgColor,\n                      color: exam.color,\n                      padding: '0.75rem',\n                      borderRadius: '0.5rem',\n                      marginRight: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(exam.icon, {\n                      size: 20\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1107,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1100,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'flex-start',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          style: {\n                            fontWeight: 'bold',\n                            margin: 0,\n                            marginBottom: '0.25rem'\n                          },\n                          children: exam.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1112,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            color: '#6B7280',\n                            fontSize: '0.875rem',\n                            margin: 0\n                          },\n                          children: exam.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1113,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1111,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: exam.bgColor,\n                          color: exam.color,\n                          fontSize: '0.75rem',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '0.25rem'\n                        },\n                        children: exam.timeLeft\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1115,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1110,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        fontSize: '0.875rem',\n                        color: '#6B7280'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                        size: 14,\n                        style: {\n                          marginRight: '0.5rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1126,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: exam.date\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1127,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1125,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowAddExamModal(true),\n                style: {\n                  marginTop: '1.5rem',\n                  width: '100%',\n                  padding: '0.75rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                },\n                onMouseEnter: e => e.target.style.background = '#B91C1C',\n                onMouseLeave: e => e.target.style.background = '#DC2626',\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1154,\n                  columnNumber: 19\n                }, this), \" Add Exam Reminder\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1038,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: 'bold',\n                color: '#DC2626',\n                marginBottom: '1rem'\n              },\n              children: \"Academic Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center',\n                marginBottom: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative',\n                  width: '160px',\n                  height: '160px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  style: {\n                    width: '100%',\n                    height: '100%'\n                  },\n                  viewBox: \"0 0 100 100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"50\",\n                    r: \"40\",\n                    stroke: \"#E5E7EB\",\n                    strokeWidth: \"8\",\n                    fill: \"transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1175,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"50\",\n                    r: \"40\",\n                    stroke: \"#DC2626\",\n                    strokeWidth: \"8\",\n                    fill: \"transparent\",\n                    strokeDasharray: \"251.2\",\n                    strokeDashoffset: \"62.8\",\n                    strokeLinecap: \"round\",\n                    style: {\n                      transform: 'rotate(-90deg)',\n                      transformOrigin: '50% 50%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1183,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    inset: 0,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    flexDirection: 'column'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '2rem',\n                      fontWeight: 'bold',\n                      color: '#DC2626'\n                    },\n                    children: \"75%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#6B7280',\n                      fontSize: '0.875rem'\n                    },\n                    children: \"Overall\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1205,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1196,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1173,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.75rem'\n              },\n              children: [{\n                name: 'Data Structures',\n                progress: '82%',\n                color: '#DC2626'\n              }, {\n                name: 'Linear Algebra',\n                progress: '90%',\n                color: '#7C3AED'\n              }, {\n                name: 'Quantum Mechanics',\n                progress: '65%',\n                color: '#EA580C'\n              }, {\n                name: 'Modern Poetry',\n                progress: '45%',\n                color: '#6B7280'\n              }].map((subject, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '0.75rem',\n                      height: '0.75rem',\n                      borderRadius: '50%',\n                      background: subject.color,\n                      marginRight: '0.5rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1218,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: subject.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1225,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1217,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: subject.progress\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1227,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1216,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  color: '#DC2626',\n                  margin: 0\n                },\n                children: \"Course Mindmap\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiMaximize2, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1250,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '300px',\n                background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n                borderRadius: '1rem',\n                position: 'relative',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mindmap-node\",\n                style: {\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  background: '#DC2626',\n                  color: 'white',\n                  borderRadius: '0.5rem',\n                  padding: '0.5rem 1rem',\n                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  border: '2px solid transparent'\n                },\n                children: \"Computer Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1261,\n                columnNumber: 17\n              }, this), [{\n                text: 'Data Structures',\n                top: '30%',\n                left: '20%'\n              }, {\n                text: 'Algorithms',\n                top: '50%',\n                left: '20%'\n              }, {\n                text: 'Databases',\n                top: '70%',\n                left: '20%'\n              }, {\n                text: 'AI/ML',\n                top: '30%',\n                left: '80%'\n              }, {\n                text: 'Networking',\n                top: '50%',\n                left: '80%'\n              }, {\n                text: 'Cybersecurity',\n                top: '70%',\n                left: '80%'\n              }].map((node, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mindmap-node\",\n                  style: {\n                    position: 'absolute',\n                    top: node.top,\n                    left: node.left,\n                    background: 'white',\n                    borderRadius: '0.5rem',\n                    padding: '0.5rem 1rem',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    border: '2px solid transparent',\n                    transform: 'translate(-50%, -50%)'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.transform = 'translate(-50%, -50%) scale(1.05)';\n                    e.target.style.borderColor = '#DC2626';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.transform = 'translate(-50%, -50%) scale(1)';\n                    e.target.style.borderColor = 'transparent';\n                  },\n                  children: node.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1292,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    background: '#FCA5A5',\n                    height: '2px',\n                    width: '100px',\n                    top: node.top,\n                    left: node.left === '20%' ? '30%' : '70%',\n                    transformOrigin: 'left center',\n                    transform: node.left === '20%' ? node.top === '30%' ? 'translateY(-50%) rotate(-30deg)' : node.top === '50%' ? 'translateY(-50%)' : 'translateY(-50%) rotate(30deg)' : node.top === '30%' ? 'translateY(-50%) rotate(30deg)' : node.top === '50%' ? 'translateY(-50%)' : 'translateY(-50%) rotate(-30deg)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1320,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1291,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '1rem',\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 19\n                }, this), \" Add Node\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiSave, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 19\n                }, this), \" Save\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: 'bold',\n                color: '#DC2626',\n                marginBottom: '1rem'\n              },\n              children: \"Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(2, 1fr)',\n                gap: '0.75rem'\n              },\n              children: [{\n                icon: FiBookOpen,\n                label: 'Study Materials',\n                color: '#DC2626',\n                bgColor: '#FEE2E2',\n                action: () => setShowStudyMaterialsModal(true)\n              }, {\n                icon: FiClipboard,\n                label: 'Assignments',\n                color: '#7C3AED',\n                bgColor: '#EDE9FE',\n                action: () => setShowAssignmentsModal(true)\n              }, {\n                icon: FiTrendingUp,\n                label: 'Grades',\n                color: '#EA580C',\n                bgColor: '#FED7AA',\n                action: () => setShowGradesModal(true)\n              }, {\n                icon: FiCalendar,\n                label: 'Schedule',\n                color: '#6B7280',\n                bgColor: '#F3F4F6',\n                action: () => setShowScheduleModal(true)\n              }].map((action, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: action.action,\n                style: {\n                  background: action.bgColor,\n                  color: action.color,\n                  padding: '0.75rem',\n                  borderRadius: '0.5rem',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                },\n                children: [/*#__PURE__*/_jsxDEV(action.icon, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1411,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '0.875rem'\n                  },\n                  children: action.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1412,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1386,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 889,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 689,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '2rem',\n        right: '2rem',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'flex-end',\n        gap: '1rem',\n        zIndex: 1000\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setChatbotOpen(!chatbotOpen),\n        style: {\n          width: '3.5rem',\n          height: '3.5rem',\n          borderRadius: '50%',\n          background: '#DC2626',\n          color: 'white',\n          border: 'none',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n          transition: 'all 0.3s ease',\n          animation: 'float 3s ease-in-out infinite'\n        },\n        onMouseEnter: e => e.target.style.background = '#B91C1C',\n        onMouseLeave: e => e.target.style.background = '#DC2626',\n        children: /*#__PURE__*/_jsxDEV(FiCpu, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1453,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1433,\n        columnNumber: 9\n      }, this), chatbotOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '350px',\n          height: '500px',\n          background: 'white',\n          borderRadius: '0.75rem',\n          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#DC2626',\n            color: 'white',\n            padding: '0.75rem 1rem',\n            fontWeight: 'bold',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"EduAI Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1478,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setChatbotOpen(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              cursor: 'pointer'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1488,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1479,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1469,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            padding: '1rem',\n            overflowY: 'auto',\n            background: '#F8FAFC'\n          },\n          children: chatMessages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem',\n              display: 'flex',\n              justifyContent: message.isUser ? 'flex-end' : 'flex-start'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: message.isUser ? '#DC2626' : '#FEE2E2',\n                color: message.isUser ? 'white' : '#1F2937',\n                padding: '0.75rem',\n                borderRadius: '0.5rem',\n                maxWidth: '75%'\n              },\n              children: message.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1505,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1500,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1493,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            padding: '0.75rem',\n            borderTop: '1px solid #E5E7EB',\n            background: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: chatInput,\n            onChange: e => setChatInput(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Type your question...\",\n            style: {\n              flex: 1,\n              padding: '0.5rem 0.75rem',\n              border: '1px solid #E5E7EB',\n              borderRadius: '1.25rem',\n              outline: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleChatSend,\n            style: {\n              marginLeft: '0.5rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '1.25rem',\n              padding: '0.5rem 1rem',\n              cursor: 'pointer'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiSend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1551,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1539,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1519,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1458,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1422,\n      columnNumber: 7\n    }, this), showAddCourseModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '500px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Add New Course\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1582,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCourseModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1595,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1585,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1581,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Course Title *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1601,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCourse.title,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                title: e.target.value\n              })),\n              placeholder: \"e.g., Data Structures & Algorithms\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1602,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Subject *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1618,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCourse.subject,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                subject: e.target.value\n              })),\n              placeholder: \"e.g., Computer Science\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1619,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1617,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Professor *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1635,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCourse.professor,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                professor: e.target.value\n              })),\n              placeholder: \"e.g., Prof. Smith\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1636,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1634,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1652,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newCourse.description,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                description: e.target.value\n              })),\n              placeholder: \"Course description...\",\n              rows: 3,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem',\n                resize: 'vertical'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1653,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1651,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Duration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1670,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCourse.duration,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                duration: e.target.value\n              })),\n              placeholder: \"e.g., 12 Weeks\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1671,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1669,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Color Theme\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1687,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '0.5rem'\n              },\n              children: ['#DC2626', '#7C3AED', '#EA580C', '#6B7280', '#059669', '#0891B2'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setNewCourse(prev => ({\n                  ...prev,\n                  color\n                })),\n                style: {\n                  width: '2rem',\n                  height: '2rem',\n                  borderRadius: '50%',\n                  background: color,\n                  border: newCourse.color === color ? '3px solid #000' : '1px solid #E5E7EB',\n                  cursor: 'pointer'\n                }\n              }, color, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1690,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1688,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1686,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1599,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginTop: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCourseModal(false),\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: 'white',\n              color: '#6B7280',\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1708,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCourse,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Add Course\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1722,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1707,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1572,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1560,\n      columnNumber: 9\n    }, this), showAddExamModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '500px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Add Exam Reminder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1763,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddExamModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1776,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1766,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1762,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Exam Title *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1782,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newExam.title,\n              onChange: e => setNewExam(prev => ({\n                ...prev,\n                title: e.target.value\n              })),\n              placeholder: \"e.g., Midterm Exam - Data Structures\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1783,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1781,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Course *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1799,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: newExam.courseId,\n              onChange: e => setNewExam(prev => ({\n                ...prev,\n                courseId: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a course\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1811,\n                columnNumber: 19\n              }, this), courses.map(course => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: course.id,\n                children: course.title\n              }, course.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1813,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1800,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1798,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Exam Date *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1819,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: newExam.date,\n              onChange: e => setNewExam(prev => ({\n                ...prev,\n                date: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1820,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1818,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1835,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newExam.description,\n              onChange: e => setNewExam(prev => ({\n                ...prev,\n                description: e.target.value\n              })),\n              placeholder: \"Exam coverage and details...\",\n              rows: 3,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem',\n                resize: 'vertical'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1836,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1834,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1780,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginTop: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddExamModal(false),\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: 'white',\n              color: '#6B7280',\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1854,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddExam,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Add Exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1868,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1853,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1755,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1743,\n      columnNumber: 9\n    }, this), showSettingsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '600px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Dashboard Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1911,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSettingsModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1924,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1914,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1910,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#374151'\n              },\n              children: \"Preferences\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1930,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Email Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1935,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    background: '#DC2626',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '1rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '0.875rem'\n                  },\n                  children: \"Enabled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1936,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1934,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Dark Mode\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1949,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    background: '#E5E7EB',\n                    color: '#6B7280',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '1rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '0.875rem'\n                  },\n                  children: \"Disabled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1950,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1948,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Auto-save Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1963,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    background: '#DC2626',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '1rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '0.875rem'\n                  },\n                  children: \"Enabled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1964,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1962,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1933,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1929,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#374151'\n              },\n              children: \"Data Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1980,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  padding: '0.75rem',\n                  background: '#F3F4F6',\n                  color: '#374151',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  textAlign: 'left'\n                },\n                children: \"Export Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1984,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  padding: '0.75rem',\n                  background: '#F3F4F6',\n                  color: '#374151',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  textAlign: 'left'\n                },\n                children: \"Import Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1995,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  padding: '0.75rem',\n                  background: '#FEE2E2',\n                  color: '#DC2626',\n                  border: '1px solid #FECACA',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  textAlign: 'left'\n                },\n                children: \"Reset All Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2006,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1983,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1979,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1928,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            marginTop: '2rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSettingsModal(false),\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2022,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2021,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1901,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1889,\n      columnNumber: 9\n    }, this), showStudyMaterialsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '700px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Study Materials\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2064,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowStudyMaterialsModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2077,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2067,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2063,\n          columnNumber: 13\n        }, this), isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowUploadModal(true),\n            style: {\n              background: '#7C3AED',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#6D28D9',\n            onMouseLeave: e => e.target.style.background = '#7C3AED',\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2100,\n              columnNumber: 19\n            }, this), \" Upload PDF Material (Admin Only)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2083,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2082,\n          columnNumber: 15\n        }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem',\n            background: '#F0F9FF',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            border: '1px solid #BAE6FD'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontSize: '0.875rem',\n              color: '#0284C7'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Need study materials?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2114,\n              columnNumber: 19\n            }, this), \" Use the \\\"Suggest Materials\\\" button to request PDFs from admin.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2113,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2106,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 600,\n              marginBottom: '1rem',\n              color: '#7C3AED',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FiBookOpen, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2130,\n              columnNumber: 17\n            }, this), \" Global Study Materials (Admin Provided)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2121,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '0.75rem'\n            },\n            children: globalMaterials.map((material, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                padding: '1rem',\n                background: '#F8FAFC',\n                borderRadius: '0.5rem',\n                border: '1px solid #E2E8F0',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '1rem',\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: '#7C3AED',\n                    color: 'white',\n                    padding: '0.75rem',\n                    borderRadius: '0.5rem',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FiFileText, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2154,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2145,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: 600,\n                      fontSize: '1rem',\n                      marginBottom: '0.25rem'\n                    },\n                    children: material.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2157,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.875rem',\n                      color: '#6B7280',\n                      marginBottom: '0.25rem'\n                    },\n                    children: material.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2160,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.75rem',\n                      color: '#9CA3AF'\n                    },\n                    children: [material.size, \" \\u2022 \", material.category, \" \\u2022 \", material.downloads, \" downloads \\u2022 Uploaded: \", material.uploadDate]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2163,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2156,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2144,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '0.5rem',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleGlobalDownload(material),\n                  style: {\n                    background: '#059669',\n                    color: 'white',\n                    border: 'none',\n                    padding: '0.75rem',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => e.target.style.background = '#047857',\n                  onMouseLeave: e => e.target.style.background = '#059669',\n                  title: \"Download PDF\",\n                  children: /*#__PURE__*/_jsxDEV(FiDownload, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2187,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2169,\n                  columnNumber: 23\n                }, this), isAdmin && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDeleteGlobalMaterial(material.id),\n                  style: {\n                    background: '#EF4444',\n                    color: 'white',\n                    border: 'none',\n                    padding: '0.75rem',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => e.target.style.background = '#DC2626',\n                  onMouseLeave: e => e.target.style.background = '#EF4444',\n                  title: \"Delete PDF (Admin Only)\",\n                  children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2208,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2190,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2168,\n                columnNumber: 21\n              }, this)]\n            }, material.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2134,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2132,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 600,\n              marginBottom: '1rem',\n              color: '#DC2626',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FiBook, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2228,\n              columnNumber: 17\n            }, this), \" Course Materials\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2219,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1rem'\n            },\n            children: courses.map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                padding: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: '1.1rem',\n                  fontWeight: 600,\n                  marginBottom: '0.5rem',\n                  color: course.color\n                },\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2237,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '0.5rem'\n                },\n                children: course.materials && course.materials.length > 0 ? course.materials.map((material, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '0.75rem',\n                    background: '#F9FAFB',\n                    borderRadius: '0.5rem',\n                    border: '1px solid #E5E7EB'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.75rem',\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#DC2626',\n                        color: 'white',\n                        padding: '0.5rem',\n                        borderRadius: '0.25rem',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FiFileText, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2261,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2252,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontWeight: 500,\n                          fontSize: '0.9rem'\n                        },\n                        children: typeof material === 'string' ? material : material.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2264,\n                        columnNumber: 29\n                      }, this), typeof material === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '0.75rem',\n                          color: '#6B7280'\n                        },\n                        children: [material.size, \" \\u2022 Uploaded on \", material.uploadDate]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2268,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2263,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2251,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => typeof material === 'object' ? handleDownloadFile(material) : alert('Download not available'),\n                      style: {\n                        background: '#059669',\n                        color: 'white',\n                        border: 'none',\n                        padding: '0.5rem',\n                        borderRadius: '0.25rem',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      title: \"Download PDF\",\n                      children: /*#__PURE__*/_jsxDEV(FiDownload, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2290,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2275,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDeleteFile(course.id, idx),\n                      style: {\n                        background: '#EF4444',\n                        color: 'white',\n                        border: 'none',\n                        padding: '0.5rem',\n                        borderRadius: '0.25rem',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      },\n                      title: \"Delete PDF\",\n                      children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2307,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2292,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2274,\n                    columnNumber: 25\n                  }, this)]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2242,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center',\n                    padding: '2rem',\n                    color: '#6B7280',\n                    background: '#F9FAFB',\n                    borderRadius: '0.5rem',\n                    border: '2px dashed #D1D5DB'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiFileText, {\n                    size: 48,\n                    style: {\n                      margin: '0 auto 1rem',\n                      opacity: 0.5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2320,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: 0,\n                      fontStyle: 'italic'\n                    },\n                    children: \"No PDF materials uploaded yet\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2321,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0.5rem 0 0',\n                      fontSize: '0.875rem'\n                    },\n                    children: \"Click \\\"Upload PDF Material\\\" to add study materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2322,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2312,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2240,\n                columnNumber: 19\n              }, this)]\n            }, course.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2232,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2230,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2218,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2054,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2042,\n      columnNumber: 9\n    }, this), showAssignmentsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '700px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Assignments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAssignmentsModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2373,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2363,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2359,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: '#7C3AED',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2389,\n              columnNumber: 17\n            }, this), \" Create Assignment\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2378,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: courses.map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              padding: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                marginBottom: '0.5rem',\n                color: course.color\n              },\n              children: course.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2400,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.5rem'\n              },\n              children: course.assignments && course.assignments.length > 0 ? course.assignments.map((assignment, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  padding: '0.75rem',\n                  background: '#F9FAFB',\n                  borderRadius: '0.25rem',\n                  border: '1px solid #E5E7EB'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiClipboard, {\n                    size: 16,\n                    color: \"#7C3AED\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2415,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: assignment\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2416,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2414,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      background: '#7C3AED',\n                      color: 'white',\n                      padding: '0.25rem 0.5rem',\n                      borderRadius: '0.25rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.75rem'\n                    },\n                    children: \"View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2419,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      background: '#059669',\n                      color: 'white',\n                      padding: '0.25rem 0.5rem',\n                      borderRadius: '0.25rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.75rem'\n                    },\n                    children: \"Submit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2430,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2418,\n                  columnNumber: 25\n                }, this)]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2405,\n                columnNumber: 23\n              }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6B7280',\n                  fontStyle: 'italic'\n                },\n                children: \"No assignments yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2444,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2403,\n              columnNumber: 19\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2395,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2393,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2350,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2338,\n      columnNumber: 9\n    }, this), showGradesModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '600px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Grades Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2478,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowGradesModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2491,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2481,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2477,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: courses.map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              padding: '1rem',\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              background: course.bgColor\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: '1.1rem',\n                  fontWeight: 600,\n                  margin: 0,\n                  color: course.color\n                },\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2507,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6B7280',\n                  margin: 0\n                },\n                children: [\"Progress: \", course.progress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2510,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2506,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'right'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '1.5rem',\n                  fontWeight: 'bold',\n                  color: course.color\n                },\n                children: course.grade || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2515,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6B7280'\n                },\n                children: \"Current Grade\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2522,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2514,\n              columnNumber: 19\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2497,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2495,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1.5rem',\n            padding: '1rem',\n            background: '#F9FAFB',\n            borderRadius: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              marginBottom: '0.5rem'\n            },\n            children: \"Overall GPA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2531,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold',\n              color: '#DC2626'\n            },\n            children: \"3.7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2532,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6B7280',\n              margin: 0\n            },\n            children: \"Based on completed courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2533,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2530,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2468,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2456,\n      columnNumber: 9\n    }, this), showScheduleModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '800px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Academic Schedule\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2565,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowScheduleModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2578,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2568,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2564,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: '#6B7280',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2594,\n              columnNumber: 17\n            }, this), \" Add Event\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2583,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2582,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(7, 1fr)',\n            gap: '1px',\n            background: '#E5E7EB',\n            borderRadius: '0.5rem',\n            overflow: 'hidden'\n          },\n          children: [['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#F9FAFB',\n              padding: '0.75rem',\n              textAlign: 'center',\n              fontWeight: 600,\n              fontSize: '0.875rem'\n            },\n            children: day\n          }, day, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2600,\n            columnNumber: 17\n          }, this)), Array.from({\n            length: 35\n          }, (_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              padding: '0.75rem',\n              minHeight: '60px',\n              fontSize: '0.875rem',\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6B7280'\n              },\n              children: i % 31 + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2619,\n              columnNumber: 19\n            }, this), i === 14 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#DC2626',\n                color: 'white',\n                fontSize: '0.75rem',\n                padding: '0.25rem',\n                borderRadius: '0.25rem',\n                marginTop: '0.25rem'\n              },\n              children: \"Midterm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2621,\n              columnNumber: 21\n            }, this), i === 21 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#7C3AED',\n                color: 'white',\n                fontSize: '0.75rem',\n                padding: '0.25rem',\n                borderRadius: '0.25rem',\n                marginTop: '0.25rem'\n              },\n              children: \"Quiz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2633,\n              columnNumber: 21\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2612,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2598,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              marginBottom: '1rem'\n            },\n            children: \"Upcoming Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2649,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '0.5rem'\n            },\n            children: exams.slice(0, 3).map(exam => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                padding: '0.75rem',\n                background: exam.bgColor,\n                borderRadius: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(exam.icon, {\n                  size: 16,\n                  color: exam.color\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2661,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 500\n                  },\n                  children: exam.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2662,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2660,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: exam.color,\n                  fontWeight: 500\n                },\n                children: exam.timeLeft\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2664,\n                columnNumber: 21\n              }, this)]\n            }, exam.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2652,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2650,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2648,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2555,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2543,\n      columnNumber: 9\n    }, this), showUploadModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '500px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: adminUploadType === 'global' ? '#7C3AED' : '#DC2626',\n              margin: 0\n            },\n            children: adminUploadType === 'global' ? 'Upload Global PDF (Admin)' : 'Upload PDF Material'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2697,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowUploadModal(false);\n              setSelectedFile(null);\n              setUploadCourseId('');\n              setUploadProgress(0);\n            },\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2715,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2700,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2696,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [adminUploadType !== 'global' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Select Course *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2722,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: uploadCourseId,\n              onChange: e => setUploadCourseId(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Choose a course...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2736,\n                columnNumber: 21\n              }, this), courses.map(course => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: course.id,\n                children: course.title\n              }, course.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2738,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2725,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2721,\n            columnNumber: 17\n          }, this), adminUploadType === 'global' && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#F0F9FF',\n              border: '1px solid #BAE6FD',\n              borderRadius: '0.5rem',\n              padding: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiBookOpen, {\n                size: 20,\n                color: \"#0284C7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2754,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: 600,\n                  color: '#0284C7'\n                },\n                children: \"Global Upload Mode\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2755,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2753,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                fontSize: '0.875rem',\n                color: '#6B7280'\n              },\n              children: \"This PDF will be available to all users in the Global Study Materials section.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2757,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2747,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Select PDF File *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2764,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                border: '2px dashed #D1D5DB',\n                borderRadius: '0.5rem',\n                padding: '2rem',\n                textAlign: 'center',\n                background: selectedFile ? '#F0FDF4' : '#F9FAFB',\n                borderColor: selectedFile ? '#10B981' : '#D1D5DB'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".pdf\",\n                onChange: handleFileSelect,\n                style: {\n                  display: 'none'\n                },\n                id: \"pdf-upload\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2775,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"pdf-upload\",\n                style: {\n                  cursor: 'pointer',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                  size: 32,\n                  color: selectedFile ? '#10B981' : '#6B7280'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2792,\n                  columnNumber: 21\n                }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: 500,\n                      color: '#10B981'\n                    },\n                    children: selectedFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2795,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.875rem',\n                      color: '#6B7280'\n                    },\n                    children: [(selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2798,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2794,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: 500,\n                      color: '#6B7280'\n                    },\n                    children: \"Click to select PDF file\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2804,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.875rem',\n                      color: '#9CA3AF'\n                    },\n                    children: \"Maximum file size: 10MB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2807,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2803,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2782,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2767,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2763,\n            columnNumber: 15\n          }, this), isUploading && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: 500\n                },\n                children: \"Uploading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2819,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6B7280'\n                },\n                children: [uploadProgress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2820,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2818,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '0.5rem',\n                background: '#E5E7EB',\n                borderRadius: '0.25rem',\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: `${uploadProgress}%`,\n                  height: '100%',\n                  background: '#DC2626',\n                  transition: 'width 0.3s ease'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2829,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2822,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2817,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2719,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginTop: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowUploadModal(false);\n              setSelectedFile(null);\n              setUploadCourseId('');\n              setUploadProgress(0);\n            },\n            disabled: isUploading,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: 'white',\n              color: '#6B7280',\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              cursor: isUploading ? 'not-allowed' : 'pointer',\n              opacity: isUploading ? 0.5 : 1\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2841,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: adminUploadType === 'global' ? handleAdminUpload : handleFileUpload,\n            disabled: !selectedFile || adminUploadType !== 'global' && !uploadCourseId || isUploading,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: !selectedFile || adminUploadType !== 'global' && !uploadCourseId || isUploading ? '#D1D5DB' : adminUploadType === 'global' ? '#7C3AED' : '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '0.5rem',\n              cursor: !selectedFile || adminUploadType !== 'global' && !uploadCourseId || isUploading ? 'not-allowed' : 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              gap: '0.5rem'\n            },\n            children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(FiLoader, {\n                className: \"animate-spin\",\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2881,\n                columnNumber: 21\n              }, this), \"Uploading...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2886,\n                columnNumber: 21\n              }, this), \"Upload PDF\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2862,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2840,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2689,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2677,\n      columnNumber: 9\n    }, this), showAdminPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '800px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#7C3AED',\n              margin: 0\n            },\n            children: \"Admin Panel - Content Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2920,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAdminPanel(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2933,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2923,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2919,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#F8FAFC',\n              padding: '1.5rem',\n              borderRadius: '0.75rem',\n              border: '1px solid #E2E8F0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.2rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#7C3AED'\n              },\n              children: \"Upload Content for Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2945,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '1rem',\n                marginBottom: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setAdminUploadType('global');\n                  setShowUploadModal(true);\n                },\n                style: {\n                  background: '#7C3AED',\n                  color: 'white',\n                  padding: '0.75rem 1.5rem',\n                  borderRadius: '0.5rem',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => e.target.style.background = '#6D28D9',\n                onMouseLeave: e => e.target.style.background = '#7C3AED',\n                children: [/*#__PURE__*/_jsxDEV(FiUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2970,\n                  columnNumber: 21\n                }, this), \" Upload Global PDF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2950,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setAdminUploadType('course');\n                  setShowUploadModal(true);\n                },\n                style: {\n                  background: '#DC2626',\n                  color: 'white',\n                  padding: '0.75rem 1.5rem',\n                  borderRadius: '0.5rem',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => e.target.style.background = '#B91C1C',\n                onMouseLeave: e => e.target.style.background = '#DC2626',\n                children: [/*#__PURE__*/_jsxDEV(FiUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2993,\n                  columnNumber: 21\n                }, this), \" Upload to Course\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2973,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2949,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6B7280'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Global PDF:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2999,\n                  columnNumber: 21\n                }, this), \" Available to all users in Study Materials section\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2998,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Course PDF:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3002,\n                  columnNumber: 21\n                }, this), \" Available only to users enrolled in specific course\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3001,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2997,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2939,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#F0F9FF',\n              padding: '1.5rem',\n              borderRadius: '0.75rem',\n              border: '1px solid #BAE6FD'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.2rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#0284C7'\n              },\n              children: \"Add Material from Local Folder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3014,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6B7280',\n                  margin: '0 0 1rem'\n                },\n                children: \"Add PDFs that you've already placed in the project folders:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3019,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                  gap: '0.75rem'\n                },\n                children: [{\n                  category: 'Mathematics',\n                  folder: 'mathematics',\n                  color: '#DC2626'\n                }, {\n                  category: 'Physics',\n                  folder: 'physics',\n                  color: '#7C3AED'\n                }, {\n                  category: 'Computer Science',\n                  folder: 'computer-science',\n                  color: '#059669'\n                }, {\n                  category: 'General',\n                  folder: 'general',\n                  color: '#EA580C'\n                }].map(cat => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    const fileName = prompt(`Enter the PDF filename (with .pdf extension) that you've placed in public/study-materials/${cat.folder}/ folder:`);\n                    if (fileName && fileName.endsWith('.pdf')) {\n                      const description = prompt('Enter a description for this material (optional):') || '';\n                      addMaterialFromFolder(fileName, cat.category, description);\n                      alert(`Material \"${fileName}\" added successfully!`);\n                    } else if (fileName) {\n                      alert('Please enter a valid PDF filename with .pdf extension');\n                    }\n                  },\n                  style: {\n                    background: cat.color,\n                    color: 'white',\n                    padding: '0.75rem',\n                    borderRadius: '0.5rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '0.875rem',\n                    fontWeight: 500,\n                    transition: 'all 0.3s ease'\n                  },\n                  onMouseEnter: e => e.target.style.opacity = '0.8',\n                  onMouseLeave: e => e.target.style.opacity = '1',\n                  children: [\"Add \", cat.category, \" PDF\"]\n                }, cat.category, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3030,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3023,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3018,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#FEF3C7',\n                padding: '1rem',\n                borderRadius: '0.5rem',\n                border: '1px solid #FDE68A'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#92400E',\n                  margin: 0\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Instructions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3069,\n                  columnNumber: 21\n                }, this), \" First place your PDF files in the corresponding folders:\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3070,\n                  columnNumber: 21\n                }, this), \"\\u2022 Mathematics: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"public/study-materials/mathematics/\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3070,\n                  columnNumber: 42\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3071,\n                  columnNumber: 21\n                }, this), \"\\u2022 Physics: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"public/study-materials/physics/\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3071,\n                  columnNumber: 38\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3072,\n                  columnNumber: 21\n                }, this), \"\\u2022 Computer Science: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"public/study-materials/computer-science/\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3072,\n                  columnNumber: 47\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3073,\n                  columnNumber: 21\n                }, this), \"\\u2022 General: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"public/study-materials/general/\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3073,\n                  columnNumber: 38\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3068,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3062,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3008,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#F0FDF4',\n              padding: '1.5rem',\n              borderRadius: '0.75rem',\n              border: '1px solid #BBF7D0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.2rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#059669'\n              },\n              children: \"Global Materials Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3085,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.75rem'\n              },\n              children: globalMaterials.map(material => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  padding: '1rem',\n                  background: 'white',\n                  borderRadius: '0.5rem',\n                  border: '1px solid #D1FAE5'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: 600,\n                      fontSize: '1rem',\n                      marginBottom: '0.25rem'\n                    },\n                    children: material.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3101,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.875rem',\n                      color: '#6B7280',\n                      marginBottom: '0.25rem'\n                    },\n                    children: material.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3104,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.75rem',\n                      color: '#9CA3AF'\n                    },\n                    children: [material.size, \" \\u2022 \", material.downloads, \" downloads \\u2022 \", material.uploadDate]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3107,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3100,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleGlobalDownload(material),\n                    style: {\n                      background: '#059669',\n                      color: 'white',\n                      border: 'none',\n                      padding: '0.5rem',\n                      borderRadius: '0.25rem',\n                      cursor: 'pointer'\n                    },\n                    title: \"Preview/Download\",\n                    children: /*#__PURE__*/_jsxDEV(FiDownload, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 3124,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3112,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteGlobalMaterial(material.id),\n                    style: {\n                      background: '#EF4444',\n                      color: 'white',\n                      border: 'none',\n                      padding: '0.5rem',\n                      borderRadius: '0.25rem',\n                      cursor: 'pointer'\n                    },\n                    title: \"Delete Material\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 3138,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3126,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3111,\n                  columnNumber: 23\n                }, this)]\n              }, material.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3091,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3089,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3079,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#FEF3C7',\n              padding: '1.5rem',\n              borderRadius: '0.75rem',\n              border: '1px solid #FDE68A'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.2rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#D97706'\n              },\n              children: \"Content Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '2rem',\n                    fontWeight: 'bold',\n                    color: '#7C3AED'\n                  },\n                  children: globalMaterials.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.875rem',\n                    color: '#6B7280'\n                  },\n                  children: \"Global Materials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3162,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '2rem',\n                    fontWeight: 'bold',\n                    color: '#DC2626'\n                  },\n                  children: globalMaterials.reduce((sum, mat) => sum + mat.downloads, 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3166,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.875rem',\n                    color: '#6B7280'\n                  },\n                  children: \"Total Downloads\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3169,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3165,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '2rem',\n                    fontWeight: 'bold',\n                    color: '#059669'\n                  },\n                  children: courses.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.875rem',\n                    color: '#6B7280'\n                  },\n                  children: \"Active Courses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3176,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '2rem',\n                    fontWeight: 'bold',\n                    color: '#EA580C'\n                  },\n                  children: courses.reduce((sum, course) => {\n                    var _course$materials;\n                    return sum + (((_course$materials = course.materials) === null || _course$materials === void 0 ? void 0 : _course$materials.length) || 0);\n                  }, 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.875rem',\n                    color: '#6B7280'\n                  },\n                  children: \"Course Materials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3183,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3179,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3157,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3147,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2937,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2910,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2898,\n      columnNumber: 9\n    }, this), showAdminLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#6B7280',\n              margin: 0\n            },\n            children: \"Admin Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowAdminLogin(false);\n              setAdminPassword('');\n            },\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3230,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Admin Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3236,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              value: adminPassword,\n              onChange: e => setAdminPassword(e.target.value),\n              placeholder: \"Enter admin password\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              },\n              onKeyPress: e => e.key === 'Enter' && handleAdminLogin()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3239,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3235,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowAdminLogin(false);\n                setAdminPassword('');\n              },\n              style: {\n                flex: 1,\n                padding: '0.75rem',\n                background: '#E5E7EB',\n                color: '#6B7280',\n                border: 'none',\n                borderRadius: '0.5rem',\n                cursor: 'pointer'\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3256,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAdminLogin,\n              style: {\n                flex: 1,\n                padding: '0.75rem',\n                background: '#6B7280',\n                color: 'white',\n                border: 'none',\n                borderRadius: '0.5rem',\n                cursor: 'pointer'\n              },\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3273,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#FEF3C7',\n              padding: '1rem',\n              borderRadius: '0.5rem',\n              border: '1px solid #FDE68A'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#92400E',\n                margin: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Demo Password:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3296,\n                columnNumber: 19\n              }, this), \" admin123\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3295,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3234,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3206,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3194,\n      columnNumber: 9\n    }, this), showSuggestionModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '600px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#059669',\n              margin: 0\n            },\n            children: \"Suggest Study Materials\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSuggestionModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3341,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3331,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3327,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: 500\n                },\n                children: \"Your Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newSuggestion.userName,\n                onChange: e => setNewSuggestion(prev => ({\n                  ...prev,\n                  userName: e.target.value\n                })),\n                placeholder: \"Enter your name\",\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  fontSize: '1rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3347,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: 500\n                },\n                children: \"Email *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                value: newSuggestion.email,\n                onChange: e => setNewSuggestion(prev => ({\n                  ...prev,\n                  email: e.target.value\n                })),\n                placeholder: \"Enter your email\",\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  fontSize: '1rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3370,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3366,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr 1fr',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: 500\n                },\n                children: \"Course/Subject\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newSuggestion.course,\n                onChange: e => setNewSuggestion(prev => ({\n                  ...prev,\n                  course: e.target.value\n                })),\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  fontSize: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select course...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3402,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Mathematics\",\n                  children: \"Mathematics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3403,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Physics\",\n                  children: \"Physics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Computer Science\",\n                  children: \"Computer Science\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3405,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Chemistry\",\n                  children: \"Chemistry\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Biology\",\n                  children: \"Biology\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3407,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"General\",\n                  children: \"General\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3408,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3391,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: 500\n                },\n                children: \"Material Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3413,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newSuggestion.materialType,\n                onChange: e => setNewSuggestion(prev => ({\n                  ...prev,\n                  materialType: e.target.value\n                })),\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  fontSize: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select type...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3427,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Textbook\",\n                  children: \"Textbook\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3428,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Reference Book\",\n                  children: \"Reference Book\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3429,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Notes\",\n                  children: \"Notes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3430,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Question Bank\",\n                  children: \"Question Bank\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3431,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Solution Manual\",\n                  children: \"Solution Manual\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3432,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Practice Papers\",\n                  children: \"Practice Papers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3433,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3416,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  marginBottom: '0.5rem',\n                  fontWeight: 500\n                },\n                children: \"Priority\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3438,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newSuggestion.priority,\n                onChange: e => setNewSuggestion(prev => ({\n                  ...prev,\n                  priority: e.target.value\n                })),\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  fontSize: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Low\",\n                  children: \"Low\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3452,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Medium\",\n                  children: \"Medium\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3453,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"High\",\n                  children: \"High\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3454,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Urgent\",\n                  children: \"Urgent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3455,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3441,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Material Title/Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newSuggestion.title,\n              onChange: e => setNewSuggestion(prev => ({\n                ...prev,\n                title: e.target.value\n              })),\n              placeholder: \"e.g., Advanced Calculus by James Stewart\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3464,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Description/Reason\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newSuggestion.description,\n              onChange: e => setNewSuggestion(prev => ({\n                ...prev,\n                description: e.target.value\n              })),\n              placeholder: \"Why do you need this material? How will it help your studies?\",\n              rows: 3,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem',\n                resize: 'vertical'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3483,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3479,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSuggestionModal(false),\n              style: {\n                flex: 1,\n                padding: '0.75rem',\n                background: '#E5E7EB',\n                color: '#6B7280',\n                border: 'none',\n                borderRadius: '0.5rem',\n                cursor: 'pointer'\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3500,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmitSuggestion,\n              style: {\n                flex: 1,\n                padding: '0.75rem',\n                background: '#059669',\n                color: 'white',\n                border: 'none',\n                borderRadius: '0.5rem',\n                cursor: 'pointer'\n              },\n              children: \"Submit Suggestion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3514,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3499,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3345,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3318,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3306,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes float {\n            0% { transform: translateY(0px); }\n            50% { transform: translateY(-10px); }\n            100% { transform: translateY(0px); }\n          }\n\n          .animate-spin {\n            animation: spin 1s linear infinite;\n          }\n\n          @keyframes spin {\n            from { transform: rotate(0deg); }\n            to { transform: rotate(360deg); }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3535,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 684,\n    columnNumber: 5\n  }, this);\n};\n_s(AcademicDashboard, \"LDYHV7I41KKpqIXtjjZb1z3egdo=\");\n_c = AcademicDashboard;\nexport default AcademicDashboard;\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiBook", "FiCheckCircle", "<PERSON><PERSON><PERSON><PERSON>", "FiAlertCircle", "FiPlus", "FiSettings", "FiStar", "FiUser", "FiCalendar", "FiFileText", "FiBookOpen", "FiClipboard", "FiTrendingUp", "FiMaximize2", "FiSave", "FiCpu", "FiX", "FiSend", "<PERSON><PERSON><PERSON><PERSON>", "FiZap", "FiDownload", "FiTrash2", "FiUpload", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AcademicDashboard", "_s", "chatbotOpen", "setChatbotOpen", "chatMessages", "setChatMessages", "text", "isUser", "chatInput", "setChatInput", "showAddCourseModal", "setShowAddCourseModal", "showAddExamModal", "setShowAddExamModal", "showCourseDetailsModal", "setShowCourseDetailsModal", "showSettingsModal", "setShowSettingsModal", "showStudyMaterialsModal", "setShowStudyMaterialsModal", "showAssignmentsModal", "setShowAssignmentsModal", "showGradesModal", "setShowGradesModal", "showScheduleModal", "setShowScheduleModal", "selectedCourse", "setSelectedCourse", "courseFilter", "setCourseFilter", "courses", "setCourses", "id", "subject", "title", "description", "professor", "duration", "rating", "color", "bgColor", "status", "progress", "materials", "assignments", "exams", "setExams", "date", "timeLeft", "icon", "courseId", "newCourse", "setNewCourse", "newExam", "setNewExam", "showUploadModal", "setShowUploadModal", "selectedFile", "setSelectedFile", "uploadCourseId", "setUploadCourseId", "uploadProgress", "setUploadProgress", "isUploading", "setIsUploading", "isAdmin", "setIsAdmin", "showAdminPanel", "setShowAdminPanel", "adminUploadType", "setAdminUploadType", "showAdminLogin", "setShowAdminLogin", "adminPassword", "setAdminPassword", "showSuggestionModal", "setShowSuggestionModal", "suggestions", "setSuggestions", "userName", "email", "course", "materialType", "priority", "newSuggestion", "setNewSuggestion", "globalMaterials", "setGlobalMaterials", "name", "type", "size", "uploadDate", "category", "downloads", "url", "fileName", "folderPath", "timer", "setTimeout", "nodes", "document", "querySelectorAll", "for<PERSON>ach", "node", "index", "style", "opacity", "transition", "clearTimeout", "handleChatSend", "trim", "prev", "responses", "randomResponse", "Math", "floor", "random", "length", "handleKeyPress", "e", "key", "handleAddCourse", "Date", "now", "grade", "alert", "handleAddExam", "exam", "calculateTimeLeft", "examDate", "today", "diffTime", "diffDays", "ceil", "handleCourseClick", "handleFilterChange", "filter", "getFilteredCourses", "calculateStats", "total", "completed", "c", "active", "pending", "stats", "handleFileSelect", "event", "file", "target", "files", "handleFileUpload", "uploadInterval", "setInterval", "clearInterval", "fileURL", "URL", "createObjectURL", "map", "toString", "toFixed", "toLocaleDateString", "error", "console", "handleDownloadFile", "material", "link", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleDeleteFile", "materialIndex", "window", "confirm", "newMaterials", "splice", "handleAdminUpload", "newMaterial", "isAdminUploaded", "handleGlobalDownload", "mat", "response", "fetch", "ok", "handleDeleteGlobalMaterial", "materialId", "addMaterialFromFolder", "folderMap", "handleAdminLogin", "correctPassword", "handleAdminLogout", "handleSubmitSuggestion", "suggestion", "handleUpdateSuggestionStatus", "suggestionId", "newStatus", "minHeight", "background", "padding", "children", "max<PERSON><PERSON><PERSON>", "margin", "display", "justifyContent", "alignItems", "marginBottom", "flexWrap", "gap", "fontSize", "fontWeight", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "borderRadius", "border", "cursor", "onMouseEnter", "onMouseLeave", "gridTemplateColumns", "label", "value", "stat", "boxShadow", "currentTarget", "transform", "marginRight", "flexDirection", "overflow", "borderColor", "width", "height", "marginTop", "flex", "position", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeLinecap", "transform<PERSON><PERSON>in", "inset", "className", "top", "left", "action", "bottom", "right", "zIndex", "animation", "overflowY", "message", "borderTop", "onChange", "onKeyPress", "placeholder", "outline", "marginLeft", "maxHeight", "rows", "resize", "textAlign", "idx", "fontStyle", "assignment", "day", "Array", "from", "_", "i", "slice", "accept", "htmlFor", "disabled", "folder", "cat", "prompt", "endsWith", "reduce", "sum", "_course$materials", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich/src/AcademicDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>Book, FiCheckCircle, FiLoader, FiAlertCircle, FiPlus, FiSettings, FiStar, FiUser, FiCalendar, FiFileText, FiBookOpen, FiClipboard, FiTrendingUp, FiMaximize2, FiSave, FiCpu, FiX, FiSend, FiTarget, FiZap, FiDownload, FiTrash2, FiUpload } from 'react-icons/fi';\n\nconst AcademicDashboard = () => {\n  // Chatbot states\n  const [chatbotOpen, setChatbotOpen] = useState(false);\n  const [chatMessages, setChatMessages] = useState([\n    { text: \"Hello! I'm your EduAI assistant. How can I help you with your academics today?\", isUser: false }\n  ]);\n  const [chatInput, setChatInput] = useState('');\n\n  // Modal states\n  const [showAddCourseModal, setShowAddCourseModal] = useState(false);\n  const [showAddExamModal, setShowAddExamModal] = useState(false);\n  const [showCourseDetailsModal, setShowCourseDetailsModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [showStudyMaterialsModal, setShowStudyMaterialsModal] = useState(false);\n  const [showAssignmentsModal, setShowAssignmentsModal] = useState(false);\n  const [showGradesModal, setShowGradesModal] = useState(false);\n  const [showScheduleModal, setShowScheduleModal] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n\n  // Filter states\n  const [courseFilter, setCourseFilter] = useState('All');\n\n  // Data states\n  const [courses, setCourses] = useState([\n    {\n      id: 1,\n      subject: 'Computer Science',\n      title: 'Data Structures & Algorithms',\n      description: 'Master fundamental algorithms and problem-solving techniques',\n      professor: 'Prof. Smith',\n      duration: '12 Weeks',\n      rating: 4.8,\n      color: '#DC2626',\n      bgColor: '#FEE2E2',\n      status: 'Active',\n      progress: 82,\n      materials: ['Textbook Chapter 1-5', 'Video Lectures', 'Practice Problems'],\n      assignments: ['Assignment 1: Arrays', 'Assignment 2: Linked Lists']\n    },\n    {\n      id: 2,\n      subject: 'Mathematics',\n      title: 'Linear Algebra',\n      description: 'Vectors, matrices, and their applications in computer science',\n      professor: 'Prof. Johnson',\n      duration: '8 Weeks',\n      rating: 4.5,\n      color: '#7C3AED',\n      bgColor: '#EDE9FE',\n      status: 'Active',\n      progress: 90,\n      materials: ['Linear Algebra Textbook', 'Khan Academy Videos'],\n      assignments: ['Matrix Operations Quiz', 'Eigenvalues Problem Set']\n    },\n    {\n      id: 3,\n      subject: 'Physics',\n      title: 'Quantum Mechanics',\n      description: 'Introduction to quantum theory and its applications',\n      professor: 'Prof. Williams',\n      duration: '10 Weeks',\n      rating: 4.2,\n      color: '#EA580C',\n      bgColor: '#FED7AA',\n      status: 'Active',\n      progress: 65,\n      materials: ['Quantum Physics Textbook', 'Lab Manual'],\n      assignments: ['Wave Function Analysis', 'Quantum States Problem']\n    },\n    {\n      id: 4,\n      subject: 'Literature',\n      title: 'Modern Poetry',\n      description: 'Analysis of 20th century poetry and poetic techniques',\n      professor: 'Prof. Brown',\n      duration: '6 Weeks',\n      rating: 4.7,\n      color: '#6B7280',\n      bgColor: '#F3F4F6',\n      status: 'Completed',\n      progress: 100,\n      materials: ['Poetry Anthology', 'Critical Essays'],\n      assignments: ['Poetry Analysis Essay', 'Creative Writing Assignment']\n    }\n  ]);\n\n  const [exams, setExams] = useState([\n    {\n      id: 1,\n      title: 'Midterm Exam - Data Structures',\n      description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n      date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n      timeLeft: '3 Days Left',\n      icon: FiFileText,\n      color: '#DC2626',\n      bgColor: '#FEE2E2',\n      courseId: 1\n    },\n    {\n      id: 2,\n      title: 'Quiz 2 - Linear Algebra',\n      description: 'Coverage: Matrix Operations, Determinants',\n      date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n      timeLeft: '1 Week Left',\n      icon: FiTarget,\n      color: '#7C3AED',\n      bgColor: '#EDE9FE',\n      courseId: 2\n    },\n    {\n      id: 3,\n      title: 'Final Exam - Quantum Mechanics',\n      description: 'Coverage: Entire Semester',\n      date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n      timeLeft: '2 Weeks Left',\n      icon: FiZap,\n      color: '#EA580C',\n      bgColor: '#FED7AA',\n      courseId: 3\n    }\n  ]);\n\n  // Form states\n  const [newCourse, setNewCourse] = useState({\n    subject: '',\n    title: '',\n    description: '',\n    professor: '',\n    duration: '',\n    color: '#DC2626'\n  });\n\n  const [newExam, setNewExam] = useState({\n    title: '',\n    description: '',\n    date: '',\n    courseId: ''\n  });\n\n  // File upload states\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [uploadCourseId, setUploadCourseId] = useState('');\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Admin states\n  const [isAdmin, setIsAdmin] = useState(false); // Set to false by default - only admins can access\n  const [showAdminPanel, setShowAdminPanel] = useState(false);\n  const [adminUploadType, setAdminUploadType] = useState('course'); // 'course' or 'global'\n  const [showAdminLogin, setShowAdminLogin] = useState(false);\n  const [adminPassword, setAdminPassword] = useState('');\n\n  // User suggestion states\n  const [showSuggestionModal, setShowSuggestionModal] = useState(false);\n  const [suggestions, setSuggestions] = useState([\n    {\n      id: 1,\n      userName: 'Rahul Kumar',\n      email: '<EMAIL>',\n      course: 'Mathematics',\n      materialType: 'Textbook',\n      title: 'Advanced Calculus by James Stewart',\n      description: 'Comprehensive calculus textbook with detailed examples and exercises',\n      priority: 'High',\n      status: 'Pending',\n      date: '2023-11-15'\n    },\n    {\n      id: 2,\n      userName: 'Priya Sharma',\n      email: '<EMAIL>',\n      course: 'Physics',\n      materialType: 'Reference Book',\n      title: 'Concepts of Physics by H.C. Verma',\n      description: 'Essential physics reference for competitive exams',\n      priority: 'Medium',\n      status: 'Approved',\n      date: '2023-11-12'\n    }\n  ]);\n  const [newSuggestion, setNewSuggestion] = useState({\n    userName: '',\n    email: '',\n    course: '',\n    materialType: '',\n    title: '',\n    description: '',\n    priority: 'Medium'\n  });\n  const [globalMaterials, setGlobalMaterials] = useState([\n    {\n      id: 1,\n      name: 'Complete Mathematics Guide.pdf',\n      type: 'pdf',\n      size: '15.2 MB',\n      uploadDate: '2023-11-10',\n      category: 'Mathematics',\n      description: 'Comprehensive mathematics guide for all students',\n      downloads: 245,\n      url: '/study-materials/mathematics/Complete Mathematics Guide.pdf',\n      fileName: 'Complete Mathematics Guide.pdf',\n      folderPath: 'mathematics'\n    },\n    {\n      id: 2,\n      name: 'Physics Formula Sheet.pdf',\n      type: 'pdf',\n      size: '3.8 MB',\n      uploadDate: '2023-11-08',\n      category: 'Physics',\n      description: 'Essential physics formulas and concepts',\n      downloads: 189,\n      url: '/study-materials/physics/Physics Formula Sheet.pdf',\n      fileName: 'Physics Formula Sheet.pdf',\n      folderPath: 'physics'\n    },\n    {\n      id: 3,\n      name: 'Computer Science Fundamentals.pdf',\n      type: 'pdf',\n      size: '22.1 MB',\n      uploadDate: '2023-11-05',\n      category: 'Computer Science',\n      description: 'Core computer science concepts and algorithms',\n      downloads: 312,\n      url: '/study-materials/computer-science/Computer Science Fundamentals.pdf',\n      fileName: 'Computer Science Fundamentals.pdf',\n      folderPath: 'computer-science'\n    }\n  ]);\n\n  // Animation effect for mindmap nodes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      const nodes = document.querySelectorAll('.mindmap-node');\n      nodes.forEach((node, index) => {\n        node.style.opacity = '0';\n        setTimeout(() => {\n          node.style.opacity = '1';\n          node.style.transition = 'opacity 0.5s ease, transform 0.3s ease';\n        }, 200 * index);\n      });\n    }, 100);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const handleChatSend = () => {\n    if (chatInput.trim()) {\n      setChatMessages(prev => [...prev, { text: chatInput, isUser: true }]);\n      setChatInput('');\n\n      // Simulate bot response\n      setTimeout(() => {\n        const responses = [\n          \"I can help you with that. What specific aspect do you need assistance with?\",\n          \"That's an interesting question. Let me check my knowledge base...\",\n          \"For that topic, I recommend reviewing chapter 3 of your textbook.\",\n          \"I'm still learning about that subject, but here's what I know...\",\n          \"Would you like me to find study resources for that topic?\"\n        ];\n        const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n        setChatMessages(prev => [...prev, { text: randomResponse, isUser: false }]);\n      }, 1000);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter') {\n      handleChatSend();\n    }\n  };\n\n  // Functionality handlers\n  const handleAddCourse = () => {\n    if (newCourse.title && newCourse.subject && newCourse.professor) {\n      const course = {\n        id: Date.now(),\n        ...newCourse,\n        rating: 0,\n        bgColor: newCourse.color + '20',\n        status: 'Active',\n        progress: 0,\n        materials: [],\n        assignments: [],\n        grade: 'N/A'\n      };\n      setCourses(prev => [...prev, course]);\n      setNewCourse({\n        subject: '',\n        title: '',\n        description: '',\n        professor: '',\n        duration: '',\n        color: '#DC2626'\n      });\n      setShowAddCourseModal(false);\n      alert('Course added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n\n  const handleAddExam = () => {\n    if (newExam.title && newExam.date && newExam.courseId) {\n      const exam = {\n        id: Date.now(),\n        ...newExam,\n        timeLeft: calculateTimeLeft(newExam.date),\n        icon: FiFileText,\n        color: '#DC2626',\n        bgColor: '#FEE2E2'\n      };\n      setExams(prev => [...prev, exam]);\n      setNewExam({\n        title: '',\n        description: '',\n        date: '',\n        courseId: ''\n      });\n      setShowAddExamModal(false);\n      alert('Exam added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n\n  const calculateTimeLeft = (examDate) => {\n    const today = new Date();\n    const exam = new Date(examDate);\n    const diffTime = exam - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays < 0) return 'Past Due';\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return '1 Day Left';\n    if (diffDays < 7) return `${diffDays} Days Left`;\n    if (diffDays < 14) return '1 Week Left';\n    return `${Math.ceil(diffDays / 7)} Weeks Left`;\n  };\n\n  const handleCourseClick = (course) => {\n    setSelectedCourse(course);\n    setShowCourseDetailsModal(true);\n  };\n\n  const handleFilterChange = (filter) => {\n    setCourseFilter(filter);\n  };\n\n  const getFilteredCourses = () => {\n    if (courseFilter === 'All') return courses;\n    return courses.filter(course => course.status === courseFilter);\n  };\n\n  const calculateStats = () => {\n    const total = courses.length;\n    const completed = courses.filter(c => c.status === 'Completed').length;\n    const active = courses.filter(c => c.status === 'Active').length;\n    const pending = courses.filter(c => c.progress === 0).length;\n\n    return { total, completed, active, pending };\n  };\n\n  const stats = calculateStats();\n\n  // File upload handlers\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      // Check if file is PDF\n      if (file.type !== 'application/pdf') {\n        alert('Please select a PDF file only');\n        return;\n      }\n\n      // Check file size (max 10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        alert('File size should be less than 10MB');\n        return;\n      }\n\n      setSelectedFile(file);\n    }\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile || !uploadCourseId) {\n      alert('Please select a file and course');\n      return;\n    }\n\n    setIsUploading(true);\n    setUploadProgress(0);\n\n    try {\n      // Simulate file upload progress\n      const uploadInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(uploadInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Create file URL for preview (in real app, this would be uploaded to server)\n      const fileURL = URL.createObjectURL(selectedFile);\n\n      // Add file to course materials\n      setTimeout(() => {\n        setCourses(prev => prev.map(course => {\n          if (course.id.toString() === uploadCourseId) {\n            return {\n              ...course,\n              materials: [...(course.materials || []), {\n                name: selectedFile.name,\n                type: 'pdf',\n                size: (selectedFile.size / 1024 / 1024).toFixed(2) + ' MB',\n                uploadDate: new Date().toLocaleDateString(),\n                url: fileURL\n              }]\n            };\n          }\n          return course;\n        }));\n\n        setUploadProgress(100);\n        setTimeout(() => {\n          setIsUploading(false);\n          setShowUploadModal(false);\n          setSelectedFile(null);\n          setUploadCourseId('');\n          setUploadProgress(0);\n          alert('PDF uploaded successfully!');\n        }, 500);\n      }, 2000);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Upload failed. Please try again.');\n      setIsUploading(false);\n    }\n  };\n\n  const handleDownloadFile = (material) => {\n    if (material.url) {\n      // Create download link\n      const link = document.createElement('a');\n      link.href = material.url;\n      link.download = material.name;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } else {\n      alert('File not available for download');\n    }\n  };\n\n  const handleDeleteFile = (courseId, materialIndex) => {\n    if (window.confirm('Are you sure you want to delete this file?')) {\n      setCourses(prev => prev.map(course => {\n        if (course.id === courseId) {\n          const newMaterials = [...course.materials];\n          newMaterials.splice(materialIndex, 1);\n          return { ...course, materials: newMaterials };\n        }\n        return course;\n      }));\n      alert('File deleted successfully!');\n    }\n  };\n\n  // Admin functionality handlers\n  const handleAdminUpload = async () => {\n    if (!selectedFile) {\n      alert('Please select a file');\n      return;\n    }\n\n    setIsUploading(true);\n    setUploadProgress(0);\n\n    try {\n      // Simulate file upload progress\n      const uploadInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(uploadInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Create file URL for preview\n      const fileURL = URL.createObjectURL(selectedFile);\n\n      setTimeout(() => {\n        if (adminUploadType === 'global') {\n          // Add to global materials (available to all users)\n          const newMaterial = {\n            id: Date.now(),\n            name: selectedFile.name,\n            type: 'pdf',\n            size: (selectedFile.size / 1024 / 1024).toFixed(2) + ' MB',\n            uploadDate: new Date().toLocaleDateString(),\n            category: 'General',\n            description: 'Admin uploaded material',\n            downloads: 0,\n            url: fileURL\n          };\n          setGlobalMaterials(prev => [...prev, newMaterial]);\n        } else {\n          // Add to specific course\n          setCourses(prev => prev.map(course => {\n            if (course.id.toString() === uploadCourseId) {\n              return {\n                ...course,\n                materials: [...(course.materials || []), {\n                  name: selectedFile.name,\n                  type: 'pdf',\n                  size: (selectedFile.size / 1024 / 1024).toFixed(2) + ' MB',\n                  uploadDate: new Date().toLocaleDateString(),\n                  url: fileURL,\n                  isAdminUploaded: true\n                }]\n              };\n            }\n            return course;\n          }));\n        }\n\n        setUploadProgress(100);\n        setTimeout(() => {\n          setIsUploading(false);\n          setShowUploadModal(false);\n          setSelectedFile(null);\n          setUploadCourseId('');\n          setUploadProgress(0);\n          alert('PDF uploaded successfully for all users!');\n        }, 500);\n      }, 2000);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Upload failed. Please try again.');\n      setIsUploading(false);\n    }\n  };\n\n  const handleGlobalDownload = async (material) => {\n    try {\n      // Increment download count\n      setGlobalMaterials(prev => prev.map(mat =>\n        mat.id === material.id\n          ? { ...mat, downloads: mat.downloads + 1 }\n          : mat\n      ));\n\n      // Check if file exists and download\n      const response = await fetch(material.url);\n      if (response.ok) {\n        // Create download link\n        const link = document.createElement('a');\n        link.href = material.url;\n        link.download = material.fileName || material.name;\n        link.target = '_blank'; // Open in new tab if direct download fails\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        // Show success message\n        alert(`Download started: ${material.name}`);\n      } else {\n        // File not found, show instructions\n        alert(`File not found! Please add \"${material.fileName}\" to the \"public/study-materials/${material.folderPath}/\" folder in your project directory.`);\n      }\n    } catch (error) {\n      console.error('Download error:', error);\n      alert(`To download this file, please add \"${material.fileName}\" to the \"public/study-materials/${material.folderPath}/\" folder in your project directory.`);\n    }\n  };\n\n  const handleDeleteGlobalMaterial = (materialId) => {\n    if (window.confirm('Are you sure you want to delete this material? It will be removed for all users.')) {\n      setGlobalMaterials(prev => prev.filter(mat => mat.id !== materialId));\n      alert('Material deleted successfully!');\n    }\n  };\n\n  // Function to add material from local folder\n  const addMaterialFromFolder = (fileName, category, description = '') => {\n    const folderMap = {\n      'Mathematics': 'mathematics',\n      'Physics': 'physics',\n      'Computer Science': 'computer-science',\n      'General': 'general'\n    };\n\n    const folderPath = folderMap[category] || 'general';\n    const newMaterial = {\n      id: Date.now(),\n      name: fileName,\n      type: 'pdf',\n      size: 'Unknown', // Size will be determined when file is accessed\n      uploadDate: new Date().toLocaleDateString(),\n      category: category,\n      description: description || `${category} study material`,\n      downloads: 0,\n      url: `/study-materials/${folderPath}/${fileName}`,\n      fileName: fileName,\n      folderPath: folderPath\n    };\n\n    setGlobalMaterials(prev => [...prev, newMaterial]);\n    return newMaterial;\n  };\n\n  // Admin login handler\n  const handleAdminLogin = () => {\n    const correctPassword = 'admin123'; // Change this to your desired password\n    if (adminPassword === correctPassword) {\n      setIsAdmin(true);\n      setShowAdminLogin(false);\n      setAdminPassword('');\n      alert('Admin access granted!');\n    } else {\n      alert('Incorrect password! Please try again.');\n      setAdminPassword('');\n    }\n  };\n\n  // Admin logout handler\n  const handleAdminLogout = () => {\n    setIsAdmin(false);\n    setShowAdminPanel(false);\n    alert('Admin logged out successfully!');\n  };\n\n  // Suggestion handlers\n  const handleSubmitSuggestion = () => {\n    if (!newSuggestion.userName || !newSuggestion.email || !newSuggestion.title) {\n      alert('Please fill in all required fields (Name, Email, Title)');\n      return;\n    }\n\n    const suggestion = {\n      id: Date.now(),\n      ...newSuggestion,\n      status: 'Pending',\n      date: new Date().toLocaleDateString()\n    };\n\n    setSuggestions(prev => [...prev, suggestion]);\n    setNewSuggestion({\n      userName: '',\n      email: '',\n      course: '',\n      materialType: '',\n      title: '',\n      description: '',\n      priority: 'Medium'\n    });\n\n    alert('Suggestion submitted successfully! Admin will review it soon.');\n    setShowSuggestionModal(false);\n  };\n\n  const handleUpdateSuggestionStatus = (suggestionId, newStatus) => {\n    setSuggestions(prev => prev.map(suggestion =>\n      suggestion.id === suggestionId\n        ? { ...suggestion, status: newStatus }\n        : suggestion\n    ));\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n      padding: '2rem 1rem'\n    }}>\n      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n        {/* Header */}\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '2rem',\n          flexWrap: 'wrap',\n          gap: '1rem'\n        }}>\n          <div>\n            <h1 style={{\n              fontSize: '2.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0,\n              marginBottom: '0.5rem'\n            }}>\n              Academic Dashboard\n            </h1>\n            <p style={{ color: '#6B7280', margin: 0 }}>\n              Track your academic progress and resources\n            </p>\n          </div>\n          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n            {isAdmin ? (\n              <>\n                <button\n                  onClick={() => setShowAdminPanel(true)}\n                  style={{\n                    background: '#7C3AED',\n                    color: 'white',\n                    padding: '0.75rem 1.5rem',\n                    borderRadius: '0.5rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    fontSize: '1rem',\n                    transition: 'all 0.3s ease'\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = '#6D28D9'}\n                  onMouseLeave={(e) => e.target.style.background = '#7C3AED'}\n                >\n                  <FiCpu /> Admin Panel\n                </button>\n                <button\n                  onClick={handleAdminLogout}\n                  style={{\n                    background: '#EF4444',\n                    color: 'white',\n                    padding: '0.75rem 1.5rem',\n                    borderRadius: '0.5rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    fontSize: '1rem',\n                    transition: 'all 0.3s ease'\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = '#DC2626'}\n                  onMouseLeave={(e) => e.target.style.background = '#EF4444'}\n                >\n                  <FiX /> Logout Admin\n                </button>\n              </>\n            ) : (\n              <button\n                onClick={() => setShowAdminLogin(true)}\n                style={{\n                  background: '#6B7280',\n                  color: 'white',\n                  padding: '0.75rem 1.5rem',\n                  borderRadius: '0.5rem',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  fontSize: '1rem',\n                  transition: 'all 0.3s ease'\n                }}\n                onMouseEnter={(e) => e.target.style.background = '#4B5563'}\n                onMouseLeave={(e) => e.target.style.background = '#6B7280'}\n              >\n                <FiCpu /> Admin Login\n              </button>\n            )}\n\n            <button\n              onClick={() => setShowSuggestionModal(true)}\n              style={{\n                background: '#059669',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              }}\n              onMouseEnter={(e) => e.target.style.background = '#047857'}\n              onMouseLeave={(e) => e.target.style.background = '#059669'}\n            >\n              <FiSend /> Suggest Materials\n            </button>\n\n            <button\n              onClick={() => setShowAddCourseModal(true)}\n              style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              }}\n              onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n              onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n            >\n              <FiPlus /> Add New Course\n            </button>\n            <button\n              onClick={() => setShowSettingsModal(true)}\n              style={{\n                background: 'white',\n                color: '#DC2626',\n                border: '2px solid #DC2626',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              }}\n              onMouseEnter={(e) => e.target.style.background = '#FEE2E2'}\n              onMouseLeave={(e) => e.target.style.background = 'white'}\n            >\n              <FiSettings /> Settings\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Overview */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        }}>\n          {[\n            { icon: FiBook, label: 'Total Courses', value: stats.total.toString(), color: '#DC2626', bgColor: '#FEE2E2' },\n            { icon: FiCheckCircle, label: 'Completed', value: stats.completed.toString(), color: '#7C3AED', bgColor: '#EDE9FE' },\n            { icon: FiLoader, label: 'In Progress', value: stats.active.toString(), color: '#EA580C', bgColor: '#FED7AA' },\n            { icon: FiAlertCircle, label: 'Pending', value: stats.pending.toString(), color: '#6B7280', bgColor: '#F3F4F6' }\n          ].map((stat, index) => (\n            <div key={index} style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem',\n              display: 'flex',\n              alignItems: 'center',\n              transition: 'transform 0.3s ease',\n              cursor: 'pointer'\n            }}\n            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n            >\n              <div style={{\n                background: stat.bgColor,\n                padding: '0.75rem',\n                borderRadius: '50%',\n                marginRight: '1rem'\n              }}>\n                <stat.icon size={24} color={stat.color} />\n              </div>\n              <div>\n                <p style={{ color: '#6B7280', fontSize: '0.875rem', margin: 0 }}>{stat.label}</p>\n                <h3 style={{ fontSize: '2rem', fontWeight: 'bold', color: stat.color, margin: 0 }}>\n                  {stat.value}\n                </h3>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: '2fr 1fr',\n          gap: '2rem',\n          '@media (max-width: 1024px)': {\n            gridTemplateColumns: '1fr'\n          }\n        }}>\n          {/* Left Column */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n            {/* Courses Section */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '1rem'\n              }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', margin: 0 }}>Your Courses</h2>\n                <div style={{ display: 'flex', gap: '0.5rem' }}>\n                  {['All', 'Active', 'Completed'].map((filter, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleFilterChange(filter)}\n                      style={{\n                        background: courseFilter === filter ? 'white' : '#B91C1C',\n                        color: courseFilter === filter ? '#DC2626' : 'white',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '0.375rem',\n                        border: 'none',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease'\n                      }}\n                    >\n                      {filter}\n                    </button>\n                  ))}\n                </div>\n              </div>\n              <div style={{ padding: '1.5rem' }}>\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                  gap: '1rem'\n                }}>\n                  {getFilteredCourses().map((course, index) => (\n                    <div\n                      key={index}\n                      onClick={() => handleCourseClick(course)}\n                      style={{\n                        border: '1px solid #E5E7EB',\n                        borderRadius: '0.5rem',\n                        padding: '1rem',\n                        transition: 'all 0.3s ease',\n                        cursor: 'pointer'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.borderColor = course.color;\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.borderColor = '#E5E7EB';\n                        e.currentTarget.style.transform = 'translateY(0)';\n                      }}\n                    >\n                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.75rem' }}>\n                        <span style={{\n                          background: course.bgColor,\n                          color: course.color,\n                          fontSize: '0.75rem',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '0.25rem'\n                        }}>\n                          {course.subject}\n                        </span>\n                        <span style={{ color: '#F59E0B', fontSize: '0.875rem', display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                          <FiStar size={14} /> {course.rating}\n                        </span>\n                      </div>\n                      <h3 style={{ fontWeight: 'bold', fontSize: '1.125rem', marginBottom: '0.5rem' }}>\n                        {course.title}\n                      </h3>\n                      <p style={{ color: '#6B7280', fontSize: '0.875rem', marginBottom: '1rem' }}>\n                        {course.description}\n                      </p>\n                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <div style={{ display: 'flex', alignItems: 'center' }}>\n                          <div style={{\n                            width: '2rem',\n                            height: '2rem',\n                            borderRadius: '50%',\n                            background: course.bgColor,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            marginRight: '0.5rem'\n                          }}>\n                            <FiUser size={14} color={course.color} />\n                          </div>\n                          <span style={{ fontSize: '0.875rem' }}>{course.professor}</span>\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>{course.duration}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <button\n                  onClick={() => setShowAddCourseModal(true)}\n                  style={{\n                    marginTop: '1.5rem',\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '2px dashed #D1D5DB',\n                    borderRadius: '0.5rem',\n                    background: 'transparent',\n                    color: '#6B7280',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.5rem'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.target.style.borderColor = '#DC2626';\n                    e.target.style.color = '#DC2626';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.target.style.borderColor = '#D1D5DB';\n                    e.target.style.color = '#6B7280';\n                  }}\n                >\n                  <FiPlus /> Add More Courses\n                </button>\n              </div>\n            </div>\n\n            {/* Exams Section */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem'\n              }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', margin: 0 }}>Upcoming Exams</h2>\n              </div>\n              <div style={{ padding: '1.5rem' }}>\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  {[\n                    {\n                      title: 'Midterm Exam - Data Structures',\n                      description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n                      date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n                      timeLeft: '3 Days Left',\n                      icon: FiFileText,\n                      color: '#DC2626',\n                      bgColor: '#FEE2E2'\n                    },\n                    {\n                      title: 'Quiz 2 - Linear Algebra',\n                      description: 'Coverage: Matrix Operations, Determinants',\n                      date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n                      timeLeft: '1 Week Left',\n                      icon: FiTarget,\n                      color: '#7C3AED',\n                      bgColor: '#EDE9FE'\n                    },\n                    {\n                      title: 'Final Exam - Quantum Mechanics',\n                      description: 'Coverage: Entire Semester',\n                      date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n                      timeLeft: '2 Weeks Left',\n                      icon: FiZap,\n                      color: '#EA580C',\n                      bgColor: '#FED7AA'\n                    }\n                  ].map((exam, index) => (\n                    <div key={index} style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      padding: '1rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      transition: 'all 0.3s ease',\n                      cursor: 'pointer'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = exam.bgColor;\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = 'transparent';\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    }}\n                    >\n                      <div style={{\n                        background: exam.bgColor,\n                        color: exam.color,\n                        padding: '0.75rem',\n                        borderRadius: '0.5rem',\n                        marginRight: '1rem'\n                      }}>\n                        <exam.icon size={20} />\n                      </div>\n                      <div style={{ flex: 1 }}>\n                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '0.5rem' }}>\n                          <div>\n                            <h3 style={{ fontWeight: 'bold', margin: 0, marginBottom: '0.25rem' }}>{exam.title}</h3>\n                            <p style={{ color: '#6B7280', fontSize: '0.875rem', margin: 0 }}>{exam.description}</p>\n                          </div>\n                          <span style={{\n                            background: exam.bgColor,\n                            color: exam.color,\n                            fontSize: '0.75rem',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '0.25rem'\n                          }}>\n                            {exam.timeLeft}\n                          </span>\n                        </div>\n                        <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', color: '#6B7280' }}>\n                          <FiCalendar size={14} style={{ marginRight: '0.5rem' }} />\n                          <span>{exam.date}</span>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <button\n                  onClick={() => setShowAddExamModal(true)}\n                  style={{\n                    marginTop: '1.5rem',\n                    width: '100%',\n                    padding: '0.75rem',\n                    background: '#DC2626',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.5rem'\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n                  onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n                >\n                  <FiPlus /> Add Exam Reminder\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n            {/* Progress Overview */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', marginBottom: '1rem' }}>\n                Academic Progress\n              </h2>\n              <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '1.5rem' }}>\n                <div style={{ position: 'relative', width: '160px', height: '160px' }}>\n                  <svg style={{ width: '100%', height: '100%' }} viewBox=\"0 0 100 100\">\n                    <circle\n                      cx=\"50\"\n                      cy=\"50\"\n                      r=\"40\"\n                      stroke=\"#E5E7EB\"\n                      strokeWidth=\"8\"\n                      fill=\"transparent\"\n                    />\n                    <circle\n                      cx=\"50\"\n                      cy=\"50\"\n                      r=\"40\"\n                      stroke=\"#DC2626\"\n                      strokeWidth=\"8\"\n                      fill=\"transparent\"\n                      strokeDasharray=\"251.2\"\n                      strokeDashoffset=\"62.8\"\n                      strokeLinecap=\"round\"\n                      style={{ transform: 'rotate(-90deg)', transformOrigin: '50% 50%' }}\n                    />\n                  </svg>\n                  <div style={{\n                    position: 'absolute',\n                    inset: 0,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    flexDirection: 'column'\n                  }}>\n                    <span style={{ fontSize: '2rem', fontWeight: 'bold', color: '#DC2626' }}>75%</span>\n                    <span style={{ color: '#6B7280', fontSize: '0.875rem' }}>Overall</span>\n                  </div>\n                </div>\n              </div>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n                {[\n                  { name: 'Data Structures', progress: '82%', color: '#DC2626' },\n                  { name: 'Linear Algebra', progress: '90%', color: '#7C3AED' },\n                  { name: 'Quantum Mechanics', progress: '65%', color: '#EA580C' },\n                  { name: 'Modern Poetry', progress: '45%', color: '#6B7280' }\n                ].map((subject, index) => (\n                  <div key={index} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                    <div style={{ display: 'flex', alignItems: 'center' }}>\n                      <div style={{\n                        width: '0.75rem',\n                        height: '0.75rem',\n                        borderRadius: '50%',\n                        background: subject.color,\n                        marginRight: '0.5rem'\n                      }} />\n                      <span>{subject.name}</span>\n                    </div>\n                    <span style={{ fontWeight: 'bold' }}>{subject.progress}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Mind Map */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                  Course Mindmap\n                </h2>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer'\n                }}>\n                  <FiMaximize2 />\n                </button>\n              </div>\n              <div style={{\n                height: '300px',\n                background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n                borderRadius: '1rem',\n                position: 'relative',\n                overflow: 'hidden'\n              }}>\n                {/* Central Node */}\n                <div\n                  className=\"mindmap-node\"\n                  style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    background: '#DC2626',\n                    color: 'white',\n                    borderRadius: '0.5rem',\n                    padding: '0.5rem 1rem',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    border: '2px solid transparent'\n                  }}\n                >\n                  Computer Science\n                </div>\n\n                {/* Child Nodes */}\n                {[\n                  { text: 'Data Structures', top: '30%', left: '20%' },\n                  { text: 'Algorithms', top: '50%', left: '20%' },\n                  { text: 'Databases', top: '70%', left: '20%' },\n                  { text: 'AI/ML', top: '30%', left: '80%' },\n                  { text: 'Networking', top: '50%', left: '80%' },\n                  { text: 'Cybersecurity', top: '70%', left: '80%' }\n                ].map((node, index) => (\n                  <div key={index}>\n                    <div\n                      className=\"mindmap-node\"\n                      style={{\n                        position: 'absolute',\n                        top: node.top,\n                        left: node.left,\n                        background: 'white',\n                        borderRadius: '0.5rem',\n                        padding: '0.5rem 1rem',\n                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease',\n                        border: '2px solid transparent',\n                        transform: 'translate(-50%, -50%)'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.transform = 'translate(-50%, -50%) scale(1.05)';\n                        e.target.style.borderColor = '#DC2626';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.transform = 'translate(-50%, -50%) scale(1)';\n                        e.target.style.borderColor = 'transparent';\n                      }}\n                    >\n                      {node.text}\n                    </div>\n                    {/* Connector lines */}\n                    <div style={{\n                      position: 'absolute',\n                      background: '#FCA5A5',\n                      height: '2px',\n                      width: '100px',\n                      top: node.top,\n                      left: node.left === '20%' ? '30%' : '70%',\n                      transformOrigin: 'left center',\n                      transform: node.left === '20%' ?\n                        (node.top === '30%' ? 'translateY(-50%) rotate(-30deg)' :\n                         node.top === '50%' ? 'translateY(-50%)' :\n                         'translateY(-50%) rotate(30deg)') :\n                        (node.top === '30%' ? 'translateY(-50%) rotate(30deg)' :\n                         node.top === '50%' ? 'translateY(-50%)' :\n                         'translateY(-50%) rotate(-30deg)')\n                    }} />\n                  </div>\n                ))}\n              </div>\n              <div style={{ marginTop: '1rem', display: 'flex', justifyContent: 'space-between' }}>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <FiPlus size={14} /> Add Node\n                </button>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <FiSave size={14} /> Save\n                </button>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', marginBottom: '1rem' }}>\n                Quick Actions\n              </h2>\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(2, 1fr)',\n                gap: '0.75rem'\n              }}>\n                {[\n                  { icon: FiBookOpen, label: 'Study Materials', color: '#DC2626', bgColor: '#FEE2E2', action: () => setShowStudyMaterialsModal(true) },\n                  { icon: FiClipboard, label: 'Assignments', color: '#7C3AED', bgColor: '#EDE9FE', action: () => setShowAssignmentsModal(true) },\n                  { icon: FiTrendingUp, label: 'Grades', color: '#EA580C', bgColor: '#FED7AA', action: () => setShowGradesModal(true) },\n                  { icon: FiCalendar, label: 'Schedule', color: '#6B7280', bgColor: '#F3F4F6', action: () => setShowScheduleModal(true) }\n                ].map((action, index) => (\n                  <button\n                    key={index}\n                    onClick={action.action}\n                    style={{\n                      background: action.bgColor,\n                      color: action.color,\n                      padding: '0.75rem',\n                      borderRadius: '0.5rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                      e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                      e.currentTarget.style.boxShadow = 'none';\n                    }}\n                  >\n                    <action.icon size={24} />\n                    <span style={{ fontSize: '0.875rem' }}>{action.label}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Chatbot */}\n      <div style={{\n        position: 'fixed',\n        bottom: '2rem',\n        right: '2rem',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'flex-end',\n        gap: '1rem',\n        zIndex: 1000\n      }}>\n        {/* Chatbot Toggle Button */}\n        <button\n          onClick={() => setChatbotOpen(!chatbotOpen)}\n          style={{\n            width: '3.5rem',\n            height: '3.5rem',\n            borderRadius: '50%',\n            background: '#DC2626',\n            color: 'white',\n            border: 'none',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n            transition: 'all 0.3s ease',\n            animation: 'float 3s ease-in-out infinite'\n          }}\n          onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n          onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n        >\n          <FiCpu size={20} />\n        </button>\n\n        {/* Chatbot Container */}\n        {chatbotOpen && (\n          <div style={{\n            width: '350px',\n            height: '500px',\n            background: 'white',\n            borderRadius: '0.75rem',\n            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n            overflow: 'hidden',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            {/* Chatbot Header */}\n            <div style={{\n              background: '#DC2626',\n              color: 'white',\n              padding: '0.75rem 1rem',\n              fontWeight: 'bold',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            }}>\n              <span>EduAI Assistant</span>\n              <button\n                onClick={() => setChatbotOpen(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: 'white',\n                  cursor: 'pointer'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            {/* Messages */}\n            <div style={{\n              flex: 1,\n              padding: '1rem',\n              overflowY: 'auto',\n              background: '#F8FAFC'\n            }}>\n              {chatMessages.map((message, index) => (\n                <div key={index} style={{\n                  marginBottom: '1rem',\n                  display: 'flex',\n                  justifyContent: message.isUser ? 'flex-end' : 'flex-start'\n                }}>\n                  <div style={{\n                    background: message.isUser ? '#DC2626' : '#FEE2E2',\n                    color: message.isUser ? 'white' : '#1F2937',\n                    padding: '0.75rem',\n                    borderRadius: '0.5rem',\n                    maxWidth: '75%'\n                  }}>\n                    {message.text}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Input */}\n            <div style={{\n              display: 'flex',\n              padding: '0.75rem',\n              borderTop: '1px solid #E5E7EB',\n              background: 'white'\n            }}>\n              <input\n                type=\"text\"\n                value={chatInput}\n                onChange={(e) => setChatInput(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Type your question...\"\n                style={{\n                  flex: 1,\n                  padding: '0.5rem 0.75rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '1.25rem',\n                  outline: 'none'\n                }}\n              />\n              <button\n                onClick={handleChatSend}\n                style={{\n                  marginLeft: '0.5rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '1.25rem',\n                  padding: '0.5rem 1rem',\n                  cursor: 'pointer'\n                }}\n              >\n                <FiSend />\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Add Course Modal */}\n      {showAddCourseModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '500px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Add New Course\n              </h2>\n              <button\n                onClick={() => setShowAddCourseModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Course Title *</label>\n                <input\n                  type=\"text\"\n                  value={newCourse.title}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, title: e.target.value }))}\n                  placeholder=\"e.g., Data Structures & Algorithms\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Subject *</label>\n                <input\n                  type=\"text\"\n                  value={newCourse.subject}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, subject: e.target.value }))}\n                  placeholder=\"e.g., Computer Science\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Professor *</label>\n                <input\n                  type=\"text\"\n                  value={newCourse.professor}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, professor: e.target.value }))}\n                  placeholder=\"e.g., Prof. Smith\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Description</label>\n                <textarea\n                  value={newCourse.description}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, description: e.target.value }))}\n                  placeholder=\"Course description...\"\n                  rows={3}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem',\n                    resize: 'vertical'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Duration</label>\n                <input\n                  type=\"text\"\n                  value={newCourse.duration}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, duration: e.target.value }))}\n                  placeholder=\"e.g., 12 Weeks\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Color Theme</label>\n                <div style={{ display: 'flex', gap: '0.5rem' }}>\n                  {['#DC2626', '#7C3AED', '#EA580C', '#6B7280', '#059669', '#0891B2'].map(color => (\n                    <button\n                      key={color}\n                      onClick={() => setNewCourse(prev => ({ ...prev, color }))}\n                      style={{\n                        width: '2rem',\n                        height: '2rem',\n                        borderRadius: '50%',\n                        background: color,\n                        border: newCourse.color === color ? '3px solid #000' : '1px solid #E5E7EB',\n                        cursor: 'pointer'\n                      }}\n                    />\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>\n              <button\n                onClick={() => setShowAddCourseModal(false)}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: 'white',\n                  color: '#6B7280',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleAddCourse}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Add Course\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Add Exam Modal */}\n      {showAddExamModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '500px'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Add Exam Reminder\n              </h2>\n              <button\n                onClick={() => setShowAddExamModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Exam Title *</label>\n                <input\n                  type=\"text\"\n                  value={newExam.title}\n                  onChange={(e) => setNewExam(prev => ({ ...prev, title: e.target.value }))}\n                  placeholder=\"e.g., Midterm Exam - Data Structures\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Course *</label>\n                <select\n                  value={newExam.courseId}\n                  onChange={(e) => setNewExam(prev => ({ ...prev, courseId: e.target.value }))}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                >\n                  <option value=\"\">Select a course</option>\n                  {courses.map(course => (\n                    <option key={course.id} value={course.id}>{course.title}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Exam Date *</label>\n                <input\n                  type=\"date\"\n                  value={newExam.date}\n                  onChange={(e) => setNewExam(prev => ({ ...prev, date: e.target.value }))}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Description</label>\n                <textarea\n                  value={newExam.description}\n                  onChange={(e) => setNewExam(prev => ({ ...prev, description: e.target.value }))}\n                  placeholder=\"Exam coverage and details...\"\n                  rows={3}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem',\n                    resize: 'vertical'\n                  }}\n                />\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>\n              <button\n                onClick={() => setShowAddExamModal(false)}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: 'white',\n                  color: '#6B7280',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleAddExam}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Add Exam\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Settings Modal */}\n      {showSettingsModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '600px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Dashboard Settings\n              </h2>\n              <button\n                onClick={() => setShowSettingsModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n              <div>\n                <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '1rem', color: '#374151' }}>\n                  Preferences\n                </h3>\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <span>Email Notifications</span>\n                    <button style={{\n                      background: '#DC2626',\n                      color: 'white',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '1rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem'\n                    }}>\n                      Enabled\n                    </button>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <span>Dark Mode</span>\n                    <button style={{\n                      background: '#E5E7EB',\n                      color: '#6B7280',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '1rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem'\n                    }}>\n                      Disabled\n                    </button>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <span>Auto-save Progress</span>\n                    <button style={{\n                      background: '#DC2626',\n                      color: 'white',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '1rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem'\n                    }}>\n                      Enabled\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '1rem', color: '#374151' }}>\n                  Data Management\n                </h3>\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n                  <button style={{\n                    padding: '0.75rem',\n                    background: '#F3F4F6',\n                    color: '#374151',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    textAlign: 'left'\n                  }}>\n                    Export Data\n                  </button>\n                  <button style={{\n                    padding: '0.75rem',\n                    background: '#F3F4F6',\n                    color: '#374151',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    textAlign: 'left'\n                  }}>\n                    Import Data\n                  </button>\n                  <button style={{\n                    padding: '0.75rem',\n                    background: '#FEE2E2',\n                    color: '#DC2626',\n                    border: '1px solid #FECACA',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    textAlign: 'left'\n                  }}>\n                    Reset All Data\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '2rem' }}>\n              <button\n                onClick={() => setShowSettingsModal(false)}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Close\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Study Materials Modal */}\n      {showStudyMaterialsModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '700px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Study Materials\n              </h2>\n              <button\n                onClick={() => setShowStudyMaterialsModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            {isAdmin && (\n              <div style={{ marginBottom: '1.5rem' }}>\n                <button\n                  onClick={() => setShowUploadModal(true)}\n                  style={{\n                    background: '#7C3AED',\n                    color: 'white',\n                    padding: '0.75rem 1.5rem',\n                    borderRadius: '0.5rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    transition: 'all 0.3s ease'\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = '#6D28D9'}\n                  onMouseLeave={(e) => e.target.style.background = '#7C3AED'}\n                >\n                  <FiPlus /> Upload PDF Material (Admin Only)\n                </button>\n              </div>\n            )}\n\n            {!isAdmin && (\n              <div style={{\n                marginBottom: '1.5rem',\n                background: '#F0F9FF',\n                padding: '1rem',\n                borderRadius: '0.5rem',\n                border: '1px solid #BAE6FD'\n              }}>\n                <p style={{ margin: 0, fontSize: '0.875rem', color: '#0284C7' }}>\n                  <strong>Need study materials?</strong> Use the \"Suggest Materials\" button to request PDFs from admin.\n                </p>\n              </div>\n            )}\n\n            {/* Global Materials Section */}\n            <div style={{ marginBottom: '2rem' }}>\n              <h3 style={{\n                fontSize: '1.2rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#7C3AED',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <FiBookOpen /> Global Study Materials (Admin Provided)\n              </h3>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n                {globalMaterials.map((material, idx) => (\n                  <div key={material.id} style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '1rem',\n                    background: '#F8FAFC',\n                    borderRadius: '0.5rem',\n                    border: '1px solid #E2E8F0',\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                  }}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', flex: 1 }}>\n                      <div style={{\n                        background: '#7C3AED',\n                        color: 'white',\n                        padding: '0.75rem',\n                        borderRadius: '0.5rem',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      }}>\n                        <FiFileText size={20} />\n                      </div>\n                      <div style={{ flex: 1 }}>\n                        <div style={{ fontWeight: 600, fontSize: '1rem', marginBottom: '0.25rem' }}>\n                          {material.name}\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#6B7280', marginBottom: '0.25rem' }}>\n                          {material.description}\n                        </div>\n                        <div style={{ fontSize: '0.75rem', color: '#9CA3AF' }}>\n                          {material.size} • {material.category} • {material.downloads} downloads • Uploaded: {material.uploadDate}\n                        </div>\n                      </div>\n                    </div>\n                    <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>\n                      <button\n                        onClick={() => handleGlobalDownload(material)}\n                        style={{\n                          background: '#059669',\n                          color: 'white',\n                          border: 'none',\n                          padding: '0.75rem',\n                          borderRadius: '0.5rem',\n                          cursor: 'pointer',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => e.target.style.background = '#047857'}\n                        onMouseLeave={(e) => e.target.style.background = '#059669'}\n                        title=\"Download PDF\"\n                      >\n                        <FiDownload size={16} />\n                      </button>\n                      {isAdmin && (\n                        <button\n                          onClick={() => handleDeleteGlobalMaterial(material.id)}\n                          style={{\n                            background: '#EF4444',\n                            color: 'white',\n                            border: 'none',\n                            padding: '0.75rem',\n                            borderRadius: '0.5rem',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            transition: 'all 0.3s ease'\n                          }}\n                          onMouseEnter={(e) => e.target.style.background = '#DC2626'}\n                          onMouseLeave={(e) => e.target.style.background = '#EF4444'}\n                          title=\"Delete PDF (Admin Only)\"\n                        >\n                          <FiTrash2 size={16} />\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Course-specific Materials */}\n            <div>\n              <h3 style={{\n                fontSize: '1.2rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#DC2626',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <FiBook /> Course Materials\n              </h3>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                {courses.map(course => (\n                  <div key={course.id} style={{\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    padding: '1rem'\n                  }}>\n                    <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '0.5rem', color: course.color }}>\n                      {course.title}\n                    </h3>\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    {course.materials && course.materials.length > 0 ? course.materials.map((material, idx) => (\n                      <div key={idx} style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.75rem',\n                        background: '#F9FAFB',\n                        borderRadius: '0.5rem',\n                        border: '1px solid #E5E7EB'\n                      }}>\n                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', flex: 1 }}>\n                          <div style={{\n                            background: '#DC2626',\n                            color: 'white',\n                            padding: '0.5rem',\n                            borderRadius: '0.25rem',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          }}>\n                            <FiFileText size={16} />\n                          </div>\n                          <div>\n                            <div style={{ fontWeight: 500, fontSize: '0.9rem' }}>\n                              {typeof material === 'string' ? material : material.name}\n                            </div>\n                            {typeof material === 'object' && (\n                              <div style={{ fontSize: '0.75rem', color: '#6B7280' }}>\n                                {material.size} • Uploaded on {material.uploadDate}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => typeof material === 'object' ? handleDownloadFile(material) : alert('Download not available')}\n                            style={{\n                              background: '#059669',\n                              color: 'white',\n                              border: 'none',\n                              padding: '0.5rem',\n                              borderRadius: '0.25rem',\n                              cursor: 'pointer',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}\n                            title=\"Download PDF\"\n                          >\n                            <FiDownload size={14} />\n                          </button>\n                          <button\n                            onClick={() => handleDeleteFile(course.id, idx)}\n                            style={{\n                              background: '#EF4444',\n                              color: 'white',\n                              border: 'none',\n                              padding: '0.5rem',\n                              borderRadius: '0.25rem',\n                              cursor: 'pointer',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}\n                            title=\"Delete PDF\"\n                          >\n                            <FiTrash2 size={14} />\n                          </button>\n                        </div>\n                      </div>\n                    )) : (\n                      <div style={{\n                        textAlign: 'center',\n                        padding: '2rem',\n                        color: '#6B7280',\n                        background: '#F9FAFB',\n                        borderRadius: '0.5rem',\n                        border: '2px dashed #D1D5DB'\n                      }}>\n                        <FiFileText size={48} style={{ margin: '0 auto 1rem', opacity: 0.5 }} />\n                        <p style={{ margin: 0, fontStyle: 'italic' }}>No PDF materials uploaded yet</p>\n                        <p style={{ margin: '0.5rem 0 0', fontSize: '0.875rem' }}>\n                          Click \"Upload PDF Material\" to add study materials\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Assignments Modal */}\n      {showAssignmentsModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '700px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Assignments\n              </h2>\n              <button\n                onClick={() => setShowAssignmentsModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ marginBottom: '1.5rem' }}>\n              <button style={{\n                background: '#7C3AED',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <FiPlus /> Create Assignment\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              {courses.map(course => (\n                <div key={course.id} style={{\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  padding: '1rem'\n                }}>\n                  <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '0.5rem', color: course.color }}>\n                    {course.title}\n                  </h3>\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    {course.assignments && course.assignments.length > 0 ? course.assignments.map((assignment, idx) => (\n                      <div key={idx} style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.75rem',\n                        background: '#F9FAFB',\n                        borderRadius: '0.25rem',\n                        border: '1px solid #E5E7EB'\n                      }}>\n                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                          <FiClipboard size={16} color=\"#7C3AED\" />\n                          <span>{assignment}</span>\n                        </div>\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button style={{\n                            background: '#7C3AED',\n                            color: 'white',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '0.25rem',\n                            border: 'none',\n                            cursor: 'pointer',\n                            fontSize: '0.75rem'\n                          }}>\n                            View\n                          </button>\n                          <button style={{\n                            background: '#059669',\n                            color: 'white',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '0.25rem',\n                            border: 'none',\n                            cursor: 'pointer',\n                            fontSize: '0.75rem'\n                          }}>\n                            Submit\n                          </button>\n                        </div>\n                      </div>\n                    )) : (\n                      <p style={{ color: '#6B7280', fontStyle: 'italic' }}>No assignments yet</p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Grades Modal */}\n      {showGradesModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '600px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Grades Overview\n              </h2>\n              <button\n                onClick={() => setShowGradesModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              {courses.map(course => (\n                <div key={course.id} style={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  padding: '1rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  background: course.bgColor\n                }}>\n                  <div>\n                    <h3 style={{ fontSize: '1.1rem', fontWeight: 600, margin: 0, color: course.color }}>\n                      {course.title}\n                    </h3>\n                    <p style={{ fontSize: '0.875rem', color: '#6B7280', margin: 0 }}>\n                      Progress: {course.progress}%\n                    </p>\n                  </div>\n                  <div style={{ textAlign: 'right' }}>\n                    <div style={{\n                      fontSize: '1.5rem',\n                      fontWeight: 'bold',\n                      color: course.color\n                    }}>\n                      {course.grade || 'N/A'}\n                    </div>\n                    <div style={{ fontSize: '0.75rem', color: '#6B7280' }}>\n                      Current Grade\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div style={{ marginTop: '1.5rem', padding: '1rem', background: '#F9FAFB', borderRadius: '0.5rem' }}>\n              <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '0.5rem' }}>Overall GPA</h3>\n              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#DC2626' }}>3.7</div>\n              <p style={{ fontSize: '0.875rem', color: '#6B7280', margin: 0 }}>\n                Based on completed courses\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Schedule Modal */}\n      {showScheduleModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '800px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Academic Schedule\n              </h2>\n              <button\n                onClick={() => setShowScheduleModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ marginBottom: '1.5rem' }}>\n              <button style={{\n                background: '#6B7280',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <FiPlus /> Add Event\n              </button>\n            </div>\n\n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '1px', background: '#E5E7EB', borderRadius: '0.5rem', overflow: 'hidden' }}>\n              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (\n                <div key={day} style={{\n                  background: '#F9FAFB',\n                  padding: '0.75rem',\n                  textAlign: 'center',\n                  fontWeight: 600,\n                  fontSize: '0.875rem'\n                }}>\n                  {day}\n                </div>\n              ))}\n\n              {Array.from({ length: 35 }, (_, i) => (\n                <div key={i} style={{\n                  background: 'white',\n                  padding: '0.75rem',\n                  minHeight: '60px',\n                  fontSize: '0.875rem',\n                  position: 'relative'\n                }}>\n                  <div style={{ color: '#6B7280' }}>{((i % 31) + 1)}</div>\n                  {i === 14 && (\n                    <div style={{\n                      background: '#DC2626',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      padding: '0.25rem',\n                      borderRadius: '0.25rem',\n                      marginTop: '0.25rem'\n                    }}>\n                      Midterm\n                    </div>\n                  )}\n                  {i === 21 && (\n                    <div style={{\n                      background: '#7C3AED',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      padding: '0.25rem',\n                      borderRadius: '0.25rem',\n                      marginTop: '0.25rem'\n                    }}>\n                      Quiz\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n\n            <div style={{ marginTop: '1.5rem' }}>\n              <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '1rem' }}>Upcoming Events</h3>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                {exams.slice(0, 3).map(exam => (\n                  <div key={exam.id} style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '0.75rem',\n                    background: exam.bgColor,\n                    borderRadius: '0.5rem'\n                  }}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                      <exam.icon size={16} color={exam.color} />\n                      <span style={{ fontWeight: 500 }}>{exam.title}</span>\n                    </div>\n                    <span style={{ fontSize: '0.875rem', color: exam.color, fontWeight: 500 }}>\n                      {exam.timeLeft}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Upload PDF Modal */}\n      {showUploadModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '500px'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: adminUploadType === 'global' ? '#7C3AED' : '#DC2626', margin: 0 }}>\n                {adminUploadType === 'global' ? 'Upload Global PDF (Admin)' : 'Upload PDF Material'}\n              </h2>\n              <button\n                onClick={() => {\n                  setShowUploadModal(false);\n                  setSelectedFile(null);\n                  setUploadCourseId('');\n                  setUploadProgress(0);\n                }}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n              {adminUploadType !== 'global' && (\n                <div>\n                  <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                    Select Course *\n                  </label>\n                  <select\n                    value={uploadCourseId}\n                    onChange={(e) => setUploadCourseId(e.target.value)}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      fontSize: '1rem'\n                    }}\n                  >\n                    <option value=\"\">Choose a course...</option>\n                    {courses.map(course => (\n                      <option key={course.id} value={course.id}>\n                        {course.title}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              )}\n\n              {adminUploadType === 'global' && (\n                <div style={{\n                  background: '#F0F9FF',\n                  border: '1px solid #BAE6FD',\n                  borderRadius: '0.5rem',\n                  padding: '1rem'\n                }}>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>\n                    <FiBookOpen size={20} color=\"#0284C7\" />\n                    <span style={{ fontWeight: 600, color: '#0284C7' }}>Global Upload Mode</span>\n                  </div>\n                  <p style={{ margin: 0, fontSize: '0.875rem', color: '#6B7280' }}>\n                    This PDF will be available to all users in the Global Study Materials section.\n                  </p>\n                </div>\n              )}\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                  Select PDF File *\n                </label>\n                <div style={{\n                  border: '2px dashed #D1D5DB',\n                  borderRadius: '0.5rem',\n                  padding: '2rem',\n                  textAlign: 'center',\n                  background: selectedFile ? '#F0FDF4' : '#F9FAFB',\n                  borderColor: selectedFile ? '#10B981' : '#D1D5DB'\n                }}>\n                  <input\n                    type=\"file\"\n                    accept=\".pdf\"\n                    onChange={handleFileSelect}\n                    style={{ display: 'none' }}\n                    id=\"pdf-upload\"\n                  />\n                  <label\n                    htmlFor=\"pdf-upload\"\n                    style={{\n                      cursor: 'pointer',\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    }}\n                  >\n                    <FiUpload size={32} color={selectedFile ? '#10B981' : '#6B7280'} />\n                    {selectedFile ? (\n                      <div>\n                        <div style={{ fontWeight: 500, color: '#10B981' }}>\n                          {selectedFile.name}\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>\n                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                        </div>\n                      </div>\n                    ) : (\n                      <div>\n                        <div style={{ fontWeight: 500, color: '#6B7280' }}>\n                          Click to select PDF file\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#9CA3AF' }}>\n                          Maximum file size: 10MB\n                        </div>\n                      </div>\n                    )}\n                  </label>\n                </div>\n              </div>\n\n              {isUploading && (\n                <div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>\n                    <span style={{ fontSize: '0.875rem', fontWeight: 500 }}>Uploading...</span>\n                    <span style={{ fontSize: '0.875rem', color: '#6B7280' }}>{uploadProgress}%</span>\n                  </div>\n                  <div style={{\n                    width: '100%',\n                    height: '0.5rem',\n                    background: '#E5E7EB',\n                    borderRadius: '0.25rem',\n                    overflow: 'hidden'\n                  }}>\n                    <div style={{\n                      width: `${uploadProgress}%`,\n                      height: '100%',\n                      background: '#DC2626',\n                      transition: 'width 0.3s ease'\n                    }} />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>\n              <button\n                onClick={() => {\n                  setShowUploadModal(false);\n                  setSelectedFile(null);\n                  setUploadCourseId('');\n                  setUploadProgress(0);\n                }}\n                disabled={isUploading}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: 'white',\n                  color: '#6B7280',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: isUploading ? 'not-allowed' : 'pointer',\n                  opacity: isUploading ? 0.5 : 1\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                onClick={adminUploadType === 'global' ? handleAdminUpload : handleFileUpload}\n                disabled={!selectedFile || (adminUploadType !== 'global' && !uploadCourseId) || isUploading}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: (!selectedFile || (adminUploadType !== 'global' && !uploadCourseId) || isUploading) ? '#D1D5DB' : (adminUploadType === 'global' ? '#7C3AED' : '#DC2626'),\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: (!selectedFile || (adminUploadType !== 'global' && !uploadCourseId) || isUploading) ? 'not-allowed' : 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                }}\n              >\n                {isUploading ? (\n                  <>\n                    <FiLoader className=\"animate-spin\" size={16} />\n                    Uploading...\n                  </>\n                ) : (\n                  <>\n                    <FiUpload size={16} />\n                    Upload PDF\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Admin Panel Modal */}\n      {showAdminPanel && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '800px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#7C3AED', margin: 0 }}>\n                Admin Panel - Content Management\n              </h2>\n              <button\n                onClick={() => setShowAdminPanel(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n              {/* Upload Section */}\n              <div style={{\n                background: '#F8FAFC',\n                padding: '1.5rem',\n                borderRadius: '0.75rem',\n                border: '1px solid #E2E8F0'\n              }}>\n                <h3 style={{ fontSize: '1.2rem', fontWeight: 600, marginBottom: '1rem', color: '#7C3AED' }}>\n                  Upload Content for Users\n                </h3>\n\n                <div style={{ display: 'flex', gap: '1rem', marginBottom: '1.5rem' }}>\n                  <button\n                    onClick={() => {\n                      setAdminUploadType('global');\n                      setShowUploadModal(true);\n                    }}\n                    style={{\n                      background: '#7C3AED',\n                      color: 'white',\n                      padding: '0.75rem 1.5rem',\n                      borderRadius: '0.5rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => e.target.style.background = '#6D28D9'}\n                    onMouseLeave={(e) => e.target.style.background = '#7C3AED'}\n                  >\n                    <FiUpload /> Upload Global PDF\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setAdminUploadType('course');\n                      setShowUploadModal(true);\n                    }}\n                    style={{\n                      background: '#DC2626',\n                      color: 'white',\n                      padding: '0.75rem 1.5rem',\n                      borderRadius: '0.5rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n                    onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n                  >\n                    <FiUpload /> Upload to Course\n                  </button>\n                </div>\n\n                <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>\n                  <p style={{ margin: '0 0 0.5rem' }}>\n                    <strong>Global PDF:</strong> Available to all users in Study Materials section\n                  </p>\n                  <p style={{ margin: 0 }}>\n                    <strong>Course PDF:</strong> Available only to users enrolled in specific course\n                  </p>\n                </div>\n              </div>\n\n              {/* Add Material from Folder */}\n              <div style={{\n                background: '#F0F9FF',\n                padding: '1.5rem',\n                borderRadius: '0.75rem',\n                border: '1px solid #BAE6FD'\n              }}>\n                <h3 style={{ fontSize: '1.2rem', fontWeight: 600, marginBottom: '1rem', color: '#0284C7' }}>\n                  Add Material from Local Folder\n                </h3>\n\n                <div style={{ marginBottom: '1rem' }}>\n                  <p style={{ fontSize: '0.875rem', color: '#6B7280', margin: '0 0 1rem' }}>\n                    Add PDFs that you've already placed in the project folders:\n                  </p>\n\n                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '0.75rem' }}>\n                    {[\n                      { category: 'Mathematics', folder: 'mathematics', color: '#DC2626' },\n                      { category: 'Physics', folder: 'physics', color: '#7C3AED' },\n                      { category: 'Computer Science', folder: 'computer-science', color: '#059669' },\n                      { category: 'General', folder: 'general', color: '#EA580C' }\n                    ].map(cat => (\n                      <button\n                        key={cat.category}\n                        onClick={() => {\n                          const fileName = prompt(`Enter the PDF filename (with .pdf extension) that you've placed in public/study-materials/${cat.folder}/ folder:`);\n                          if (fileName && fileName.endsWith('.pdf')) {\n                            const description = prompt('Enter a description for this material (optional):') || '';\n                            addMaterialFromFolder(fileName, cat.category, description);\n                            alert(`Material \"${fileName}\" added successfully!`);\n                          } else if (fileName) {\n                            alert('Please enter a valid PDF filename with .pdf extension');\n                          }\n                        }}\n                        style={{\n                          background: cat.color,\n                          color: 'white',\n                          padding: '0.75rem',\n                          borderRadius: '0.5rem',\n                          border: 'none',\n                          cursor: 'pointer',\n                          fontSize: '0.875rem',\n                          fontWeight: 500,\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => e.target.style.opacity = '0.8'}\n                        onMouseLeave={(e) => e.target.style.opacity = '1'}\n                      >\n                        Add {cat.category} PDF\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                <div style={{\n                  background: '#FEF3C7',\n                  padding: '1rem',\n                  borderRadius: '0.5rem',\n                  border: '1px solid #FDE68A'\n                }}>\n                  <p style={{ fontSize: '0.875rem', color: '#92400E', margin: 0 }}>\n                    <strong>Instructions:</strong> First place your PDF files in the corresponding folders:\n                    <br />• Mathematics: <code>public/study-materials/mathematics/</code>\n                    <br />• Physics: <code>public/study-materials/physics/</code>\n                    <br />• Computer Science: <code>public/study-materials/computer-science/</code>\n                    <br />• General: <code>public/study-materials/general/</code>\n                  </p>\n                </div>\n              </div>\n\n              {/* Global Materials Management */}\n              <div style={{\n                background: '#F0FDF4',\n                padding: '1.5rem',\n                borderRadius: '0.75rem',\n                border: '1px solid #BBF7D0'\n              }}>\n                <h3 style={{ fontSize: '1.2rem', fontWeight: 600, marginBottom: '1rem', color: '#059669' }}>\n                  Global Materials Management\n                </h3>\n\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n                  {globalMaterials.map((material) => (\n                    <div key={material.id} style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      padding: '1rem',\n                      background: 'white',\n                      borderRadius: '0.5rem',\n                      border: '1px solid #D1FAE5'\n                    }}>\n                      <div style={{ flex: 1 }}>\n                        <div style={{ fontWeight: 600, fontSize: '1rem', marginBottom: '0.25rem' }}>\n                          {material.name}\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#6B7280', marginBottom: '0.25rem' }}>\n                          {material.description}\n                        </div>\n                        <div style={{ fontSize: '0.75rem', color: '#9CA3AF' }}>\n                          {material.size} • {material.downloads} downloads • {material.uploadDate}\n                        </div>\n                      </div>\n                      <div style={{ display: 'flex', gap: '0.5rem' }}>\n                        <button\n                          onClick={() => handleGlobalDownload(material)}\n                          style={{\n                            background: '#059669',\n                            color: 'white',\n                            border: 'none',\n                            padding: '0.5rem',\n                            borderRadius: '0.25rem',\n                            cursor: 'pointer'\n                          }}\n                          title=\"Preview/Download\"\n                        >\n                          <FiDownload size={14} />\n                        </button>\n                        <button\n                          onClick={() => handleDeleteGlobalMaterial(material.id)}\n                          style={{\n                            background: '#EF4444',\n                            color: 'white',\n                            border: 'none',\n                            padding: '0.5rem',\n                            borderRadius: '0.25rem',\n                            cursor: 'pointer'\n                          }}\n                          title=\"Delete Material\"\n                        >\n                          <FiTrash2 size={14} />\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Statistics */}\n              <div style={{\n                background: '#FEF3C7',\n                padding: '1.5rem',\n                borderRadius: '0.75rem',\n                border: '1px solid #FDE68A'\n              }}>\n                <h3 style={{ fontSize: '1.2rem', fontWeight: 600, marginBottom: '1rem', color: '#D97706' }}>\n                  Content Statistics\n                </h3>\n\n                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>\n                  <div style={{ textAlign: 'center' }}>\n                    <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#7C3AED' }}>\n                      {globalMaterials.length}\n                    </div>\n                    <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>Global Materials</div>\n                  </div>\n\n                  <div style={{ textAlign: 'center' }}>\n                    <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#DC2626' }}>\n                      {globalMaterials.reduce((sum, mat) => sum + mat.downloads, 0)}\n                    </div>\n                    <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>Total Downloads</div>\n                  </div>\n\n                  <div style={{ textAlign: 'center' }}>\n                    <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#059669' }}>\n                      {courses.length}\n                    </div>\n                    <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>Active Courses</div>\n                  </div>\n\n                  <div style={{ textAlign: 'center' }}>\n                    <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#EA580C' }}>\n                      {courses.reduce((sum, course) => sum + (course.materials?.length || 0), 0)}\n                    </div>\n                    <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>Course Materials</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Admin Login Modal */}\n      {showAdminLogin && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '400px'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#6B7280', margin: 0 }}>\n                Admin Login\n              </h2>\n              <button\n                onClick={() => {\n                  setShowAdminLogin(false);\n                  setAdminPassword('');\n                }}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                  Admin Password\n                </label>\n                <input\n                  type=\"password\"\n                  value={adminPassword}\n                  onChange={(e) => setAdminPassword(e.target.value)}\n                  placeholder=\"Enter admin password\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                  onKeyPress={(e) => e.key === 'Enter' && handleAdminLogin()}\n                />\n              </div>\n\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button\n                  onClick={() => {\n                    setShowAdminLogin(false);\n                    setAdminPassword('');\n                  }}\n                  style={{\n                    flex: 1,\n                    padding: '0.75rem',\n                    background: '#E5E7EB',\n                    color: '#6B7280',\n                    border: 'none',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer'\n                  }}\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleAdminLogin}\n                  style={{\n                    flex: 1,\n                    padding: '0.75rem',\n                    background: '#6B7280',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer'\n                  }}\n                >\n                  Login\n                </button>\n              </div>\n\n              <div style={{\n                background: '#FEF3C7',\n                padding: '1rem',\n                borderRadius: '0.5rem',\n                border: '1px solid #FDE68A'\n              }}>\n                <p style={{ fontSize: '0.875rem', color: '#92400E', margin: 0 }}>\n                  <strong>Demo Password:</strong> admin123\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Suggestion Modal */}\n      {showSuggestionModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '600px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#059669', margin: 0 }}>\n                Suggest Study Materials\n              </h2>\n              <button\n                onClick={() => setShowSuggestionModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>\n                <div>\n                  <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                    Your Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={newSuggestion.userName}\n                    onChange={(e) => setNewSuggestion(prev => ({ ...prev, userName: e.target.value }))}\n                    placeholder=\"Enter your name\"\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      fontSize: '1rem'\n                    }}\n                  />\n                </div>\n\n                <div>\n                  <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                    Email *\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={newSuggestion.email}\n                    onChange={(e) => setNewSuggestion(prev => ({ ...prev, email: e.target.value }))}\n                    placeholder=\"Enter your email\"\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      fontSize: '1rem'\n                    }}\n                  />\n                </div>\n              </div>\n\n              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '1rem' }}>\n                <div>\n                  <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                    Course/Subject\n                  </label>\n                  <select\n                    value={newSuggestion.course}\n                    onChange={(e) => setNewSuggestion(prev => ({ ...prev, course: e.target.value }))}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      fontSize: '1rem'\n                    }}\n                  >\n                    <option value=\"\">Select course...</option>\n                    <option value=\"Mathematics\">Mathematics</option>\n                    <option value=\"Physics\">Physics</option>\n                    <option value=\"Computer Science\">Computer Science</option>\n                    <option value=\"Chemistry\">Chemistry</option>\n                    <option value=\"Biology\">Biology</option>\n                    <option value=\"General\">General</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                    Material Type\n                  </label>\n                  <select\n                    value={newSuggestion.materialType}\n                    onChange={(e) => setNewSuggestion(prev => ({ ...prev, materialType: e.target.value }))}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      fontSize: '1rem'\n                    }}\n                  >\n                    <option value=\"\">Select type...</option>\n                    <option value=\"Textbook\">Textbook</option>\n                    <option value=\"Reference Book\">Reference Book</option>\n                    <option value=\"Notes\">Notes</option>\n                    <option value=\"Question Bank\">Question Bank</option>\n                    <option value=\"Solution Manual\">Solution Manual</option>\n                    <option value=\"Practice Papers\">Practice Papers</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                    Priority\n                  </label>\n                  <select\n                    value={newSuggestion.priority}\n                    onChange={(e) => setNewSuggestion(prev => ({ ...prev, priority: e.target.value }))}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      fontSize: '1rem'\n                    }}\n                  >\n                    <option value=\"Low\">Low</option>\n                    <option value=\"Medium\">Medium</option>\n                    <option value=\"High\">High</option>\n                    <option value=\"Urgent\">Urgent</option>\n                  </select>\n                </div>\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                  Material Title/Name *\n                </label>\n                <input\n                  type=\"text\"\n                  value={newSuggestion.title}\n                  onChange={(e) => setNewSuggestion(prev => ({ ...prev, title: e.target.value }))}\n                  placeholder=\"e.g., Advanced Calculus by James Stewart\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                  Description/Reason\n                </label>\n                <textarea\n                  value={newSuggestion.description}\n                  onChange={(e) => setNewSuggestion(prev => ({ ...prev, description: e.target.value }))}\n                  placeholder=\"Why do you need this material? How will it help your studies?\"\n                  rows={3}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem',\n                    resize: 'vertical'\n                  }}\n                />\n              </div>\n\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button\n                  onClick={() => setShowSuggestionModal(false)}\n                  style={{\n                    flex: 1,\n                    padding: '0.75rem',\n                    background: '#E5E7EB',\n                    color: '#6B7280',\n                    border: 'none',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer'\n                  }}\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSubmitSuggestion}\n                  style={{\n                    flex: 1,\n                    padding: '0.75rem',\n                    background: '#059669',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer'\n                  }}\n                >\n                  Submit Suggestion\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* CSS Animation */}\n      <style>\n        {`\n          @keyframes float {\n            0% { transform: translateY(0px); }\n            50% { transform: translateY(-10px); }\n            100% { transform: translateY(0px); }\n          }\n\n          .animate-spin {\n            animation: spin 1s linear infinite;\n          }\n\n          @keyframes spin {\n            from { transform: rotate(0deg); }\n            to { transform: rotate(360deg); }\n          }\n        `}\n      </style>\n    </div>\n  );\n};\n\nexport default AcademicDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7Q,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,CAC/C;IAAEmC,IAAI,EAAE,gFAAgF;IAAEC,MAAM,EAAE;EAAM,CAAC,CAC1G,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+C,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACiD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,CACrC;IACE6D,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,kBAAkB;IAC3BC,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,8DAA8D;IAC3EC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;IAC1EC,WAAW,EAAE,CAAC,sBAAsB,EAAE,4BAA4B;EACpE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,+DAA+D;IAC5EC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,yBAAyB,EAAE,qBAAqB,CAAC;IAC7DC,WAAW,EAAE,CAAC,wBAAwB,EAAE,yBAAyB;EACnE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,qDAAqD;IAClEC,SAAS,EAAE,gBAAgB;IAC3BC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,0BAA0B,EAAE,YAAY,CAAC;IACrDC,WAAW,EAAE,CAAC,wBAAwB,EAAE,wBAAwB;EAClE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,uDAAuD;IACpEC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;IAClDC,WAAW,EAAE,CAAC,uBAAuB,EAAE,6BAA6B;EACtE,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC,CACjC;IACE6D,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,gCAAgC;IACvCC,WAAW,EAAE,gDAAgD;IAC7DY,IAAI,EAAE,mCAAmC;IACzCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAEnE,UAAU;IAChByD,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,2CAA2C;IACxDY,IAAI,EAAE,kCAAkC;IACxCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE1D,QAAQ;IACdgD,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,gCAAgC;IACvCC,WAAW,EAAE,2BAA2B;IACxCY,IAAI,EAAE,mCAAmC;IACzCC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAEzD,KAAK;IACX+C,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC;IACzC8D,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC;IACrC+D,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfY,IAAI,EAAE,EAAE;IACRG,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACK,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwF,cAAc,EAAEC,iBAAiB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0F,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC4F,WAAW,EAAEC,cAAc,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC8F,OAAO,EAAEC,UAAU,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACgG,cAAc,EAAEC,iBAAiB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkG,eAAe,EAAEC,kBAAkB,CAAC,GAAGnG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EAClE,MAAM,CAACoG,cAAc,EAAEC,iBAAiB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsG,aAAa,EAAEC,gBAAgB,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACwG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0G,WAAW,EAAEC,cAAc,CAAC,GAAG3G,QAAQ,CAAC,CAC7C;IACE6D,EAAE,EAAE,CAAC;IACL+C,QAAQ,EAAE,aAAa;IACvBC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,aAAa;IACrBC,YAAY,EAAE,UAAU;IACxBhD,KAAK,EAAE,oCAAoC;IAC3CC,WAAW,EAAE,sEAAsE;IACnFgD,QAAQ,EAAE,MAAM;IAChB1C,MAAM,EAAE,SAAS;IACjBM,IAAI,EAAE;EACR,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACL+C,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,SAAS;IACjBC,YAAY,EAAE,gBAAgB;IAC9BhD,KAAK,EAAE,mCAAmC;IAC1CC,WAAW,EAAE,mDAAmD;IAChEgD,QAAQ,EAAE,QAAQ;IAClB1C,MAAM,EAAE,UAAU;IAClBM,IAAI,EAAE;EACR,CAAC,CACF,CAAC;EACF,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlH,QAAQ,CAAC;IACjD4G,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBhD,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfgD,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGpH,QAAQ,CAAC,CACrD;IACE6D,EAAE,EAAE,CAAC;IACLwD,IAAI,EAAE,gCAAgC;IACtCC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,SAAS;IACfC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,aAAa;IACvBzD,WAAW,EAAE,kDAAkD;IAC/D0D,SAAS,EAAE,GAAG;IACdC,GAAG,EAAE,6DAA6D;IAClEC,QAAQ,EAAE,gCAAgC;IAC1CC,UAAU,EAAE;EACd,CAAC,EACD;IACEhE,EAAE,EAAE,CAAC;IACLwD,IAAI,EAAE,2BAA2B;IACjCC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,QAAQ;IACdC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,SAAS;IACnBzD,WAAW,EAAE,yCAAyC;IACtD0D,SAAS,EAAE,GAAG;IACdC,GAAG,EAAE,oDAAoD;IACzDC,QAAQ,EAAE,2BAA2B;IACrCC,UAAU,EAAE;EACd,CAAC,EACD;IACEhE,EAAE,EAAE,CAAC;IACLwD,IAAI,EAAE,mCAAmC;IACzCC,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,SAAS;IACfC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,kBAAkB;IAC5BzD,WAAW,EAAE,+CAA+C;IAC5D0D,SAAS,EAAE,GAAG;IACdC,GAAG,EAAE,qEAAqE;IAC1EC,QAAQ,EAAE,mCAAmC;IAC7CC,UAAU,EAAE;EACd,CAAC,CACF,CAAC;;EAEF;EACA5H,SAAS,CAAC,MAAM;IACd,MAAM6H,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7B,MAAMC,KAAK,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,eAAe,CAAC;MACxDF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC7BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;QACxBR,UAAU,CAAC,MAAM;UACfK,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;UACxBH,IAAI,CAACE,KAAK,CAACE,UAAU,GAAG,wCAAwC;QAClE,CAAC,EAAE,GAAG,GAAGH,KAAK,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMI,YAAY,CAACX,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrG,SAAS,CAACsG,IAAI,CAAC,CAAC,EAAE;MACpBzG,eAAe,CAAC0G,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAEzG,IAAI,EAAEE,SAAS;QAAED,MAAM,EAAE;MAAK,CAAC,CAAC,CAAC;MACrEE,YAAY,CAAC,EAAE,CAAC;;MAEhB;MACAyF,UAAU,CAAC,MAAM;QACf,MAAMc,SAAS,GAAG,CAChB,6EAA6E,EAC7E,mEAAmE,EACnE,mEAAmE,EACnE,kEAAkE,EAClE,2DAA2D,CAC5D;QACD,MAAMC,cAAc,GAAGD,SAAS,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,SAAS,CAACK,MAAM,CAAC,CAAC;QAC9EhH,eAAe,CAAC0G,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAEzG,IAAI,EAAE2G,cAAc;UAAE1G,MAAM,EAAE;QAAM,CAAC,CAAC,CAAC;MAC7E,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAM+G,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBX,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAItE,SAAS,CAACjB,KAAK,IAAIiB,SAAS,CAAClB,OAAO,IAAIkB,SAAS,CAACf,SAAS,EAAE;MAC/D,MAAM6C,MAAM,GAAG;QACbjD,EAAE,EAAE0F,IAAI,CAACC,GAAG,CAAC,CAAC;QACd,GAAGxE,SAAS;QACZb,MAAM,EAAE,CAAC;QACTE,OAAO,EAAEW,SAAS,CAACZ,KAAK,GAAG,IAAI;QAC/BE,MAAM,EAAE,QAAQ;QAChBC,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,EAAE;QACfgF,KAAK,EAAE;MACT,CAAC;MACD7F,UAAU,CAACgF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE9B,MAAM,CAAC,CAAC;MACrC7B,YAAY,CAAC;QACXnB,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZE,KAAK,EAAE;MACT,CAAC,CAAC;MACF5B,qBAAqB,CAAC,KAAK,CAAC;MAC5BkH,KAAK,CAAC,4BAA4B,CAAC;IACrC,CAAC,MAAM;MACLA,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzE,OAAO,CAACnB,KAAK,IAAImB,OAAO,CAACN,IAAI,IAAIM,OAAO,CAACH,QAAQ,EAAE;MACrD,MAAM6E,IAAI,GAAG;QACX/F,EAAE,EAAE0F,IAAI,CAACC,GAAG,CAAC,CAAC;QACd,GAAGtE,OAAO;QACVL,QAAQ,EAAEgF,iBAAiB,CAAC3E,OAAO,CAACN,IAAI,CAAC;QACzCE,IAAI,EAAEnE,UAAU;QAChByD,KAAK,EAAE,SAAS;QAChBC,OAAO,EAAE;MACX,CAAC;MACDM,QAAQ,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEgB,IAAI,CAAC,CAAC;MACjCzE,UAAU,CAAC;QACTpB,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfY,IAAI,EAAE,EAAE;QACRG,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFrC,mBAAmB,CAAC,KAAK,CAAC;MAC1BgH,KAAK,CAAC,0BAA0B,CAAC;IACnC,CAAC,MAAM;MACLA,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAIC,QAAQ,IAAK;IACtC,MAAMC,KAAK,GAAG,IAAIR,IAAI,CAAC,CAAC;IACxB,MAAMK,IAAI,GAAG,IAAIL,IAAI,CAACO,QAAQ,CAAC;IAC/B,MAAME,QAAQ,GAAGJ,IAAI,GAAGG,KAAK;IAC7B,MAAME,QAAQ,GAAGlB,IAAI,CAACmB,IAAI,CAACF,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,UAAU;IACnC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,YAAY;IACvC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,aAAa;IACvC,OAAO,GAAGlB,IAAI,CAACmB,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,aAAa;EAChD,CAAC;EAED,MAAME,iBAAiB,GAAIrD,MAAM,IAAK;IACpCtD,iBAAiB,CAACsD,MAAM,CAAC;IACzBlE,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMwH,kBAAkB,GAAIC,MAAM,IAAK;IACrC3G,eAAe,CAAC2G,MAAM,CAAC;EACzB,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI7G,YAAY,KAAK,KAAK,EAAE,OAAOE,OAAO;IAC1C,OAAOA,OAAO,CAAC0G,MAAM,CAACvD,MAAM,IAAIA,MAAM,CAACxC,MAAM,KAAKb,YAAY,CAAC;EACjE,CAAC;EAED,MAAM8G,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAG7G,OAAO,CAACuF,MAAM;IAC5B,MAAMuB,SAAS,GAAG9G,OAAO,CAAC0G,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACpG,MAAM,KAAK,WAAW,CAAC,CAAC4E,MAAM;IACtE,MAAMyB,MAAM,GAAGhH,OAAO,CAAC0G,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACpG,MAAM,KAAK,QAAQ,CAAC,CAAC4E,MAAM;IAChE,MAAM0B,OAAO,GAAGjH,OAAO,CAAC0G,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACnG,QAAQ,KAAK,CAAC,CAAC,CAAC2E,MAAM;IAE5D,OAAO;MAAEsB,KAAK;MAAEC,SAAS;MAAEE,MAAM;MAAEC;IAAQ,CAAC;EAC9C,CAAC;EAED,MAAMC,KAAK,GAAGN,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMO,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR;MACA,IAAIA,IAAI,CAAC1D,IAAI,KAAK,iBAAiB,EAAE;QACnCoC,KAAK,CAAC,+BAA+B,CAAC;QACtC;MACF;;MAEA;MACA,IAAIsB,IAAI,CAACzD,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChCmC,KAAK,CAAC,oCAAoC,CAAC;QAC3C;MACF;MAEAnE,eAAe,CAACyF,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC7F,YAAY,IAAI,CAACE,cAAc,EAAE;MACpCkE,KAAK,CAAC,iCAAiC,CAAC;MACxC;IACF;IAEA7D,cAAc,CAAC,IAAI,CAAC;IACpBF,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAMyF,cAAc,GAAGC,WAAW,CAAC,MAAM;QACvC1F,iBAAiB,CAACiD,IAAI,IAAI;UACxB,IAAIA,IAAI,IAAI,EAAE,EAAE;YACd0C,aAAa,CAACF,cAAc,CAAC;YAC7B,OAAO,EAAE;UACX;UACA,OAAOxC,IAAI,GAAG,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;;MAEP;MACA,MAAM2C,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACnG,YAAY,CAAC;;MAEjD;MACAyC,UAAU,CAAC,MAAM;QACfnE,UAAU,CAACgF,IAAI,IAAIA,IAAI,CAAC8C,GAAG,CAAC5E,MAAM,IAAI;UACpC,IAAIA,MAAM,CAACjD,EAAE,CAAC8H,QAAQ,CAAC,CAAC,KAAKnG,cAAc,EAAE;YAC3C,OAAO;cACL,GAAGsB,MAAM;cACTtC,SAAS,EAAE,CAAC,IAAIsC,MAAM,CAACtC,SAAS,IAAI,EAAE,CAAC,EAAE;gBACvC6C,IAAI,EAAE/B,YAAY,CAAC+B,IAAI;gBACvBC,IAAI,EAAE,KAAK;gBACXC,IAAI,EAAE,CAACjC,YAAY,CAACiC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEqE,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;gBAC1DpE,UAAU,EAAE,IAAI+B,IAAI,CAAC,CAAC,CAACsC,kBAAkB,CAAC,CAAC;gBAC3ClE,GAAG,EAAE4D;cACP,CAAC;YACH,CAAC;UACH;UACA,OAAOzE,MAAM;QACf,CAAC,CAAC,CAAC;QAEHnB,iBAAiB,CAAC,GAAG,CAAC;QACtBoC,UAAU,CAAC,MAAM;UACflC,cAAc,CAAC,KAAK,CAAC;UACrBR,kBAAkB,CAAC,KAAK,CAAC;UACzBE,eAAe,CAAC,IAAI,CAAC;UACrBE,iBAAiB,CAAC,EAAE,CAAC;UACrBE,iBAAiB,CAAC,CAAC,CAAC;UACpB+D,KAAK,CAAC,4BAA4B,CAAC;QACrC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCpC,KAAK,CAAC,kCAAkC,CAAC;MACzC7D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMmG,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAIA,QAAQ,CAACtE,GAAG,EAAE;MAChB;MACA,MAAMuE,IAAI,GAAGjE,QAAQ,CAACkE,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGH,QAAQ,CAACtE,GAAG;MACxBuE,IAAI,CAACG,QAAQ,GAAGJ,QAAQ,CAAC5E,IAAI;MAC7BY,QAAQ,CAACqE,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;MAC/BA,IAAI,CAACM,KAAK,CAAC,CAAC;MACZvE,QAAQ,CAACqE,IAAI,CAACG,WAAW,CAACP,IAAI,CAAC;IACjC,CAAC,MAAM;MACLxC,KAAK,CAAC,iCAAiC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMgD,gBAAgB,GAAGA,CAAC3H,QAAQ,EAAE4H,aAAa,KAAK;IACpD,IAAIC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChEjJ,UAAU,CAACgF,IAAI,IAAIA,IAAI,CAAC8C,GAAG,CAAC5E,MAAM,IAAI;QACpC,IAAIA,MAAM,CAACjD,EAAE,KAAKkB,QAAQ,EAAE;UAC1B,MAAM+H,YAAY,GAAG,CAAC,GAAGhG,MAAM,CAACtC,SAAS,CAAC;UAC1CsI,YAAY,CAACC,MAAM,CAACJ,aAAa,EAAE,CAAC,CAAC;UACrC,OAAO;YAAE,GAAG7F,MAAM;YAAEtC,SAAS,EAAEsI;UAAa,CAAC;QAC/C;QACA,OAAOhG,MAAM;MACf,CAAC,CAAC,CAAC;MACH4C,KAAK,CAAC,4BAA4B,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMsD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC1H,YAAY,EAAE;MACjBoE,KAAK,CAAC,sBAAsB,CAAC;MAC7B;IACF;IAEA7D,cAAc,CAAC,IAAI,CAAC;IACpBF,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAMyF,cAAc,GAAGC,WAAW,CAAC,MAAM;QACvC1F,iBAAiB,CAACiD,IAAI,IAAI;UACxB,IAAIA,IAAI,IAAI,EAAE,EAAE;YACd0C,aAAa,CAACF,cAAc,CAAC;YAC7B,OAAO,EAAE;UACX;UACA,OAAOxC,IAAI,GAAG,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;;MAEP;MACA,MAAM2C,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACnG,YAAY,CAAC;MAEjDyC,UAAU,CAAC,MAAM;QACf,IAAI7B,eAAe,KAAK,QAAQ,EAAE;UAChC;UACA,MAAM+G,WAAW,GAAG;YAClBpJ,EAAE,EAAE0F,IAAI,CAACC,GAAG,CAAC,CAAC;YACdnC,IAAI,EAAE/B,YAAY,CAAC+B,IAAI;YACvBC,IAAI,EAAE,KAAK;YACXC,IAAI,EAAE,CAACjC,YAAY,CAACiC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEqE,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;YAC1DpE,UAAU,EAAE,IAAI+B,IAAI,CAAC,CAAC,CAACsC,kBAAkB,CAAC,CAAC;YAC3CpE,QAAQ,EAAE,SAAS;YACnBzD,WAAW,EAAE,yBAAyB;YACtC0D,SAAS,EAAE,CAAC;YACZC,GAAG,EAAE4D;UACP,CAAC;UACDnE,kBAAkB,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEqE,WAAW,CAAC,CAAC;QACpD,CAAC,MAAM;UACL;UACArJ,UAAU,CAACgF,IAAI,IAAIA,IAAI,CAAC8C,GAAG,CAAC5E,MAAM,IAAI;YACpC,IAAIA,MAAM,CAACjD,EAAE,CAAC8H,QAAQ,CAAC,CAAC,KAAKnG,cAAc,EAAE;cAC3C,OAAO;gBACL,GAAGsB,MAAM;gBACTtC,SAAS,EAAE,CAAC,IAAIsC,MAAM,CAACtC,SAAS,IAAI,EAAE,CAAC,EAAE;kBACvC6C,IAAI,EAAE/B,YAAY,CAAC+B,IAAI;kBACvBC,IAAI,EAAE,KAAK;kBACXC,IAAI,EAAE,CAACjC,YAAY,CAACiC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEqE,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;kBAC1DpE,UAAU,EAAE,IAAI+B,IAAI,CAAC,CAAC,CAACsC,kBAAkB,CAAC,CAAC;kBAC3ClE,GAAG,EAAE4D,OAAO;kBACZ2B,eAAe,EAAE;gBACnB,CAAC;cACH,CAAC;YACH;YACA,OAAOpG,MAAM;UACf,CAAC,CAAC,CAAC;QACL;QAEAnB,iBAAiB,CAAC,GAAG,CAAC;QACtBoC,UAAU,CAAC,MAAM;UACflC,cAAc,CAAC,KAAK,CAAC;UACrBR,kBAAkB,CAAC,KAAK,CAAC;UACzBE,eAAe,CAAC,IAAI,CAAC;UACrBE,iBAAiB,CAAC,EAAE,CAAC;UACrBE,iBAAiB,CAAC,CAAC,CAAC;UACpB+D,KAAK,CAAC,0CAA0C,CAAC;QACnD,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCpC,KAAK,CAAC,kCAAkC,CAAC;MACzC7D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMsH,oBAAoB,GAAG,MAAOlB,QAAQ,IAAK;IAC/C,IAAI;MACF;MACA7E,kBAAkB,CAACwB,IAAI,IAAIA,IAAI,CAAC8C,GAAG,CAAC0B,GAAG,IACrCA,GAAG,CAACvJ,EAAE,KAAKoI,QAAQ,CAACpI,EAAE,GAClB;QAAE,GAAGuJ,GAAG;QAAE1F,SAAS,EAAE0F,GAAG,CAAC1F,SAAS,GAAG;MAAE,CAAC,GACxC0F,GACN,CAAC,CAAC;;MAEF;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACrB,QAAQ,CAACtE,GAAG,CAAC;MAC1C,IAAI0F,QAAQ,CAACE,EAAE,EAAE;QACf;QACA,MAAMrB,IAAI,GAAGjE,QAAQ,CAACkE,aAAa,CAAC,GAAG,CAAC;QACxCD,IAAI,CAACE,IAAI,GAAGH,QAAQ,CAACtE,GAAG;QACxBuE,IAAI,CAACG,QAAQ,GAAGJ,QAAQ,CAACrE,QAAQ,IAAIqE,QAAQ,CAAC5E,IAAI;QAClD6E,IAAI,CAACjB,MAAM,GAAG,QAAQ,CAAC,CAAC;QACxBhD,QAAQ,CAACqE,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;QAC/BA,IAAI,CAACM,KAAK,CAAC,CAAC;QACZvE,QAAQ,CAACqE,IAAI,CAACG,WAAW,CAACP,IAAI,CAAC;;QAE/B;QACAxC,KAAK,CAAC,qBAAqBuC,QAAQ,CAAC5E,IAAI,EAAE,CAAC;MAC7C,CAAC,MAAM;QACL;QACAqC,KAAK,CAAC,+BAA+BuC,QAAQ,CAACrE,QAAQ,oCAAoCqE,QAAQ,CAACpE,UAAU,sCAAsC,CAAC;MACtJ;IACF,CAAC,CAAC,OAAOiE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCpC,KAAK,CAAC,sCAAsCuC,QAAQ,CAACrE,QAAQ,oCAAoCqE,QAAQ,CAACpE,UAAU,sCAAsC,CAAC;IAC7J;EACF,CAAC;EAED,MAAM2F,0BAA0B,GAAIC,UAAU,IAAK;IACjD,IAAIb,MAAM,CAACC,OAAO,CAAC,kFAAkF,CAAC,EAAE;MACtGzF,kBAAkB,CAACwB,IAAI,IAAIA,IAAI,CAACyB,MAAM,CAAC+C,GAAG,IAAIA,GAAG,CAACvJ,EAAE,KAAK4J,UAAU,CAAC,CAAC;MACrE/D,KAAK,CAAC,gCAAgC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMgE,qBAAqB,GAAGA,CAAC9F,QAAQ,EAAEH,QAAQ,EAAEzD,WAAW,GAAG,EAAE,KAAK;IACtE,MAAM2J,SAAS,GAAG;MAChB,aAAa,EAAE,aAAa;MAC5B,SAAS,EAAE,SAAS;MACpB,kBAAkB,EAAE,kBAAkB;MACtC,SAAS,EAAE;IACb,CAAC;IAED,MAAM9F,UAAU,GAAG8F,SAAS,CAAClG,QAAQ,CAAC,IAAI,SAAS;IACnD,MAAMwF,WAAW,GAAG;MAClBpJ,EAAE,EAAE0F,IAAI,CAACC,GAAG,CAAC,CAAC;MACdnC,IAAI,EAAEO,QAAQ;MACdN,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,SAAS;MAAE;MACjBC,UAAU,EAAE,IAAI+B,IAAI,CAAC,CAAC,CAACsC,kBAAkB,CAAC,CAAC;MAC3CpE,QAAQ,EAAEA,QAAQ;MAClBzD,WAAW,EAAEA,WAAW,IAAI,GAAGyD,QAAQ,iBAAiB;MACxDC,SAAS,EAAE,CAAC;MACZC,GAAG,EAAE,oBAAoBE,UAAU,IAAID,QAAQ,EAAE;MACjDA,QAAQ,EAAEA,QAAQ;MAClBC,UAAU,EAAEA;IACd,CAAC;IAEDT,kBAAkB,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEqE,WAAW,CAAC,CAAC;IAClD,OAAOA,WAAW;EACpB,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,eAAe,GAAG,UAAU,CAAC,CAAC;IACpC,IAAIvH,aAAa,KAAKuH,eAAe,EAAE;MACrC9H,UAAU,CAAC,IAAI,CAAC;MAChBM,iBAAiB,CAAC,KAAK,CAAC;MACxBE,gBAAgB,CAAC,EAAE,CAAC;MACpBmD,KAAK,CAAC,uBAAuB,CAAC;IAChC,CAAC,MAAM;MACLA,KAAK,CAAC,uCAAuC,CAAC;MAC9CnD,gBAAgB,CAAC,EAAE,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMuH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B/H,UAAU,CAAC,KAAK,CAAC;IACjBE,iBAAiB,CAAC,KAAK,CAAC;IACxByD,KAAK,CAAC,gCAAgC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMqE,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAAC9G,aAAa,CAACL,QAAQ,IAAI,CAACK,aAAa,CAACJ,KAAK,IAAI,CAACI,aAAa,CAAClD,KAAK,EAAE;MAC3E2F,KAAK,CAAC,yDAAyD,CAAC;MAChE;IACF;IAEA,MAAMsE,UAAU,GAAG;MACjBnK,EAAE,EAAE0F,IAAI,CAACC,GAAG,CAAC,CAAC;MACd,GAAGvC,aAAa;MAChB3C,MAAM,EAAE,SAAS;MACjBM,IAAI,EAAE,IAAI2E,IAAI,CAAC,CAAC,CAACsC,kBAAkB,CAAC;IACtC,CAAC;IAEDlF,cAAc,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEoF,UAAU,CAAC,CAAC;IAC7C9G,gBAAgB,CAAC;MACfN,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBhD,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfgD,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF0C,KAAK,CAAC,+DAA+D,CAAC;IACtEjD,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMwH,4BAA4B,GAAGA,CAACC,YAAY,EAAEC,SAAS,KAAK;IAChExH,cAAc,CAACiC,IAAI,IAAIA,IAAI,CAAC8C,GAAG,CAACsC,UAAU,IACxCA,UAAU,CAACnK,EAAE,KAAKqK,YAAY,GAC1B;MAAE,GAAGF,UAAU;MAAE1J,MAAM,EAAE6J;IAAU,CAAC,GACpCH,UACN,CAAC,CAAC;EACJ,CAAC;EAED,oBACEtM,OAAA;IAAK4G,KAAK,EAAE;MACV8F,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,gBACA7M,OAAA;MAAK4G,KAAK,EAAE;QAAEkG,QAAQ,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAAF,QAAA,gBAEnD7M,OAAA;QAAK4G,KAAK,EAAE;UACVoG,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE,MAAM;UACpBC,QAAQ,EAAE,MAAM;UAChBC,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,gBACA7M,OAAA;UAAA6M,QAAA,gBACE7M,OAAA;YAAI4G,KAAK,EAAE;cACT0G,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,MAAM;cAClB7K,KAAK,EAAE,SAAS;cAChBqK,MAAM,EAAE,CAAC;cACTI,YAAY,EAAE;YAChB,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YAAG4G,KAAK,EAAE;cAAElE,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAE3C;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE,MAAM;YAAED,QAAQ,EAAE;UAAO,CAAE;UAAAP,QAAA,GAC5DzI,OAAO,gBACNpE,OAAA,CAAAE,SAAA;YAAA2M,QAAA,gBACE7M,OAAA;cACE2N,OAAO,EAAEA,CAAA,KAAMpJ,iBAAiB,CAAC,IAAI,CAAE;cACvCqC,KAAK,EAAE;gBACL+F,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,OAAO;gBACdkK,OAAO,EAAE,gBAAgB;gBACzBgB,YAAY,EAAE,QAAQ;gBACtBC,MAAM,EAAE,MAAM;gBACdC,MAAM,EAAE,SAAS;gBACjBd,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBG,GAAG,EAAE,QAAQ;gBACbC,QAAQ,EAAE,MAAM;gBAChBxG,UAAU,EAAE;cACd,CAAE;cACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;cAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;cAAAE,QAAA,gBAE3D7M,OAAA,CAACT,KAAK;gBAAA2G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBACX;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1N,OAAA;cACE2N,OAAO,EAAEvB,iBAAkB;cAC3BxF,KAAK,EAAE;gBACL+F,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,OAAO;gBACdkK,OAAO,EAAE,gBAAgB;gBACzBgB,YAAY,EAAE,QAAQ;gBACtBC,MAAM,EAAE,MAAM;gBACdC,MAAM,EAAE,SAAS;gBACjBd,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBG,GAAG,EAAE,QAAQ;gBACbC,QAAQ,EAAE,MAAM;gBAChBxG,UAAU,EAAE;cACd,CAAE;cACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;cAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;cAAAE,QAAA,gBAE3D7M,OAAA,CAACR,GAAG;gBAAA0G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBACT;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEH1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMhJ,iBAAiB,CAAC,IAAI,CAAE;YACvCiC,KAAK,EAAE;cACL+F,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdkK,OAAO,EAAE,gBAAgB;cACzBgB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBd,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbC,QAAQ,EAAE,MAAM;cAChBxG,UAAU,EAAE;YACd,CAAE;YACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAAAE,QAAA,gBAE3D7M,OAAA,CAACT,KAAK;cAAA2G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACX;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAED1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM5I,sBAAsB,CAAC,IAAI,CAAE;YAC5C6B,KAAK,EAAE;cACL+F,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdkK,OAAO,EAAE,gBAAgB;cACzBgB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBd,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbC,QAAQ,EAAE,MAAM;cAChBxG,UAAU,EAAE;YACd,CAAE;YACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAAAE,QAAA,gBAE3D7M,OAAA,CAACP,MAAM;cAAAyG,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBACZ;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM7M,qBAAqB,CAAC,IAAI,CAAE;YAC3C8F,KAAK,EAAE;cACL+F,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdkK,OAAO,EAAE,gBAAgB;cACzBgB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBd,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbC,QAAQ,EAAE,MAAM;cAChBxG,UAAU,EAAE;YACd,CAAE;YACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAAAE,QAAA,gBAE3D7M,OAAA,CAACpB,MAAM;cAAAsH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBACZ;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMvM,oBAAoB,CAAC,IAAI,CAAE;YAC1CwF,KAAK,EAAE;cACL+F,UAAU,EAAE,OAAO;cACnBjK,KAAK,EAAE,SAAS;cAChBmL,MAAM,EAAE,mBAAmB;cAC3BjB,OAAO,EAAE,gBAAgB;cACzBgB,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE,SAAS;cACjBd,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbC,QAAQ,EAAE,MAAM;cAChBxG,UAAU,EAAE;YACd,CAAE;YACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,OAAQ;YAAAE,QAAA,gBAEzD7M,OAAA,CAACnB,UAAU;cAAAqH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAChB;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1N,OAAA;QAAK4G,KAAK,EAAE;UACVoG,OAAO,EAAE,MAAM;UACfiB,mBAAmB,EAAE,sCAAsC;UAC3DZ,GAAG,EAAE,QAAQ;UACbF,YAAY,EAAE;QAChB,CAAE;QAAAN,QAAA,EACC,CACC;UAAEzJ,IAAI,EAAE5E,MAAM;UAAE0P,KAAK,EAAE,eAAe;UAAEC,KAAK,EAAEhF,KAAK,CAACL,KAAK,CAACmB,QAAQ,CAAC,CAAC;UAAEvH,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EAC7G;UAAES,IAAI,EAAE3E,aAAa;UAAEyP,KAAK,EAAE,WAAW;UAAEC,KAAK,EAAEhF,KAAK,CAACJ,SAAS,CAACkB,QAAQ,CAAC,CAAC;UAAEvH,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EACpH;UAAES,IAAI,EAAE1E,QAAQ;UAAEwP,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAEhF,KAAK,CAACF,MAAM,CAACgB,QAAQ,CAAC,CAAC;UAAEvH,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EAC9G;UAAES,IAAI,EAAEzE,aAAa;UAAEuP,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAEhF,KAAK,CAACD,OAAO,CAACe,QAAQ,CAAC,CAAC;UAAEvH,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,CACjH,CAACqH,GAAG,CAAC,CAACoE,IAAI,EAAEzH,KAAK,kBAChB3G,OAAA;UAAiB4G,KAAK,EAAE;YACtB+F,UAAU,EAAE,OAAO;YACnBiB,YAAY,EAAE,MAAM;YACpBS,SAAS,EAAE,mCAAmC;YAC9CzB,OAAO,EAAE,QAAQ;YACjBI,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBpG,UAAU,EAAE,qBAAqB;YACjCgH,MAAM,EAAE;UACV,CAAE;UACFC,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC2H,SAAS,GAAG,kBAAmB;UAC1EP,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC2H,SAAS,GAAG,eAAgB;UAAA1B,QAAA,gBAErE7M,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAEyB,IAAI,CAACzL,OAAO;cACxBiK,OAAO,EAAE,SAAS;cAClBgB,YAAY,EAAE,KAAK;cACnBY,WAAW,EAAE;YACf,CAAE;YAAA3B,QAAA,eACA7M,OAAA,CAACoO,IAAI,CAAChL,IAAI;cAACyC,IAAI,EAAE,EAAG;cAACnD,KAAK,EAAE0L,IAAI,CAAC1L;YAAM;cAAAwD,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAG4G,KAAK,EAAE;gBAAElE,KAAK,EAAE,SAAS;gBAAE4K,QAAQ,EAAE,UAAU;gBAAEP,MAAM,EAAE;cAAE,CAAE;cAAAF,QAAA,EAAEuB,IAAI,CAACF;YAAK;cAAAhI,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF1N,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAE7K,KAAK,EAAE0L,IAAI,CAAC1L,KAAK;gBAAEqK,MAAM,EAAE;cAAE,CAAE;cAAAF,QAAA,EAC/EuB,IAAI,CAACD;YAAK;cAAAjI,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GA1BE/G,KAAK;UAAAT,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BV,CACN;MAAC;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1N,OAAA;QAAK4G,KAAK,EAAE;UACVoG,OAAO,EAAE,MAAM;UACfiB,mBAAmB,EAAE,SAAS;UAC9BZ,GAAG,EAAE,MAAM;UACX,4BAA4B,EAAE;YAC5BY,mBAAmB,EAAE;UACvB;QACF,CAAE;QAAApB,QAAA,gBAEA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBAEtE7M,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,OAAO;cACnBiB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9CK,QAAQ,EAAE;YACZ,CAAE;YAAA7B,QAAA,gBACA7M,OAAA;cAAK4G,KAAK,EAAE;gBACV+F,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,OAAO;gBACdkK,OAAO,EAAE,QAAQ;gBACjBI,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBE,QAAQ,EAAE,MAAM;gBAChBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBACA7M,OAAA;gBAAI4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAER,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAAY;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpF1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEK,GAAG,EAAE;gBAAS,CAAE;gBAAAR,QAAA,EAC5C,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC7C,GAAG,CAAC,CAACrB,MAAM,EAAEhC,KAAK,kBAChD3G,OAAA;kBAEE2N,OAAO,EAAEA,CAAA,KAAMjF,kBAAkB,CAACC,MAAM,CAAE;kBAC1C/B,KAAK,EAAE;oBACL+F,UAAU,EAAE5K,YAAY,KAAK4G,MAAM,GAAG,OAAO,GAAG,SAAS;oBACzDjG,KAAK,EAAEX,YAAY,KAAK4G,MAAM,GAAG,SAAS,GAAG,OAAO;oBACpDiE,OAAO,EAAE,aAAa;oBACtBgB,YAAY,EAAE,UAAU;oBACxBC,MAAM,EAAE,MAAM;oBACdP,QAAQ,EAAE,UAAU;oBACpBQ,MAAM,EAAE,SAAS;oBACjBhH,UAAU,EAAE;kBACd,CAAE;kBAAA+F,QAAA,EAEDlE;gBAAM,GAbFhC,KAAK;kBAAAT,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcJ,CACT;cAAC;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEgG,OAAO,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAChC7M,OAAA;gBAAK4G,KAAK,EAAE;kBACVoG,OAAO,EAAE,MAAM;kBACfiB,mBAAmB,EAAE,sCAAsC;kBAC3DZ,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,EACCjE,kBAAkB,CAAC,CAAC,CAACoB,GAAG,CAAC,CAAC5E,MAAM,EAAEuB,KAAK,kBACtC3G,OAAA;kBAEE2N,OAAO,EAAEA,CAAA,KAAMlF,iBAAiB,CAACrD,MAAM,CAAE;kBACzCwB,KAAK,EAAE;oBACLiH,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,QAAQ;oBACtBhB,OAAO,EAAE,MAAM;oBACf9F,UAAU,EAAE,eAAe;oBAC3BgH,MAAM,EAAE;kBACV,CAAE;kBACFC,YAAY,EAAGrG,CAAC,IAAK;oBACnBA,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC+H,WAAW,GAAGvJ,MAAM,CAAC1C,KAAK;oBAChDgF,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC2H,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACFP,YAAY,EAAGtG,CAAC,IAAK;oBACnBA,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC+H,WAAW,GAAG,SAAS;oBAC7CjH,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC2H,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAA1B,QAAA,gBAEF7M,OAAA;oBAAK4G,KAAK,EAAE;sBAAEoG,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEE,YAAY,EAAE;oBAAU,CAAE;oBAAAN,QAAA,gBACxF7M,OAAA;sBAAM4G,KAAK,EAAE;wBACX+F,UAAU,EAAEvH,MAAM,CAACzC,OAAO;wBAC1BD,KAAK,EAAE0C,MAAM,CAAC1C,KAAK;wBACnB4K,QAAQ,EAAE,SAAS;wBACnBV,OAAO,EAAE,gBAAgB;wBACzBgB,YAAY,EAAE;sBAChB,CAAE;sBAAAf,QAAA,EACCzH,MAAM,CAAChD;oBAAO;sBAAA8D,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACP1N,OAAA;sBAAM4G,KAAK,EAAE;wBAAElE,KAAK,EAAE,SAAS;wBAAE4K,QAAQ,EAAE,UAAU;wBAAEN,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEG,GAAG,EAAE;sBAAU,CAAE;sBAAAR,QAAA,gBAC7G7M,OAAA,CAAClB,MAAM;wBAAC+G,IAAI,EAAE;sBAAG;wBAAAK,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,KAAC,EAACtI,MAAM,CAAC3C,MAAM;oBAAA;sBAAAyD,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAxH,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN1N,OAAA;oBAAI4G,KAAK,EAAE;sBAAE2G,UAAU,EAAE,MAAM;sBAAED,QAAQ,EAAE,UAAU;sBAAEH,YAAY,EAAE;oBAAS,CAAE;oBAAAN,QAAA,EAC7EzH,MAAM,CAAC/C;kBAAK;oBAAA6D,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACL1N,OAAA;oBAAG4G,KAAK,EAAE;sBAAElE,KAAK,EAAE,SAAS;sBAAE4K,QAAQ,EAAE,UAAU;sBAAEH,YAAY,EAAE;oBAAO,CAAE;oBAAAN,QAAA,EACxEzH,MAAM,CAAC9C;kBAAW;oBAAA4D,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACJ1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAEoG,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAAL,QAAA,gBACrF7M,OAAA;sBAAK4G,KAAK,EAAE;wBAAEoG,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAAL,QAAA,gBACpD7M,OAAA;wBAAK4G,KAAK,EAAE;0BACVgI,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdjB,YAAY,EAAE,KAAK;0BACnBjB,UAAU,EAAEvH,MAAM,CAACzC,OAAO;0BAC1BqK,OAAO,EAAE,MAAM;0BACfE,UAAU,EAAE,QAAQ;0BACpBD,cAAc,EAAE,QAAQ;0BACxBuB,WAAW,EAAE;wBACf,CAAE;wBAAA3B,QAAA,eACA7M,OAAA,CAACjB,MAAM;0BAAC8G,IAAI,EAAE,EAAG;0BAACnD,KAAK,EAAE0C,MAAM,CAAC1C;wBAAM;0BAAAwD,QAAA,EAAAsH,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAxH,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,eACN1N,OAAA;wBAAM4G,KAAK,EAAE;0BAAE0G,QAAQ,EAAE;wBAAW,CAAE;wBAAAT,QAAA,EAAEzH,MAAM,CAAC7C;sBAAS;wBAAA2D,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAxH,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACN1N,OAAA;sBAAK4G,KAAK,EAAE;wBAAE0G,QAAQ,EAAE,UAAU;wBAAE5K,KAAK,EAAE;sBAAU,CAAE;sBAAAmK,QAAA,EAAEzH,MAAM,CAAC5C;oBAAQ;sBAAA0D,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAxH,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA,GAvDD/G,KAAK;kBAAAT,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwDP,CACN;cAAC;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1N,OAAA;gBACE2N,OAAO,EAAEA,CAAA,KAAM7M,qBAAqB,CAAC,IAAI,CAAE;gBAC3C8F,KAAK,EAAE;kBACLkI,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE,MAAM;kBACbhC,OAAO,EAAE,SAAS;kBAClBiB,MAAM,EAAE,oBAAoB;kBAC5BD,YAAY,EAAE,QAAQ;kBACtBjB,UAAU,EAAE,aAAa;kBACzBjK,KAAK,EAAE,SAAS;kBAChBoL,MAAM,EAAE,SAAS;kBACjBhH,UAAU,EAAE,eAAe;kBAC3BkG,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBI,GAAG,EAAE;gBACP,CAAE;gBACFU,YAAY,EAAGrG,CAAC,IAAK;kBACnBA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+H,WAAW,GAAG,SAAS;kBACtCjH,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAClE,KAAK,GAAG,SAAS;gBAClC,CAAE;gBACFsL,YAAY,EAAGtG,CAAC,IAAK;kBACnBA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+H,WAAW,GAAG,SAAS;kBACtCjH,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAClE,KAAK,GAAG,SAAS;gBAClC,CAAE;gBAAAmK,QAAA,gBAEF7M,OAAA,CAACpB,MAAM;kBAAAsH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBACZ;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1N,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,OAAO;cACnBiB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9CK,QAAQ,EAAE;YACZ,CAAE;YAAA7B,QAAA,gBACA7M,OAAA;cAAK4G,KAAK,EAAE;gBACV+F,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,OAAO;gBACdkK,OAAO,EAAE;cACX,CAAE;cAAAC,QAAA,eACA7M,OAAA;gBAAI4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAER,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAAc;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACN1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEgG,OAAO,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAChC7M,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEyB,aAAa,EAAE,QAAQ;kBAAEpB,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EACnE,CACC;kBACExK,KAAK,EAAE,gCAAgC;kBACvCC,WAAW,EAAE,gDAAgD;kBAC7DY,IAAI,EAAE,mCAAmC;kBACzCC,QAAQ,EAAE,aAAa;kBACvBC,IAAI,EAAEnE,UAAU;kBAChByD,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,EACD;kBACEN,KAAK,EAAE,yBAAyB;kBAChCC,WAAW,EAAE,2CAA2C;kBACxDY,IAAI,EAAE,kCAAkC;kBACxCC,QAAQ,EAAE,aAAa;kBACvBC,IAAI,EAAE1D,QAAQ;kBACdgD,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,EACD;kBACEN,KAAK,EAAE,gCAAgC;kBACvCC,WAAW,EAAE,2BAA2B;kBACxCY,IAAI,EAAE,mCAAmC;kBACzCC,QAAQ,EAAE,cAAc;kBACxBC,IAAI,EAAEzD,KAAK;kBACX+C,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,CACF,CAACqH,GAAG,CAAC,CAAC9B,IAAI,EAAEvB,KAAK,kBAChB3G,OAAA;kBAAiB4G,KAAK,EAAE;oBACtBoG,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,YAAY;oBACxBN,OAAO,EAAE,MAAM;oBACfiB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,QAAQ;oBACtB9G,UAAU,EAAE,eAAe;oBAC3BgH,MAAM,EAAE;kBACV,CAAE;kBACFC,YAAY,EAAGrG,CAAC,IAAK;oBACnBA,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC+F,UAAU,GAAGzE,IAAI,CAACvF,OAAO;oBAC/C+E,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC2H,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACFP,YAAY,EAAGtG,CAAC,IAAK;oBACnBA,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC+F,UAAU,GAAG,aAAa;oBAChDjF,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC2H,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAA1B,QAAA,gBAEA7M,OAAA;oBAAK4G,KAAK,EAAE;sBACV+F,UAAU,EAAEzE,IAAI,CAACvF,OAAO;sBACxBD,KAAK,EAAEwF,IAAI,CAACxF,KAAK;sBACjBkK,OAAO,EAAE,SAAS;sBAClBgB,YAAY,EAAE,QAAQ;sBACtBY,WAAW,EAAE;oBACf,CAAE;oBAAA3B,QAAA,eACA7M,OAAA,CAACkI,IAAI,CAAC9E,IAAI;sBAACyC,IAAI,EAAE;oBAAG;sBAAAK,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAxH,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACN1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAEmI,IAAI,EAAE;oBAAE,CAAE;oBAAAlC,QAAA,gBACtB7M,OAAA;sBAAK4G,KAAK,EAAE;wBAAEoG,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEC,UAAU,EAAE,YAAY;wBAAEC,YAAY,EAAE;sBAAS,CAAE;sBAAAN,QAAA,gBACjH7M,OAAA;wBAAA6M,QAAA,gBACE7M,OAAA;0BAAI4G,KAAK,EAAE;4BAAE2G,UAAU,EAAE,MAAM;4BAAER,MAAM,EAAE,CAAC;4BAAEI,YAAY,EAAE;0BAAU,CAAE;0BAAAN,QAAA,EAAE3E,IAAI,CAAC7F;wBAAK;0BAAA6D,QAAA,EAAAsH,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxF1N,OAAA;0BAAG4G,KAAK,EAAE;4BAAElE,KAAK,EAAE,SAAS;4BAAE4K,QAAQ,EAAE,UAAU;4BAAEP,MAAM,EAAE;0BAAE,CAAE;0BAAAF,QAAA,EAAE3E,IAAI,CAAC5F;wBAAW;0BAAA4D,QAAA,EAAAsH,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAxH,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpF,CAAC,eACN1N,OAAA;wBAAM4G,KAAK,EAAE;0BACX+F,UAAU,EAAEzE,IAAI,CAACvF,OAAO;0BACxBD,KAAK,EAAEwF,IAAI,CAACxF,KAAK;0BACjB4K,QAAQ,EAAE,SAAS;0BACnBV,OAAO,EAAE,gBAAgB;0BACzBgB,YAAY,EAAE;wBAChB,CAAE;wBAAAf,QAAA,EACC3E,IAAI,CAAC/E;sBAAQ;wBAAA+C,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAxH,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN1N,OAAA;sBAAK4G,KAAK,EAAE;wBAAEoG,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEI,QAAQ,EAAE,UAAU;wBAAE5K,KAAK,EAAE;sBAAU,CAAE;sBAAAmK,QAAA,gBAC5F7M,OAAA,CAAChB,UAAU;wBAAC6G,IAAI,EAAE,EAAG;wBAACe,KAAK,EAAE;0BAAE4H,WAAW,EAAE;wBAAS;sBAAE;wBAAAtI,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC1D1N,OAAA;wBAAA6M,QAAA,EAAO3E,IAAI,CAAChF;sBAAI;wBAAAgD,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAxH,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAxH,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA/CE/G,KAAK;kBAAAT,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgDV,CACN;cAAC;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1N,OAAA;gBACE2N,OAAO,EAAEA,CAAA,KAAM3M,mBAAmB,CAAC,IAAI,CAAE;gBACzC4F,KAAK,EAAE;kBACLkI,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE,MAAM;kBACbhC,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBjK,KAAK,EAAE,OAAO;kBACdmL,MAAM,EAAE,MAAM;kBACdD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjBhH,UAAU,EAAE,eAAe;kBAC3BkG,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBI,GAAG,EAAE;gBACP,CAAE;gBACFU,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;gBAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;gBAAAE,QAAA,gBAE3D7M,OAAA,CAACpB,MAAM;kBAAAsH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBACZ;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBAEtE7M,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,OAAO;cACnBiB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9CzB,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,SAAS;gBAAEC,UAAU,EAAE,MAAM;gBAAE7K,KAAK,EAAE,SAAS;gBAAEyK,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,EAAC;YAEhG;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAEE,YAAY,EAAE;cAAS,CAAE;cAAAN,QAAA,eAChF7M,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoI,QAAQ,EAAE,UAAU;kBAAEJ,KAAK,EAAE,OAAO;kBAAEC,MAAM,EAAE;gBAAQ,CAAE;gBAAAhC,QAAA,gBACpE7M,OAAA;kBAAK4G,KAAK,EAAE;oBAAEgI,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE;kBAAO,CAAE;kBAACI,OAAO,EAAC,aAAa;kBAAApC,QAAA,gBAClE7M,OAAA;oBACEkP,EAAE,EAAC,IAAI;oBACPC,EAAE,EAAC,IAAI;oBACPC,CAAC,EAAC,IAAI;oBACNC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,IAAI,EAAC;kBAAa;oBAAArJ,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACF1N,OAAA;oBACEkP,EAAE,EAAC,IAAI;oBACPC,EAAE,EAAC,IAAI;oBACPC,CAAC,EAAC,IAAI;oBACNC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,IAAI,EAAC,aAAa;oBAClBC,eAAe,EAAC,OAAO;oBACvBC,gBAAgB,EAAC,MAAM;oBACvBC,aAAa,EAAC,OAAO;oBACrB9I,KAAK,EAAE;sBAAE2H,SAAS,EAAE,gBAAgB;sBAAEoB,eAAe,EAAE;oBAAU;kBAAE;oBAAAzJ,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1N,OAAA;kBAAK4G,KAAK,EAAE;oBACVoI,QAAQ,EAAE,UAAU;oBACpBY,KAAK,EAAE,CAAC;oBACR5C,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxBwB,aAAa,EAAE;kBACjB,CAAE;kBAAA5B,QAAA,gBACA7M,OAAA;oBAAM4G,KAAK,EAAE;sBAAE0G,QAAQ,EAAE,MAAM;sBAAEC,UAAU,EAAE,MAAM;sBAAE7K,KAAK,EAAE;oBAAU,CAAE;oBAAAmK,QAAA,EAAC;kBAAG;oBAAA3G,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnF1N,OAAA;oBAAM4G,KAAK,EAAE;sBAAElE,KAAK,EAAE,SAAS;sBAAE4K,QAAQ,EAAE;oBAAW,CAAE;oBAAAT,QAAA,EAAC;kBAAO;oBAAA3G,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEyB,aAAa,EAAE,QAAQ;gBAAEpB,GAAG,EAAE;cAAU,CAAE;cAAAR,QAAA,EACtE,CACC;gBAAElH,IAAI,EAAE,iBAAiB;gBAAE9C,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAC9D;gBAAEiD,IAAI,EAAE,gBAAgB;gBAAE9C,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAC7D;gBAAEiD,IAAI,EAAE,mBAAmB;gBAAE9C,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAChE;gBAAEiD,IAAI,EAAE,eAAe;gBAAE9C,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,CAC7D,CAACsH,GAAG,CAAC,CAAC5H,OAAO,EAAEuE,KAAK,kBACnB3G,OAAA;gBAAiB4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAJ,QAAA,gBACjG7M,OAAA;kBAAK4G,KAAK,EAAE;oBAAEoG,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAL,QAAA,gBACpD7M,OAAA;oBAAK4G,KAAK,EAAE;sBACVgI,KAAK,EAAE,SAAS;sBAChBC,MAAM,EAAE,SAAS;sBACjBjB,YAAY,EAAE,KAAK;sBACnBjB,UAAU,EAAEvK,OAAO,CAACM,KAAK;sBACzB8L,WAAW,EAAE;oBACf;kBAAE;oBAAAtI,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACL1N,OAAA;oBAAA6M,QAAA,EAAOzK,OAAO,CAACuD;kBAAI;oBAAAO,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN1N,OAAA;kBAAM4G,KAAK,EAAE;oBAAE2G,UAAU,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAEzK,OAAO,CAACS;gBAAQ;kBAAAqD,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAXtD/G,KAAK;gBAAAT,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYV,CACN;YAAC;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1N,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,OAAO;cACnBiB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9CzB,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA7M,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,gBAC3G7M,OAAA;gBAAI4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAE7K,KAAK,EAAE,SAAS;kBAAEqK,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAErF;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1N,OAAA;gBAAQ4G,KAAK,EAAE;kBACb+F,UAAU,EAAE,MAAM;kBAClBkB,MAAM,EAAE,MAAM;kBACdnL,KAAK,EAAE,SAAS;kBAChBoL,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,eACA7M,OAAA,CAACX,WAAW;kBAAA6G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN1N,OAAA;cAAK4G,KAAK,EAAE;gBACViI,MAAM,EAAE,OAAO;gBACflC,UAAU,EAAE,mDAAmD;gBAC/DiB,YAAY,EAAE,MAAM;gBACpBoB,QAAQ,EAAE,UAAU;gBACpBN,QAAQ,EAAE;cACZ,CAAE;cAAA7B,QAAA,gBAEA7M,OAAA;gBACE6P,SAAS,EAAC,cAAc;gBACxBjJ,KAAK,EAAE;kBACLoI,QAAQ,EAAE,UAAU;kBACpBc,GAAG,EAAE,KAAK;kBACVC,IAAI,EAAE,KAAK;kBACXxB,SAAS,EAAE,uBAAuB;kBAClC5B,UAAU,EAAE,SAAS;kBACrBjK,KAAK,EAAE,OAAO;kBACdkL,YAAY,EAAE,QAAQ;kBACtBhB,OAAO,EAAE,aAAa;kBACtByB,SAAS,EAAE,mCAAmC;kBAC9Cd,UAAU,EAAE,KAAK;kBACjBO,MAAM,EAAE,SAAS;kBACjBhH,UAAU,EAAE,eAAe;kBAC3B+G,MAAM,EAAE;gBACV,CAAE;gBAAAhB,QAAA,EACH;cAED;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAGL,CACC;gBAAEjN,IAAI,EAAE,iBAAiB;gBAAEqP,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EACpD;gBAAEtP,IAAI,EAAE,YAAY;gBAAEqP,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC/C;gBAAEtP,IAAI,EAAE,WAAW;gBAAEqP,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC9C;gBAAEtP,IAAI,EAAE,OAAO;gBAAEqP,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC1C;gBAAEtP,IAAI,EAAE,YAAY;gBAAEqP,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC/C;gBAAEtP,IAAI,EAAE,eAAe;gBAAEqP,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,CACnD,CAAC/F,GAAG,CAAC,CAACtD,IAAI,EAAEC,KAAK,kBAChB3G,OAAA;gBAAA6M,QAAA,gBACE7M,OAAA;kBACE6P,SAAS,EAAC,cAAc;kBACxBjJ,KAAK,EAAE;oBACLoI,QAAQ,EAAE,UAAU;oBACpBc,GAAG,EAAEpJ,IAAI,CAACoJ,GAAG;oBACbC,IAAI,EAAErJ,IAAI,CAACqJ,IAAI;oBACfpD,UAAU,EAAE,OAAO;oBACnBiB,YAAY,EAAE,QAAQ;oBACtBhB,OAAO,EAAE,aAAa;oBACtByB,SAAS,EAAE,mCAAmC;oBAC9Cd,UAAU,EAAE,KAAK;oBACjBO,MAAM,EAAE,SAAS;oBACjBhH,UAAU,EAAE,eAAe;oBAC3B+G,MAAM,EAAE,uBAAuB;oBAC/BU,SAAS,EAAE;kBACb,CAAE;kBACFR,YAAY,EAAGrG,CAAC,IAAK;oBACnBA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC2H,SAAS,GAAG,mCAAmC;oBAC9D7G,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+H,WAAW,GAAG,SAAS;kBACxC,CAAE;kBACFX,YAAY,EAAGtG,CAAC,IAAK;oBACnBA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC2H,SAAS,GAAG,gCAAgC;oBAC3D7G,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+H,WAAW,GAAG,aAAa;kBAC5C,CAAE;kBAAA9B,QAAA,EAEDnG,IAAI,CAACjG;gBAAI;kBAAAyF,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eAEN1N,OAAA;kBAAK4G,KAAK,EAAE;oBACVoI,QAAQ,EAAE,UAAU;oBACpBrC,UAAU,EAAE,SAAS;oBACrBkC,MAAM,EAAE,KAAK;oBACbD,KAAK,EAAE,OAAO;oBACdkB,GAAG,EAAEpJ,IAAI,CAACoJ,GAAG;oBACbC,IAAI,EAAErJ,IAAI,CAACqJ,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;oBACzCJ,eAAe,EAAE,aAAa;oBAC9BpB,SAAS,EAAE7H,IAAI,CAACqJ,IAAI,KAAK,KAAK,GAC3BrJ,IAAI,CAACoJ,GAAG,KAAK,KAAK,GAAG,iCAAiC,GACtDpJ,IAAI,CAACoJ,GAAG,KAAK,KAAK,GAAG,kBAAkB,GACvC,gCAAgC,GAChCpJ,IAAI,CAACoJ,GAAG,KAAK,KAAK,GAAG,gCAAgC,GACrDpJ,IAAI,CAACoJ,GAAG,KAAK,KAAK,GAAG,kBAAkB,GACvC;kBACL;gBAAE;kBAAA5J,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GA5CG/G,KAAK;gBAAAT,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6CV,CACN,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEkI,SAAS,EAAE,MAAM;gBAAE9B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAgB,CAAE;cAAAJ,QAAA,gBAClF7M,OAAA;gBAAQ4G,KAAK,EAAE;kBACb+F,UAAU,EAAE,MAAM;kBAClBkB,MAAM,EAAE,MAAM;kBACdnL,KAAK,EAAE,SAAS;kBAChBoL,MAAM,EAAE,SAAS;kBACjBd,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBACA7M,OAAA,CAACpB,MAAM;kBAACiH,IAAI,EAAE;gBAAG;kBAAAK,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aACtB;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1N,OAAA;gBAAQ4G,KAAK,EAAE;kBACb+F,UAAU,EAAE,MAAM;kBAClBkB,MAAM,EAAE,MAAM;kBACdnL,KAAK,EAAE,SAAS;kBAChBoL,MAAM,EAAE,SAAS;kBACjBd,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBACA7M,OAAA,CAACV,MAAM;kBAACuG,IAAI,EAAE;gBAAG;kBAAAK,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SACtB;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1N,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,OAAO;cACnBiB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9CzB,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,SAAS;gBAAEC,UAAU,EAAE,MAAM;gBAAE7K,KAAK,EAAE,SAAS;gBAAEyK,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,EAAC;YAEhG;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1N,OAAA;cAAK4G,KAAK,EAAE;gBACVoG,OAAO,EAAE,MAAM;gBACfiB,mBAAmB,EAAE,gBAAgB;gBACrCZ,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,EACC,CACC;gBAAEzJ,IAAI,EAAElE,UAAU;gBAAEgP,KAAK,EAAE,iBAAiB;gBAAExL,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE,SAAS;gBAAEqN,MAAM,EAAEA,CAAA,KAAM1O,0BAA0B,CAAC,IAAI;cAAE,CAAC,EACpI;gBAAE8B,IAAI,EAAEjE,WAAW;gBAAE+O,KAAK,EAAE,aAAa;gBAAExL,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE,SAAS;gBAAEqN,MAAM,EAAEA,CAAA,KAAMxO,uBAAuB,CAAC,IAAI;cAAE,CAAC,EAC9H;gBAAE4B,IAAI,EAAEhE,YAAY;gBAAE8O,KAAK,EAAE,QAAQ;gBAAExL,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE,SAAS;gBAAEqN,MAAM,EAAEA,CAAA,KAAMtO,kBAAkB,CAAC,IAAI;cAAE,CAAC,EACrH;gBAAE0B,IAAI,EAAEpE,UAAU;gBAAEkP,KAAK,EAAE,UAAU;gBAAExL,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE,SAAS;gBAAEqN,MAAM,EAAEA,CAAA,KAAMpO,oBAAoB,CAAC,IAAI;cAAE,CAAC,CACxH,CAACoI,GAAG,CAAC,CAACgG,MAAM,EAAErJ,KAAK,kBAClB3G,OAAA;gBAEE2N,OAAO,EAAEqC,MAAM,CAACA,MAAO;gBACvBpJ,KAAK,EAAE;kBACL+F,UAAU,EAAEqD,MAAM,CAACrN,OAAO;kBAC1BD,KAAK,EAAEsN,MAAM,CAACtN,KAAK;kBACnBkK,OAAO,EAAE,SAAS;kBAClBgB,YAAY,EAAE,QAAQ;kBACtBC,MAAM,EAAE,MAAM;kBACdC,MAAM,EAAE,SAAS;kBACjBd,OAAO,EAAE,MAAM;kBACfyB,aAAa,EAAE,QAAQ;kBACvBvB,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE,QAAQ;kBACbvG,UAAU,EAAE;gBACd,CAAE;gBACFiH,YAAY,EAAGrG,CAAC,IAAK;kBACnBA,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC2H,SAAS,GAAG,kBAAkB;kBACpD7G,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAACyH,SAAS,GAAG,mCAAmC;gBACvE,CAAE;gBACFL,YAAY,EAAGtG,CAAC,IAAK;kBACnBA,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAAC2H,SAAS,GAAG,eAAe;kBACjD7G,CAAC,CAAC4G,aAAa,CAAC1H,KAAK,CAACyH,SAAS,GAAG,MAAM;gBAC1C,CAAE;gBAAAxB,QAAA,gBAEF7M,OAAA,CAACgQ,MAAM,CAAC5M,IAAI;kBAACyC,IAAI,EAAE;gBAAG;kBAAAK,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzB1N,OAAA;kBAAM4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE;kBAAW,CAAE;kBAAAT,QAAA,EAAEmD,MAAM,CAAC9B;gBAAK;kBAAAhI,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAzBvD/G,KAAK;gBAAAT,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BJ,CACT;YAAC;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1N,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBiB,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACblD,OAAO,EAAE,MAAM;QACfyB,aAAa,EAAE,QAAQ;QACvBvB,UAAU,EAAE,UAAU;QACtBG,GAAG,EAAE,MAAM;QACX8C,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,gBAEA7M,OAAA;QACE2N,OAAO,EAAEA,CAAA,KAAMrN,cAAc,CAAC,CAACD,WAAW,CAAE;QAC5CuG,KAAK,EAAE;UACLgI,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE,QAAQ;UAChBjB,YAAY,EAAE,KAAK;UACnBjB,UAAU,EAAE,SAAS;UACrBjK,KAAK,EAAE,OAAO;UACdmL,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,SAAS;UACjBd,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBoB,SAAS,EAAE,mCAAmC;UAC9CvH,UAAU,EAAE,eAAe;UAC3BsJ,SAAS,EAAE;QACb,CAAE;QACFrC,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;QAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;QAAAE,QAAA,eAE3D7M,OAAA,CAACT,KAAK;UAACsG,IAAI,EAAE;QAAG;UAAAK,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,EAGRrN,WAAW,iBACVL,OAAA;QAAK4G,KAAK,EAAE;UACVgI,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACflC,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,SAAS;UACvBS,SAAS,EAAE,gCAAgC;UAC3CK,QAAQ,EAAE,QAAQ;UAClB1B,OAAO,EAAE,MAAM;UACfyB,aAAa,EAAE;QACjB,CAAE;QAAA5B,QAAA,gBAEA7M,OAAA;UAAK4G,KAAK,EAAE;YACV+F,UAAU,EAAE,SAAS;YACrBjK,KAAK,EAAE,OAAO;YACdkK,OAAO,EAAE,cAAc;YACvBW,UAAU,EAAE,MAAM;YAClBP,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE;UACd,CAAE;UAAAL,QAAA,gBACA7M,OAAA;YAAA6M,QAAA,EAAM;UAAe;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5B1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMrN,cAAc,CAAC,KAAK,CAAE;YACrCsG,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,OAAO;cACdoL,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN1N,OAAA;UAAK4G,KAAK,EAAE;YACVmI,IAAI,EAAE,CAAC;YACPnC,OAAO,EAAE,MAAM;YACfyD,SAAS,EAAE,MAAM;YACjB1D,UAAU,EAAE;UACd,CAAE;UAAAE,QAAA,EACCtM,YAAY,CAACyJ,GAAG,CAAC,CAACsG,OAAO,EAAE3J,KAAK,kBAC/B3G,OAAA;YAAiB4G,KAAK,EAAE;cACtBuG,YAAY,EAAE,MAAM;cACpBH,OAAO,EAAE,MAAM;cACfC,cAAc,EAAEqD,OAAO,CAAC5P,MAAM,GAAG,UAAU,GAAG;YAChD,CAAE;YAAAmM,QAAA,eACA7M,OAAA;cAAK4G,KAAK,EAAE;gBACV+F,UAAU,EAAE2D,OAAO,CAAC5P,MAAM,GAAG,SAAS,GAAG,SAAS;gBAClDgC,KAAK,EAAE4N,OAAO,CAAC5P,MAAM,GAAG,OAAO,GAAG,SAAS;gBAC3CkM,OAAO,EAAE,SAAS;gBAClBgB,YAAY,EAAE,QAAQ;gBACtBd,QAAQ,EAAE;cACZ,CAAE;cAAAD,QAAA,EACCyD,OAAO,CAAC7P;YAAI;cAAAyF,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAbE/G,KAAK;YAAAT,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACN;QAAC;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1N,OAAA;UAAK4G,KAAK,EAAE;YACVoG,OAAO,EAAE,MAAM;YACfJ,OAAO,EAAE,SAAS;YAClB2D,SAAS,EAAE,mBAAmB;YAC9B5D,UAAU,EAAE;UACd,CAAE;UAAAE,QAAA,gBACA7M,OAAA;YACE4F,IAAI,EAAC,MAAM;YACXuI,KAAK,EAAExN,SAAU;YACjB6P,QAAQ,EAAG9I,CAAC,IAAK9G,YAAY,CAAC8G,CAAC,CAAC6B,MAAM,CAAC4E,KAAK,CAAE;YAC9CsC,UAAU,EAAEhJ,cAAe;YAC3BiJ,WAAW,EAAC,uBAAuB;YACnC9J,KAAK,EAAE;cACLmI,IAAI,EAAE,CAAC;cACPnC,OAAO,EAAE,gBAAgB;cACzBiB,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,SAAS;cACvB+C,OAAO,EAAE;YACX;UAAE;YAAAzK,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF1N,OAAA;YACE2N,OAAO,EAAE3G,cAAe;YACxBJ,KAAK,EAAE;cACLgK,UAAU,EAAE,QAAQ;cACpBjE,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdmL,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,SAAS;cACvBhB,OAAO,EAAE,aAAa;cACtBkB,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,eAEF7M,OAAA,CAACP,MAAM;cAAAyG,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL7M,kBAAkB,iBACjBb,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE,OAAO;UACjB+D,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAxD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM7M,qBAAqB,CAAC,KAAK,CAAE;YAC5C8F,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACpE7M,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAc;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnG1N,OAAA;cACE4F,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAE7K,SAAS,CAACjB,KAAM;cACvBmO,QAAQ,EAAG9I,CAAC,IAAKnE,YAAY,CAAC2D,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7E,KAAK,EAAEqF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAC5EuC,WAAW,EAAC,oCAAoC;cAChD9J,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ;YAAE;cAAApH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAS;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9F1N,OAAA;cACE4F,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAE7K,SAAS,CAAClB,OAAQ;cACzBoO,QAAQ,EAAG9I,CAAC,IAAKnE,YAAY,CAAC2D,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE9E,OAAO,EAAEsF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAC9EuC,WAAW,EAAC,wBAAwB;cACpC9J,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ;YAAE;cAAApH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG1N,OAAA;cACE4F,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAE7K,SAAS,CAACf,SAAU;cAC3BiO,QAAQ,EAAG9I,CAAC,IAAKnE,YAAY,CAAC2D,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE3E,SAAS,EAAEmF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAChFuC,WAAW,EAAC,mBAAmB;cAC/B9J,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ;YAAE;cAAApH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG1N,OAAA;cACEmO,KAAK,EAAE7K,SAAS,CAAChB,WAAY;cAC7BkO,QAAQ,EAAG9I,CAAC,IAAKnE,YAAY,CAAC2D,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5E,WAAW,EAAEoF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAClFuC,WAAW,EAAC,uBAAuB;cACnCI,IAAI,EAAE,CAAE;cACRlK,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE,MAAM;gBAChByD,MAAM,EAAE;cACV;YAAE;cAAA7K,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAQ;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7F1N,OAAA;cACE4F,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAE7K,SAAS,CAACd,QAAS;cAC1BgO,QAAQ,EAAG9I,CAAC,IAAKnE,YAAY,CAAC2D,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1E,QAAQ,EAAEkF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAC/EuC,WAAW,EAAC,gBAAgB;cAC5B9J,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ;YAAE;cAAApH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEK,GAAG,EAAE;cAAS,CAAE;cAAAR,QAAA,EAC5C,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC7C,GAAG,CAACtH,KAAK,iBAC3E1C,OAAA;gBAEE2N,OAAO,EAAEA,CAAA,KAAMpK,YAAY,CAAC2D,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAExE;gBAAM,CAAC,CAAC,CAAE;gBAC1DkE,KAAK,EAAE;kBACLgI,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdjB,YAAY,EAAE,KAAK;kBACnBjB,UAAU,EAAEjK,KAAK;kBACjBmL,MAAM,EAAEvK,SAAS,CAACZ,KAAK,KAAKA,KAAK,GAAG,gBAAgB,GAAG,mBAAmB;kBAC1EoL,MAAM,EAAE;gBACV;cAAE,GATGpL,KAAK;gBAAAwD,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUX,CACF;YAAC;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE,MAAM;YAAEyB,SAAS,EAAE;UAAO,CAAE;UAAAjC,QAAA,gBAC9D7M,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM7M,qBAAqB,CAAC,KAAK,CAAE;YAC5C8F,KAAK,EAAE;cACLmI,IAAI,EAAE,CAAC;cACPnC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,OAAO;cACnBjK,KAAK,EAAE,SAAS;cAChBmL,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,EACH;UAED;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1N,OAAA;YACE2N,OAAO,EAAE/F,eAAgB;YACzBhB,KAAK,EAAE;cACLmI,IAAI,EAAE,CAAC;cACPnC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdmL,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,EACH;UAED;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA3M,gBAAgB,iBACff,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE;QACZ,CAAE;QAAAD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM3M,mBAAmB,CAAC,KAAK,CAAE;YAC1C4F,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACpE7M,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAY;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjG1N,OAAA;cACE4F,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAE3K,OAAO,CAACnB,KAAM;cACrBmO,QAAQ,EAAG9I,CAAC,IAAKjE,UAAU,CAACyD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7E,KAAK,EAAEqF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAC1EuC,WAAW,EAAC,sCAAsC;cAClD9J,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ;YAAE;cAAApH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAQ;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7F1N,OAAA;cACEmO,KAAK,EAAE3K,OAAO,CAACH,QAAS;cACxBmN,QAAQ,EAAG9I,CAAC,IAAKjE,UAAU,CAACyD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7D,QAAQ,EAAEqE,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAC7EvH,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ,CAAE;cAAAT,QAAA,gBAEF7M,OAAA;gBAAQmO,KAAK,EAAC,EAAE;gBAAAtB,QAAA,EAAC;cAAe;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCzL,OAAO,CAAC+H,GAAG,CAAC5E,MAAM,iBACjBpF,OAAA;gBAAwBmO,KAAK,EAAE/I,MAAM,CAACjD,EAAG;gBAAA0K,QAAA,EAAEzH,MAAM,CAAC/C;cAAK,GAA1C+C,MAAM,CAACjD,EAAE;gBAAA+D,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA0C,CACjE,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG1N,OAAA;cACE4F,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAE3K,OAAO,CAACN,IAAK;cACpBsN,QAAQ,EAAG9I,CAAC,IAAKjE,UAAU,CAACyD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEhE,IAAI,EAAEwE,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cACzEvH,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ;YAAE;cAAApH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG1N,OAAA;cACEmO,KAAK,EAAE3K,OAAO,CAAClB,WAAY;cAC3BkO,QAAQ,EAAG9I,CAAC,IAAKjE,UAAU,CAACyD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5E,WAAW,EAAEoF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAChFuC,WAAW,EAAC,8BAA8B;cAC1CI,IAAI,EAAE,CAAE;cACRlK,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE,MAAM;gBAChByD,MAAM,EAAE;cACV;YAAE;cAAA7K,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE,MAAM;YAAEyB,SAAS,EAAE;UAAO,CAAE;UAAAjC,QAAA,gBAC9D7M,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM3M,mBAAmB,CAAC,KAAK,CAAE;YAC1C4F,KAAK,EAAE;cACLmI,IAAI,EAAE,CAAC;cACPnC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,OAAO;cACnBjK,KAAK,EAAE,SAAS;cAChBmL,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,EACH;UAED;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1N,OAAA;YACE2N,OAAO,EAAE1F,aAAc;YACvBrB,KAAK,EAAE;cACLmI,IAAI,EAAE,CAAC;cACPnC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdmL,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,EACH;UAED;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAvM,iBAAiB,iBAChBnB,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE,OAAO;UACjB+D,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAxD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMvM,oBAAoB,CAAC,KAAK,CAAE;YAC3CwF,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACpE7M,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,MAAM;gBAAEzK,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,EAAC;YAE5F;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEyB,aAAa,EAAE,QAAQ;gBAAEpB,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,gBACpE7M,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACrF7M,OAAA;kBAAA6M,QAAA,EAAM;gBAAmB;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChC1N,OAAA;kBAAQ4G,KAAK,EAAE;oBACb+F,UAAU,EAAE,SAAS;oBACrBjK,KAAK,EAAE,OAAO;oBACdkK,OAAO,EAAE,iBAAiB;oBAC1BgB,YAAY,EAAE,MAAM;oBACpBC,MAAM,EAAE,MAAM;oBACdC,MAAM,EAAE,SAAS;oBACjBR,QAAQ,EAAE;kBACZ,CAAE;kBAAAT,QAAA,EAAC;gBAEH;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACrF7M,OAAA;kBAAA6M,QAAA,EAAM;gBAAS;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtB1N,OAAA;kBAAQ4G,KAAK,EAAE;oBACb+F,UAAU,EAAE,SAAS;oBACrBjK,KAAK,EAAE,SAAS;oBAChBkK,OAAO,EAAE,iBAAiB;oBAC1BgB,YAAY,EAAE,MAAM;oBACpBC,MAAM,EAAE,MAAM;oBACdC,MAAM,EAAE,SAAS;oBACjBR,QAAQ,EAAE;kBACZ,CAAE;kBAAAT,QAAA,EAAC;gBAEH;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACrF7M,OAAA;kBAAA6M,QAAA,EAAM;gBAAkB;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/B1N,OAAA;kBAAQ4G,KAAK,EAAE;oBACb+F,UAAU,EAAE,SAAS;oBACrBjK,KAAK,EAAE,OAAO;oBACdkK,OAAO,EAAE,iBAAiB;oBAC1BgB,YAAY,EAAE,MAAM;oBACpBC,MAAM,EAAE,MAAM;oBACdC,MAAM,EAAE,SAAS;oBACjBR,QAAQ,EAAE;kBACZ,CAAE;kBAAAT,QAAA,EAAC;gBAEH;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,MAAM;gBAAEzK,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,EAAC;YAE5F;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEyB,aAAa,EAAE,QAAQ;gBAAEpB,GAAG,EAAE;cAAU,CAAE;cAAAR,QAAA,gBACvE7M,OAAA;gBAAQ4G,KAAK,EAAE;kBACbgG,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBjK,KAAK,EAAE,SAAS;kBAChBmL,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjBkD,SAAS,EAAE;gBACb,CAAE;gBAAAnE,QAAA,EAAC;cAEH;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1N,OAAA;gBAAQ4G,KAAK,EAAE;kBACbgG,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBjK,KAAK,EAAE,SAAS;kBAChBmL,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjBkD,SAAS,EAAE;gBACb,CAAE;gBAAAnE,QAAA,EAAC;cAEH;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1N,OAAA;gBAAQ4G,KAAK,EAAE;kBACbgG,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBjK,KAAK,EAAE,SAAS;kBAChBmL,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjBkD,SAAS,EAAE;gBACb,CAAE;gBAAAnE,QAAA,EAAC;cAEH;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,UAAU;YAAE6B,SAAS,EAAE;UAAO,CAAE;UAAAjC,QAAA,eAC7E7M,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMvM,oBAAoB,CAAC,KAAK,CAAE;YAC3CwF,KAAK,EAAE;cACLgG,OAAO,EAAE,gBAAgB;cACzBD,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdmL,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,EACH;UAED;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGArM,uBAAuB,iBACtBrB,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE,OAAO;UACjB+D,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAxD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMrM,0BAA0B,CAAC,KAAK,CAAE;YACjDsF,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELtJ,OAAO,iBACNpE,OAAA;UAAK4G,KAAK,EAAE;YAAEuG,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,eACrC7M,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMhK,kBAAkB,CAAC,IAAI,CAAE;YACxCiD,KAAK,EAAE;cACL+F,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdkK,OAAO,EAAE,gBAAgB;cACzBgB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBd,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbvG,UAAU,EAAE;YACd,CAAE;YACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;YAAAE,QAAA,gBAE3D7M,OAAA,CAACpB,MAAM;cAAAsH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qCACZ;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEA,CAACtJ,OAAO,iBACPpE,OAAA;UAAK4G,KAAK,EAAE;YACVuG,YAAY,EAAE,QAAQ;YACtBR,UAAU,EAAE,SAAS;YACrBC,OAAO,EAAE,MAAM;YACfgB,YAAY,EAAE,QAAQ;YACtBC,MAAM,EAAE;UACV,CAAE;UAAAhB,QAAA,eACA7M,OAAA;YAAG4G,KAAK,EAAE;cAAEmG,MAAM,EAAE,CAAC;cAAEO,QAAQ,EAAE,UAAU;cAAE5K,KAAK,EAAE;YAAU,CAAE;YAAAmK,QAAA,gBAC9D7M,OAAA;cAAA6M,QAAA,EAAQ;YAAqB;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,qEACxC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAGD1N,OAAA;UAAK4G,KAAK,EAAE;YAAEuG,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC7M,OAAA;YAAI4G,KAAK,EAAE;cACT0G,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,GAAG;cACfJ,YAAY,EAAE,MAAM;cACpBzK,KAAK,EAAE,SAAS;cAChBsK,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBACA7M,OAAA,CAACd,UAAU;cAAAgH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4CAChB;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YAAK4G,KAAK,EAAE;cAAEoG,OAAO,EAAE,MAAM;cAAEyB,aAAa,EAAE,QAAQ;cAAEpB,GAAG,EAAE;YAAU,CAAE;YAAAR,QAAA,EACtEpH,eAAe,CAACuE,GAAG,CAAC,CAACO,QAAQ,EAAE0G,GAAG,kBACjCjR,OAAA;cAAuB4G,KAAK,EAAE;gBAC5BoG,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBN,OAAO,EAAE,MAAM;gBACfD,UAAU,EAAE,SAAS;gBACrBiB,YAAY,EAAE,QAAQ;gBACtBC,MAAM,EAAE,mBAAmB;gBAC3BQ,SAAS,EAAE;cACb,CAAE;cAAAxB,QAAA,gBACA7M,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEG,GAAG,EAAE,MAAM;kBAAE0B,IAAI,EAAE;gBAAE,CAAE;gBAAAlC,QAAA,gBAC1E7M,OAAA;kBAAK4G,KAAK,EAAE;oBACV+F,UAAU,EAAE,SAAS;oBACrBjK,KAAK,EAAE,OAAO;oBACdkK,OAAO,EAAE,SAAS;oBAClBgB,YAAY,EAAE,QAAQ;oBACtBZ,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAAJ,QAAA,eACA7M,OAAA,CAACf,UAAU;oBAAC4G,IAAI,EAAE;kBAAG;oBAAAK,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACN1N,OAAA;kBAAK4G,KAAK,EAAE;oBAAEmI,IAAI,EAAE;kBAAE,CAAE;kBAAAlC,QAAA,gBACtB7M,OAAA;oBAAK4G,KAAK,EAAE;sBAAE2G,UAAU,EAAE,GAAG;sBAAED,QAAQ,EAAE,MAAM;sBAAEH,YAAY,EAAE;oBAAU,CAAE;oBAAAN,QAAA,EACxEtC,QAAQ,CAAC5E;kBAAI;oBAAAO,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACN1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAE0G,QAAQ,EAAE,UAAU;sBAAE5K,KAAK,EAAE,SAAS;sBAAEyK,YAAY,EAAE;oBAAU,CAAE;oBAAAN,QAAA,EAC7EtC,QAAQ,CAACjI;kBAAW;oBAAA4D,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACN1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAE0G,QAAQ,EAAE,SAAS;sBAAE5K,KAAK,EAAE;oBAAU,CAAE;oBAAAmK,QAAA,GACnDtC,QAAQ,CAAC1E,IAAI,EAAC,UAAG,EAAC0E,QAAQ,CAACxE,QAAQ,EAAC,UAAG,EAACwE,QAAQ,CAACvE,SAAS,EAAC,8BAAuB,EAACuE,QAAQ,CAACzE,UAAU;kBAAA;oBAAAI,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpG,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEK,GAAG,EAAE,QAAQ;kBAAEH,UAAU,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACnE7M,OAAA;kBACE2N,OAAO,EAAEA,CAAA,KAAMlC,oBAAoB,CAAClB,QAAQ,CAAE;kBAC9C3D,KAAK,EAAE;oBACL+F,UAAU,EAAE,SAAS;oBACrBjK,KAAK,EAAE,OAAO;oBACdmL,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,SAAS;oBAClBgB,YAAY,EAAE,QAAQ;oBACtBE,MAAM,EAAE,SAAS;oBACjBd,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxBnG,UAAU,EAAE;kBACd,CAAE;kBACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;kBAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;kBAC3DtK,KAAK,EAAC,cAAc;kBAAAwK,QAAA,eAEpB7M,OAAA,CAACJ,UAAU;oBAACiG,IAAI,EAAE;kBAAG;oBAAAK,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,EACRtJ,OAAO,iBACNpE,OAAA;kBACE2N,OAAO,EAAEA,CAAA,KAAM7B,0BAA0B,CAACvB,QAAQ,CAACpI,EAAE,CAAE;kBACvDyE,KAAK,EAAE;oBACL+F,UAAU,EAAE,SAAS;oBACrBjK,KAAK,EAAE,OAAO;oBACdmL,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,SAAS;oBAClBgB,YAAY,EAAE,QAAQ;oBACtBE,MAAM,EAAE,SAAS;oBACjBd,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxBnG,UAAU,EAAE;kBACd,CAAE;kBACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;kBAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;kBAC3DtK,KAAK,EAAC,yBAAyB;kBAAAwK,QAAA,eAE/B7M,OAAA,CAACH,QAAQ;oBAACgG,IAAI,EAAE;kBAAG;oBAAAK,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACT;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GA7EEnD,QAAQ,CAACpI,EAAE;cAAA+D,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8EhB,CACN;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1N,OAAA;UAAA6M,QAAA,gBACE7M,OAAA;YAAI4G,KAAK,EAAE;cACT0G,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,GAAG;cACfJ,YAAY,EAAE,MAAM;cACpBzK,KAAK,EAAE,SAAS;cAChBsK,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBACA7M,OAAA,CAACxB,MAAM;cAAA0H,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBACZ;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YAAK4G,KAAK,EAAE;cAAEoG,OAAO,EAAE,MAAM;cAAEyB,aAAa,EAAE,QAAQ;cAAEpB,GAAG,EAAE;YAAO,CAAE;YAAAR,QAAA,EACnE5K,OAAO,CAAC+H,GAAG,CAAC5E,MAAM,iBACjBpF,OAAA;cAAqB4G,KAAK,EAAE;gBAC1BiH,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBhB,OAAO,EAAE;cACX,CAAE;cAAAC,QAAA,gBACA7M,OAAA;gBAAI4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,QAAQ;kBAAEC,UAAU,EAAE,GAAG;kBAAEJ,YAAY,EAAE,QAAQ;kBAAEzK,KAAK,EAAE0C,MAAM,CAAC1C;gBAAM,CAAE;gBAAAmK,QAAA,EAC7FzH,MAAM,CAAC/C;cAAK;gBAAA6D,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACP1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEyB,aAAa,EAAE,QAAQ;kBAAEpB,GAAG,EAAE;gBAAS,CAAE;gBAAAR,QAAA,EACrEzH,MAAM,CAACtC,SAAS,IAAIsC,MAAM,CAACtC,SAAS,CAAC0E,MAAM,GAAG,CAAC,GAAGpC,MAAM,CAACtC,SAAS,CAACkH,GAAG,CAAC,CAACO,QAAQ,EAAE0G,GAAG,kBACpFjR,OAAA;kBAAe4G,KAAK,EAAE;oBACpBoG,OAAO,EAAE,MAAM;oBACfC,cAAc,EAAE,eAAe;oBAC/BC,UAAU,EAAE,QAAQ;oBACpBN,OAAO,EAAE,SAAS;oBAClBD,UAAU,EAAE,SAAS;oBACrBiB,YAAY,EAAE,QAAQ;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAAhB,QAAA,gBACA7M,OAAA;oBAAK4G,KAAK,EAAE;sBAAEoG,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEG,GAAG,EAAE,SAAS;sBAAE0B,IAAI,EAAE;oBAAE,CAAE;oBAAAlC,QAAA,gBAC7E7M,OAAA;sBAAK4G,KAAK,EAAE;wBACV+F,UAAU,EAAE,SAAS;wBACrBjK,KAAK,EAAE,OAAO;wBACdkK,OAAO,EAAE,QAAQ;wBACjBgB,YAAY,EAAE,SAAS;wBACvBZ,OAAO,EAAE,MAAM;wBACfE,UAAU,EAAE,QAAQ;wBACpBD,cAAc,EAAE;sBAClB,CAAE;sBAAAJ,QAAA,eACA7M,OAAA,CAACf,UAAU;wBAAC4G,IAAI,EAAE;sBAAG;wBAAAK,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAxH,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACN1N,OAAA;sBAAA6M,QAAA,gBACE7M,OAAA;wBAAK4G,KAAK,EAAE;0BAAE2G,UAAU,EAAE,GAAG;0BAAED,QAAQ,EAAE;wBAAS,CAAE;wBAAAT,QAAA,EACjD,OAAOtC,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAGA,QAAQ,CAAC5E;sBAAI;wBAAAO,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,EACL,OAAOnD,QAAQ,KAAK,QAAQ,iBAC3BvK,OAAA;wBAAK4G,KAAK,EAAE;0BAAE0G,QAAQ,EAAE,SAAS;0BAAE5K,KAAK,EAAE;wBAAU,CAAE;wBAAAmK,QAAA,GACnDtC,QAAQ,CAAC1E,IAAI,EAAC,sBAAe,EAAC0E,QAAQ,CAACzE,UAAU;sBAAA;wBAAAI,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CACN;oBAAA;sBAAAxH,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAxH,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAEoG,OAAO,EAAE,MAAM;sBAAEK,GAAG,EAAE;oBAAS,CAAE;oBAAAR,QAAA,gBAC7C7M,OAAA;sBACE2N,OAAO,EAAEA,CAAA,KAAM,OAAOpD,QAAQ,KAAK,QAAQ,GAAGD,kBAAkB,CAACC,QAAQ,CAAC,GAAGvC,KAAK,CAAC,wBAAwB,CAAE;sBAC7GpB,KAAK,EAAE;wBACL+F,UAAU,EAAE,SAAS;wBACrBjK,KAAK,EAAE,OAAO;wBACdmL,MAAM,EAAE,MAAM;wBACdjB,OAAO,EAAE,QAAQ;wBACjBgB,YAAY,EAAE,SAAS;wBACvBE,MAAM,EAAE,SAAS;wBACjBd,OAAO,EAAE,MAAM;wBACfE,UAAU,EAAE,QAAQ;wBACpBD,cAAc,EAAE;sBAClB,CAAE;sBACF5K,KAAK,EAAC,cAAc;sBAAAwK,QAAA,eAEpB7M,OAAA,CAACJ,UAAU;wBAACiG,IAAI,EAAE;sBAAG;wBAAAK,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAxH,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACT1N,OAAA;sBACE2N,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAAC5F,MAAM,CAACjD,EAAE,EAAE8O,GAAG,CAAE;sBAChDrK,KAAK,EAAE;wBACL+F,UAAU,EAAE,SAAS;wBACrBjK,KAAK,EAAE,OAAO;wBACdmL,MAAM,EAAE,MAAM;wBACdjB,OAAO,EAAE,QAAQ;wBACjBgB,YAAY,EAAE,SAAS;wBACvBE,MAAM,EAAE,SAAS;wBACjBd,OAAO,EAAE,MAAM;wBACfE,UAAU,EAAE,QAAQ;wBACpBD,cAAc,EAAE;sBAClB,CAAE;sBACF5K,KAAK,EAAC,YAAY;sBAAAwK,QAAA,eAElB7M,OAAA,CAACH,QAAQ;wBAACgG,IAAI,EAAE;sBAAG;wBAAAK,QAAA,EAAAsH,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAxH,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC;kBAAA;oBAAAxH,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GAnEEuD,GAAG;kBAAA/K,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoER,CACN,CAAC,gBACA1N,OAAA;kBAAK4G,KAAK,EAAE;oBACVoK,SAAS,EAAE,QAAQ;oBACnBpE,OAAO,EAAE,MAAM;oBACflK,KAAK,EAAE,SAAS;oBAChBiK,UAAU,EAAE,SAAS;oBACrBiB,YAAY,EAAE,QAAQ;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAAhB,QAAA,gBACA7M,OAAA,CAACf,UAAU;oBAAC4G,IAAI,EAAE,EAAG;oBAACe,KAAK,EAAE;sBAAEmG,MAAM,EAAE,aAAa;sBAAElG,OAAO,EAAE;oBAAI;kBAAE;oBAAAX,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxE1N,OAAA;oBAAG4G,KAAK,EAAE;sBAAEmG,MAAM,EAAE,CAAC;sBAAEmE,SAAS,EAAE;oBAAS,CAAE;oBAAArE,QAAA,EAAC;kBAA6B;oBAAA3G,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/E1N,OAAA;oBAAG4G,KAAK,EAAE;sBAAEmG,MAAM,EAAE,YAAY;sBAAEO,QAAQ,EAAE;oBAAW,CAAE;oBAAAT,QAAA,EAAC;kBAE1D;oBAAA3G,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACN;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GA/FItI,MAAM,CAACjD,EAAE;cAAA+D,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgGhB,CACN;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnM,oBAAoB,iBACnBvB,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE,OAAO;UACjB+D,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAxD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMnM,uBAAuB,CAAC,KAAK,CAAE;YAC9CoF,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEuG,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,eACrC7M,OAAA;YAAQ4G,KAAK,EAAE;cACb+F,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdkK,OAAO,EAAE,gBAAgB;cACzBgB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBd,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBACA7M,OAAA,CAACpB,MAAM;cAAAsH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBACZ;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,EACnE5K,OAAO,CAAC+H,GAAG,CAAC5E,MAAM,iBACjBpF,OAAA;YAAqB4G,KAAK,EAAE;cAC1BiH,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBhB,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,QAAQ;gBAAEzK,KAAK,EAAE0C,MAAM,CAAC1C;cAAM,CAAE;cAAAmK,QAAA,EAC7FzH,MAAM,CAAC/C;YAAK;cAAA6D,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACL1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEyB,aAAa,EAAE,QAAQ;gBAAEpB,GAAG,EAAE;cAAS,CAAE;cAAAR,QAAA,EACrEzH,MAAM,CAACrC,WAAW,IAAIqC,MAAM,CAACrC,WAAW,CAACyE,MAAM,GAAG,CAAC,GAAGpC,MAAM,CAACrC,WAAW,CAACiH,GAAG,CAAC,CAACmH,UAAU,EAAEF,GAAG,kBAC5FjR,OAAA;gBAAe4G,KAAK,EAAE;kBACpBoG,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBN,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBiB,YAAY,EAAE,SAAS;kBACvBC,MAAM,EAAE;gBACV,CAAE;gBAAAhB,QAAA,gBACA7M,OAAA;kBAAK4G,KAAK,EAAE;oBAAEoG,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEG,GAAG,EAAE;kBAAS,CAAE;kBAAAR,QAAA,gBACnE7M,OAAA,CAACb,WAAW;oBAAC0G,IAAI,EAAE,EAAG;oBAACnD,KAAK,EAAC;kBAAS;oBAAAwD,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzC1N,OAAA;oBAAA6M,QAAA,EAAOsE;kBAAU;oBAAAjL,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACN1N,OAAA;kBAAK4G,KAAK,EAAE;oBAAEoG,OAAO,EAAE,MAAM;oBAAEK,GAAG,EAAE;kBAAS,CAAE;kBAAAR,QAAA,gBAC7C7M,OAAA;oBAAQ4G,KAAK,EAAE;sBACb+F,UAAU,EAAE,SAAS;sBACrBjK,KAAK,EAAE,OAAO;sBACdkK,OAAO,EAAE,gBAAgB;sBACzBgB,YAAY,EAAE,SAAS;sBACvBC,MAAM,EAAE,MAAM;sBACdC,MAAM,EAAE,SAAS;sBACjBR,QAAQ,EAAE;oBACZ,CAAE;oBAAAT,QAAA,EAAC;kBAEH;oBAAA3G,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1N,OAAA;oBAAQ4G,KAAK,EAAE;sBACb+F,UAAU,EAAE,SAAS;sBACrBjK,KAAK,EAAE,OAAO;sBACdkK,OAAO,EAAE,gBAAgB;sBACzBgB,YAAY,EAAE,SAAS;sBACvBC,MAAM,EAAE,MAAM;sBACdC,MAAM,EAAE,SAAS;sBACjBR,QAAQ,EAAE;oBACZ,CAAE;oBAAAT,QAAA,EAAC;kBAEH;oBAAA3G,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GApCEuD,GAAG;gBAAA/K,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqCR,CACN,CAAC,gBACA1N,OAAA;gBAAG4G,KAAK,EAAE;kBAAElE,KAAK,EAAE,SAAS;kBAAEwO,SAAS,EAAE;gBAAS,CAAE;gBAAArE,QAAA,EAAC;cAAkB;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAC3E;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAnDEtI,MAAM,CAACjD,EAAE;YAAA+D,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoDd,CACN;QAAC;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAjM,eAAe,iBACdzB,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE,OAAO;UACjB+D,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAxD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMjM,kBAAkB,CAAC,KAAK,CAAE;YACzCkF,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,EACnE5K,OAAO,CAAC+H,GAAG,CAAC5E,MAAM,iBACjBpF,OAAA;YAAqB4G,KAAK,EAAE;cAC1BoG,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE,QAAQ;cACpBN,OAAO,EAAE,MAAM;cACfiB,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBjB,UAAU,EAAEvH,MAAM,CAACzC;YACrB,CAAE;YAAAkK,QAAA,gBACA7M,OAAA;cAAA6M,QAAA,gBACE7M,OAAA;gBAAI4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,QAAQ;kBAAEC,UAAU,EAAE,GAAG;kBAAER,MAAM,EAAE,CAAC;kBAAErK,KAAK,EAAE0C,MAAM,CAAC1C;gBAAM,CAAE;gBAAAmK,QAAA,EAChFzH,MAAM,CAAC/C;cAAK;gBAAA6D,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACL1N,OAAA;gBAAG4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,UAAU;kBAAE5K,KAAK,EAAE,SAAS;kBAAEqK,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,GAAC,YACrD,EAACzH,MAAM,CAACvC,QAAQ,EAAC,GAC7B;cAAA;gBAAAqD,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoK,SAAS,EAAE;cAAQ,CAAE;cAAAnE,QAAA,gBACjC7M,OAAA;gBAAK4G,KAAK,EAAE;kBACV0G,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE,MAAM;kBAClB7K,KAAK,EAAE0C,MAAM,CAAC1C;gBAChB,CAAE;gBAAAmK,QAAA,EACCzH,MAAM,CAAC2C,KAAK,IAAI;cAAK;gBAAA7B,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACN1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,SAAS;kBAAE5K,KAAK,EAAE;gBAAU,CAAE;gBAAAmK,QAAA,EAAC;cAEvD;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA5BEtI,MAAM,CAACjD,EAAE;YAAA+D,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Bd,CACN;QAAC;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEkI,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,MAAM;YAAED,UAAU,EAAE,SAAS;YAAEiB,YAAY,EAAE;UAAS,CAAE;UAAAf,QAAA,gBAClG7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,GAAG;cAAEJ,YAAY,EAAE;YAAS,CAAE;YAAAN,QAAA,EAAC;UAAW;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5F1N,OAAA;YAAK4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE;YAAU,CAAE;YAAAmK,QAAA,EAAC;UAAG;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjF1N,OAAA;YAAG4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,UAAU;cAAE5K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEjE;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA/L,iBAAiB,iBAChB3B,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE,OAAO;UACjB+D,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAxD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM/L,oBAAoB,CAAC,KAAK,CAAE;YAC3CgF,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEuG,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,eACrC7M,OAAA;YAAQ4G,KAAK,EAAE;cACb+F,UAAU,EAAE,SAAS;cACrBjK,KAAK,EAAE,OAAO;cACdkK,OAAO,EAAE,gBAAgB;cACzBgB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBd,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBACA7M,OAAA,CAACpB,MAAM;cAAAsH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cACZ;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEiB,mBAAmB,EAAE,gBAAgB;YAAEZ,GAAG,EAAE,KAAK;YAAEV,UAAU,EAAE,SAAS;YAAEiB,YAAY,EAAE,QAAQ;YAAEc,QAAQ,EAAE;UAAS,CAAE;UAAA7B,QAAA,GACnJ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC7C,GAAG,CAACoH,GAAG,iBACxDpR,OAAA;YAAe4G,KAAK,EAAE;cACpB+F,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,SAAS;cAClBoE,SAAS,EAAE,QAAQ;cACnBzD,UAAU,EAAE,GAAG;cACfD,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,EACCuE;UAAG,GAPIA,GAAG;YAAAlL,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQR,CACN,CAAC,EAED2D,KAAK,CAACC,IAAI,CAAC;YAAE9J,MAAM,EAAE;UAAG,CAAC,EAAE,CAAC+J,CAAC,EAAEC,CAAC,kBAC/BxR,OAAA;YAAa4G,KAAK,EAAE;cAClB+F,UAAU,EAAE,OAAO;cACnBC,OAAO,EAAE,SAAS;cAClBF,SAAS,EAAE,MAAM;cACjBY,QAAQ,EAAE,UAAU;cACpB0B,QAAQ,EAAE;YACZ,CAAE;YAAAnC,QAAA,gBACA7M,OAAA;cAAK4G,KAAK,EAAE;gBAAElE,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,EAAI2E,CAAC,GAAG,EAAE,GAAI;YAAC;cAAAtL,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACvD8D,CAAC,KAAK,EAAE,iBACPxR,OAAA;cAAK4G,KAAK,EAAE;gBACV+F,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,OAAO;gBACd4K,QAAQ,EAAE,SAAS;gBACnBV,OAAO,EAAE,SAAS;gBAClBgB,YAAY,EAAE,SAAS;gBACvBkB,SAAS,EAAE;cACb,CAAE;cAAAjC,QAAA,EAAC;YAEH;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,EACA8D,CAAC,KAAK,EAAE,iBACPxR,OAAA;cAAK4G,KAAK,EAAE;gBACV+F,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,OAAO;gBACd4K,QAAQ,EAAE,SAAS;gBACnBV,OAAO,EAAE,SAAS;gBAClBgB,YAAY,EAAE,SAAS;gBACvBkB,SAAS,EAAE;cACb,CAAE;cAAAjC,QAAA,EAAC;YAEH;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,GA/BO8D,CAAC;YAAAtL,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCN,CACN,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEkI,SAAS,EAAE;UAAS,CAAE;UAAAjC,QAAA,gBAClC7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,GAAG;cAAEJ,YAAY,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAe;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9F1N,OAAA;YAAK4G,KAAK,EAAE;cAAEoG,OAAO,EAAE,MAAM;cAAEyB,aAAa,EAAE,QAAQ;cAAEpB,GAAG,EAAE;YAAS,CAAE;YAAAR,QAAA,EACrE7J,KAAK,CAACyO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACzH,GAAG,CAAC9B,IAAI,iBACzBlI,OAAA;cAAmB4G,KAAK,EAAE;gBACxBoG,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBN,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAEzE,IAAI,CAACvF,OAAO;gBACxBiL,YAAY,EAAE;cAChB,CAAE;cAAAf,QAAA,gBACA7M,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEG,GAAG,EAAE;gBAAS,CAAE;gBAAAR,QAAA,gBACnE7M,OAAA,CAACkI,IAAI,CAAC9E,IAAI;kBAACyC,IAAI,EAAE,EAAG;kBAACnD,KAAK,EAAEwF,IAAI,CAACxF;gBAAM;kBAAAwD,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1C1N,OAAA;kBAAM4G,KAAK,EAAE;oBAAE2G,UAAU,EAAE;kBAAI,CAAE;kBAAAV,QAAA,EAAE3E,IAAI,CAAC7F;gBAAK;kBAAA6D,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN1N,OAAA;gBAAM4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,UAAU;kBAAE5K,KAAK,EAAEwF,IAAI,CAACxF,KAAK;kBAAE6K,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EACvE3E,IAAI,CAAC/E;cAAQ;gBAAA+C,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA,GAdCxF,IAAI,CAAC/F,EAAE;cAAA+D,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeZ,CACN;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAhK,eAAe,iBACd1D,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE;QACZ,CAAE;QAAAD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE8B,eAAe,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;cAAEuI,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAC3HrI,eAAe,KAAK,QAAQ,GAAG,2BAA2B,GAAG;UAAqB;YAAA0B,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM;cACbhK,kBAAkB,CAAC,KAAK,CAAC;cACzBE,eAAe,CAAC,IAAI,CAAC;cACrBE,iBAAiB,CAAC,EAAE,CAAC;cACrBE,iBAAiB,CAAC,CAAC,CAAC;YACtB,CAAE;YACF2C,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,GACrErI,eAAe,KAAK,QAAQ,iBAC3BxE,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1N,OAAA;cACEmO,KAAK,EAAErK,cAAe;cACtB0M,QAAQ,EAAG9I,CAAC,IAAK3D,iBAAiB,CAAC2D,CAAC,CAAC6B,MAAM,CAAC4E,KAAK,CAAE;cACnDvH,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ,CAAE;cAAAT,QAAA,gBAEF7M,OAAA;gBAAQmO,KAAK,EAAC,EAAE;gBAAAtB,QAAA,EAAC;cAAkB;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC3CzL,OAAO,CAAC+H,GAAG,CAAC5E,MAAM,iBACjBpF,OAAA;gBAAwBmO,KAAK,EAAE/I,MAAM,CAACjD,EAAG;gBAAA0K,QAAA,EACtCzH,MAAM,CAAC/C;cAAK,GADF+C,MAAM,CAACjD,EAAE;gBAAA+D,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEAlJ,eAAe,KAAK,QAAQ,iBAC3BxE,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,SAAS;cACrBkB,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBhB,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA7M,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEG,GAAG,EAAE,QAAQ;gBAAEF,YAAY,EAAE;cAAS,CAAE;cAAAN,QAAA,gBAC3F7M,OAAA,CAACd,UAAU;gBAAC2G,IAAI,EAAE,EAAG;gBAACnD,KAAK,EAAC;cAAS;gBAAAwD,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxC1N,OAAA;gBAAM4G,KAAK,EAAE;kBAAE2G,UAAU,EAAE,GAAG;kBAAE7K,KAAK,EAAE;gBAAU,CAAE;gBAAAmK,QAAA,EAAC;cAAkB;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACN1N,OAAA;cAAG4G,KAAK,EAAE;gBAAEmG,MAAM,EAAE,CAAC;gBAAEO,QAAQ,EAAE,UAAU;gBAAE5K,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,EAAC;YAEjE;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN,eAED1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1N,OAAA;cAAK4G,KAAK,EAAE;gBACViH,MAAM,EAAE,oBAAoB;gBAC5BD,YAAY,EAAE,QAAQ;gBACtBhB,OAAO,EAAE,MAAM;gBACfoE,SAAS,EAAE,QAAQ;gBACnBrE,UAAU,EAAE/I,YAAY,GAAG,SAAS,GAAG,SAAS;gBAChD+K,WAAW,EAAE/K,YAAY,GAAG,SAAS,GAAG;cAC1C,CAAE;cAAAiJ,QAAA,gBACA7M,OAAA;gBACE4F,IAAI,EAAC,MAAM;gBACX8L,MAAM,EAAC,MAAM;gBACblB,QAAQ,EAAEpH,gBAAiB;gBAC3BxC,KAAK,EAAE;kBAAEoG,OAAO,EAAE;gBAAO,CAAE;gBAC3B7K,EAAE,EAAC;cAAY;gBAAA+D,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACF1N,OAAA;gBACE2R,OAAO,EAAC,YAAY;gBACpB/K,KAAK,EAAE;kBACLkH,MAAM,EAAE,SAAS;kBACjBd,OAAO,EAAE,MAAM;kBACfyB,aAAa,EAAE,QAAQ;kBACvBvB,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBAEF7M,OAAA,CAACF,QAAQ;kBAAC+F,IAAI,EAAE,EAAG;kBAACnD,KAAK,EAAEkB,YAAY,GAAG,SAAS,GAAG;gBAAU;kBAAAsC,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAClE9J,YAAY,gBACX5D,OAAA;kBAAA6M,QAAA,gBACE7M,OAAA;oBAAK4G,KAAK,EAAE;sBAAE2G,UAAU,EAAE,GAAG;sBAAE7K,KAAK,EAAE;oBAAU,CAAE;oBAAAmK,QAAA,EAC/CjJ,YAAY,CAAC+B;kBAAI;oBAAAO,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACN1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAE0G,QAAQ,EAAE,UAAU;sBAAE5K,KAAK,EAAE;oBAAU,CAAE;oBAAAmK,QAAA,GACpD,CAACjJ,YAAY,CAACiC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEqE,OAAO,CAAC,CAAC,CAAC,EAAC,KAChD;kBAAA;oBAAAhE,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEN1N,OAAA;kBAAA6M,QAAA,gBACE7M,OAAA;oBAAK4G,KAAK,EAAE;sBAAE2G,UAAU,EAAE,GAAG;sBAAE7K,KAAK,EAAE;oBAAU,CAAE;oBAAAmK,QAAA,EAAC;kBAEnD;oBAAA3G,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAE0G,QAAQ,EAAE,UAAU;sBAAE5K,KAAK,EAAE;oBAAU,CAAE;oBAAAmK,QAAA,EAAC;kBAExD;oBAAA3G,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELxJ,WAAW,iBACVlE,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEE,YAAY,EAAE;cAAS,CAAE;cAAAN,QAAA,gBACvF7M,OAAA;gBAAM4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAAY;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3E1N,OAAA;gBAAM4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,UAAU;kBAAE5K,KAAK,EAAE;gBAAU,CAAE;gBAAAmK,QAAA,GAAE7I,cAAc,EAAC,GAAC;cAAA;gBAAAkC,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACN1N,OAAA;cAAK4G,KAAK,EAAE;gBACVgI,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,QAAQ;gBAChBlC,UAAU,EAAE,SAAS;gBACrBiB,YAAY,EAAE,SAAS;gBACvBc,QAAQ,EAAE;cACZ,CAAE;cAAA7B,QAAA,eACA7M,OAAA;gBAAK4G,KAAK,EAAE;kBACVgI,KAAK,EAAE,GAAG5K,cAAc,GAAG;kBAC3B6K,MAAM,EAAE,MAAM;kBACdlC,UAAU,EAAE,SAAS;kBACrB7F,UAAU,EAAE;gBACd;cAAE;gBAAAZ,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE,MAAM;YAAEyB,SAAS,EAAE;UAAO,CAAE;UAAAjC,QAAA,gBAC9D7M,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM;cACbhK,kBAAkB,CAAC,KAAK,CAAC;cACzBE,eAAe,CAAC,IAAI,CAAC;cACrBE,iBAAiB,CAAC,EAAE,CAAC;cACrBE,iBAAiB,CAAC,CAAC,CAAC;YACtB,CAAE;YACF2N,QAAQ,EAAE1N,WAAY;YACtB0C,KAAK,EAAE;cACLmI,IAAI,EAAE,CAAC;cACPnC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,OAAO;cACnBjK,KAAK,EAAE,SAAS;cAChBmL,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE5J,WAAW,GAAG,aAAa,GAAG,SAAS;cAC/C2C,OAAO,EAAE3C,WAAW,GAAG,GAAG,GAAG;YAC/B,CAAE;YAAA2I,QAAA,EACH;UAED;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1N,OAAA;YACE2N,OAAO,EAAEnJ,eAAe,KAAK,QAAQ,GAAG8G,iBAAiB,GAAG7B,gBAAiB;YAC7EmI,QAAQ,EAAE,CAAChO,YAAY,IAAKY,eAAe,KAAK,QAAQ,IAAI,CAACV,cAAe,IAAII,WAAY;YAC5F0C,KAAK,EAAE;cACLmI,IAAI,EAAE,CAAC;cACPnC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAG,CAAC/I,YAAY,IAAKY,eAAe,KAAK,QAAQ,IAAI,CAACV,cAAe,IAAII,WAAW,GAAI,SAAS,GAAIM,eAAe,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;cACpK9B,KAAK,EAAE,OAAO;cACdmL,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAG,CAAClK,YAAY,IAAKY,eAAe,KAAK,QAAQ,IAAI,CAACV,cAAe,IAAII,WAAW,GAAI,aAAa,GAAG,SAAS;cACvH8I,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxBI,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,EAED3I,WAAW,gBACVlE,OAAA,CAAAE,SAAA;cAAA2M,QAAA,gBACE7M,OAAA,CAACtB,QAAQ;gBAACmR,SAAS,EAAC,cAAc;gBAAChK,IAAI,EAAE;cAAG;gBAAAK,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjD;YAAA,eAAE,CAAC,gBAEH1N,OAAA,CAAAE,SAAA;cAAA2M,QAAA,gBACE7M,OAAA,CAACF,QAAQ;gBAAC+F,IAAI,EAAE;cAAG;gBAAAK,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAExB;YAAA,eAAE;UACH;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGApJ,cAAc,iBACbtE,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE,OAAO;UACjB+D,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAxD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAMpJ,iBAAiB,CAAC,KAAK,CAAE;YACxCqC,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBAEpE7M,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,QAAQ;cACjBgB,YAAY,EAAE,SAAS;cACvBC,MAAM,EAAE;YACV,CAAE;YAAAhB,QAAA,gBACA7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,MAAM;gBAAEzK,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,EAAC;YAE5F;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEK,GAAG,EAAE,MAAM;gBAAEF,YAAY,EAAE;cAAS,CAAE;cAAAN,QAAA,gBACnE7M,OAAA;gBACE2N,OAAO,EAAEA,CAAA,KAAM;kBACblJ,kBAAkB,CAAC,QAAQ,CAAC;kBAC5Bd,kBAAkB,CAAC,IAAI,CAAC;gBAC1B,CAAE;gBACFiD,KAAK,EAAE;kBACL+F,UAAU,EAAE,SAAS;kBACrBjK,KAAK,EAAE,OAAO;kBACdkK,OAAO,EAAE,gBAAgB;kBACzBgB,YAAY,EAAE,QAAQ;kBACtBC,MAAM,EAAE,MAAM;kBACdC,MAAM,EAAE,SAAS;kBACjBd,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE,QAAQ;kBACbvG,UAAU,EAAE;gBACd,CAAE;gBACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;gBAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;gBAAAE,QAAA,gBAE3D7M,OAAA,CAACF,QAAQ;kBAAAoG,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBACd;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET1N,OAAA;gBACE2N,OAAO,EAAEA,CAAA,KAAM;kBACblJ,kBAAkB,CAAC,QAAQ,CAAC;kBAC5Bd,kBAAkB,CAAC,IAAI,CAAC;gBAC1B,CAAE;gBACFiD,KAAK,EAAE;kBACL+F,UAAU,EAAE,SAAS;kBACrBjK,KAAK,EAAE,OAAO;kBACdkK,OAAO,EAAE,gBAAgB;kBACzBgB,YAAY,EAAE,QAAQ;kBACtBC,MAAM,EAAE,MAAM;kBACdC,MAAM,EAAE,SAAS;kBACjBd,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE,QAAQ;kBACbvG,UAAU,EAAE;gBACd,CAAE;gBACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;gBAC3DqB,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAAC+F,UAAU,GAAG,SAAU;gBAAAE,QAAA,gBAE3D7M,OAAA,CAACF,QAAQ;kBAAAoG,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBACd;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1N,OAAA;cAAK4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,UAAU;gBAAE5K,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,gBACrD7M,OAAA;gBAAG4G,KAAK,EAAE;kBAAEmG,MAAM,EAAE;gBAAa,CAAE;gBAAAF,QAAA,gBACjC7M,OAAA;kBAAA6M,QAAA,EAAQ;gBAAW;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,sDAC9B;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ1N,OAAA;gBAAG4G,KAAK,EAAE;kBAAEmG,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,gBACtB7M,OAAA;kBAAA6M,QAAA,EAAQ;gBAAW;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,wDAC9B;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1N,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,QAAQ;cACjBgB,YAAY,EAAE,SAAS;cACvBC,MAAM,EAAE;YACV,CAAE;YAAAhB,QAAA,gBACA7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,MAAM;gBAAEzK,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,EAAC;YAE5F;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEuG,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,gBACnC7M,OAAA;gBAAG4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,UAAU;kBAAE5K,KAAK,EAAE,SAAS;kBAAEqK,MAAM,EAAE;gBAAW,CAAE;gBAAAF,QAAA,EAAC;cAE1E;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAEJ1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,MAAM;kBAAEiB,mBAAmB,EAAE,sCAAsC;kBAAEZ,GAAG,EAAE;gBAAU,CAAE;gBAAAR,QAAA,EAC1G,CACC;kBAAE9G,QAAQ,EAAE,aAAa;kBAAE8L,MAAM,EAAE,aAAa;kBAAEnP,KAAK,EAAE;gBAAU,CAAC,EACpE;kBAAEqD,QAAQ,EAAE,SAAS;kBAAE8L,MAAM,EAAE,SAAS;kBAAEnP,KAAK,EAAE;gBAAU,CAAC,EAC5D;kBAAEqD,QAAQ,EAAE,kBAAkB;kBAAE8L,MAAM,EAAE,kBAAkB;kBAAEnP,KAAK,EAAE;gBAAU,CAAC,EAC9E;kBAAEqD,QAAQ,EAAE,SAAS;kBAAE8L,MAAM,EAAE,SAAS;kBAAEnP,KAAK,EAAE;gBAAU,CAAC,CAC7D,CAACsH,GAAG,CAAC8H,GAAG,iBACP9R,OAAA;kBAEE2N,OAAO,EAAEA,CAAA,KAAM;oBACb,MAAMzH,QAAQ,GAAG6L,MAAM,CAAC,6FAA6FD,GAAG,CAACD,MAAM,WAAW,CAAC;oBAC3I,IAAI3L,QAAQ,IAAIA,QAAQ,CAAC8L,QAAQ,CAAC,MAAM,CAAC,EAAE;sBACzC,MAAM1P,WAAW,GAAGyP,MAAM,CAAC,mDAAmD,CAAC,IAAI,EAAE;sBACrF/F,qBAAqB,CAAC9F,QAAQ,EAAE4L,GAAG,CAAC/L,QAAQ,EAAEzD,WAAW,CAAC;sBAC1D0F,KAAK,CAAC,aAAa9B,QAAQ,uBAAuB,CAAC;oBACrD,CAAC,MAAM,IAAIA,QAAQ,EAAE;sBACnB8B,KAAK,CAAC,uDAAuD,CAAC;oBAChE;kBACF,CAAE;kBACFpB,KAAK,EAAE;oBACL+F,UAAU,EAAEmF,GAAG,CAACpP,KAAK;oBACrBA,KAAK,EAAE,OAAO;oBACdkK,OAAO,EAAE,SAAS;oBAClBgB,YAAY,EAAE,QAAQ;oBACtBC,MAAM,EAAE,MAAM;oBACdC,MAAM,EAAE,SAAS;oBACjBR,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,GAAG;oBACfzG,UAAU,EAAE;kBACd,CAAE;kBACFiH,YAAY,EAAGrG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAACC,OAAO,GAAG,KAAM;kBACpDmH,YAAY,EAAGtG,CAAC,IAAKA,CAAC,CAAC6B,MAAM,CAAC3C,KAAK,CAACC,OAAO,GAAG,GAAI;kBAAAgG,QAAA,GACnD,MACK,EAACiF,GAAG,CAAC/L,QAAQ,EAAC,MACpB;gBAAA,GA1BO+L,GAAG,CAAC/L,QAAQ;kBAAAG,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0BX,CACT;cAAC;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1N,OAAA;cAAK4G,KAAK,EAAE;gBACV+F,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,MAAM;gBACfgB,YAAY,EAAE,QAAQ;gBACtBC,MAAM,EAAE;cACV,CAAE;cAAAhB,QAAA,eACA7M,OAAA;gBAAG4G,KAAK,EAAE;kBAAE0G,QAAQ,EAAE,UAAU;kBAAE5K,KAAK,EAAE,SAAS;kBAAEqK,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,gBAC9D7M,OAAA;kBAAA6M,QAAA,EAAQ;gBAAa;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,6DAC9B,eAAA1N,OAAA;kBAAAkG,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,wBAAe,eAAA1N,OAAA;kBAAA6M,QAAA,EAAM;gBAAmC;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrE1N,OAAA;kBAAAkG,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,oBAAW,eAAA1N,OAAA;kBAAA6M,QAAA,EAAM;gBAA+B;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7D1N,OAAA;kBAAAkG,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,6BAAoB,eAAA1N,OAAA;kBAAA6M,QAAA,EAAM;gBAAwC;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/E1N,OAAA;kBAAAkG,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,oBAAW,eAAA1N,OAAA;kBAAA6M,QAAA,EAAM;gBAA+B;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1N,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,QAAQ;cACjBgB,YAAY,EAAE,SAAS;cACvBC,MAAM,EAAE;YACV,CAAE;YAAAhB,QAAA,gBACA7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,MAAM;gBAAEzK,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,EAAC;YAE5F;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEyB,aAAa,EAAE,QAAQ;gBAAEpB,GAAG,EAAE;cAAU,CAAE;cAAAR,QAAA,EACtEpH,eAAe,CAACuE,GAAG,CAAEO,QAAQ,iBAC5BvK,OAAA;gBAAuB4G,KAAK,EAAE;kBAC5BoG,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBN,OAAO,EAAE,MAAM;kBACfD,UAAU,EAAE,OAAO;kBACnBiB,YAAY,EAAE,QAAQ;kBACtBC,MAAM,EAAE;gBACV,CAAE;gBAAAhB,QAAA,gBACA7M,OAAA;kBAAK4G,KAAK,EAAE;oBAAEmI,IAAI,EAAE;kBAAE,CAAE;kBAAAlC,QAAA,gBACtB7M,OAAA;oBAAK4G,KAAK,EAAE;sBAAE2G,UAAU,EAAE,GAAG;sBAAED,QAAQ,EAAE,MAAM;sBAAEH,YAAY,EAAE;oBAAU,CAAE;oBAAAN,QAAA,EACxEtC,QAAQ,CAAC5E;kBAAI;oBAAAO,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACN1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAE0G,QAAQ,EAAE,UAAU;sBAAE5K,KAAK,EAAE,SAAS;sBAAEyK,YAAY,EAAE;oBAAU,CAAE;oBAAAN,QAAA,EAC7EtC,QAAQ,CAACjI;kBAAW;oBAAA4D,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACN1N,OAAA;oBAAK4G,KAAK,EAAE;sBAAE0G,QAAQ,EAAE,SAAS;sBAAE5K,KAAK,EAAE;oBAAU,CAAE;oBAAAmK,QAAA,GACnDtC,QAAQ,CAAC1E,IAAI,EAAC,UAAG,EAAC0E,QAAQ,CAACvE,SAAS,EAAC,oBAAa,EAACuE,QAAQ,CAACzE,UAAU;kBAAA;oBAAAI,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN1N,OAAA;kBAAK4G,KAAK,EAAE;oBAAEoG,OAAO,EAAE,MAAM;oBAAEK,GAAG,EAAE;kBAAS,CAAE;kBAAAR,QAAA,gBAC7C7M,OAAA;oBACE2N,OAAO,EAAEA,CAAA,KAAMlC,oBAAoB,CAAClB,QAAQ,CAAE;oBAC9C3D,KAAK,EAAE;sBACL+F,UAAU,EAAE,SAAS;sBACrBjK,KAAK,EAAE,OAAO;sBACdmL,MAAM,EAAE,MAAM;sBACdjB,OAAO,EAAE,QAAQ;sBACjBgB,YAAY,EAAE,SAAS;sBACvBE,MAAM,EAAE;oBACV,CAAE;oBACFzL,KAAK,EAAC,kBAAkB;oBAAAwK,QAAA,eAExB7M,OAAA,CAACJ,UAAU;sBAACiG,IAAI,EAAE;oBAAG;sBAAAK,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAxH,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACT1N,OAAA;oBACE2N,OAAO,EAAEA,CAAA,KAAM7B,0BAA0B,CAACvB,QAAQ,CAACpI,EAAE,CAAE;oBACvDyE,KAAK,EAAE;sBACL+F,UAAU,EAAE,SAAS;sBACrBjK,KAAK,EAAE,OAAO;sBACdmL,MAAM,EAAE,MAAM;sBACdjB,OAAO,EAAE,QAAQ;sBACjBgB,YAAY,EAAE,SAAS;sBACvBE,MAAM,EAAE;oBACV,CAAE;oBACFzL,KAAK,EAAC,iBAAiB;oBAAAwK,QAAA,eAEvB7M,OAAA,CAACH,QAAQ;sBAACgG,IAAI,EAAE;oBAAG;sBAAAK,QAAA,EAAAsH,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAxH,QAAA,EAAAsH,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAxH,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAjDEnD,QAAQ,CAACpI,EAAE;gBAAA+D,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkDhB,CACN;YAAC;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1N,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,QAAQ;cACjBgB,YAAY,EAAE,SAAS;cACvBC,MAAM,EAAE;YACV,CAAE;YAAAhB,QAAA,gBACA7M,OAAA;cAAI4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,MAAM;gBAAEzK,KAAK,EAAE;cAAU,CAAE;cAAAmK,QAAA,EAAC;YAE5F;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL1N,OAAA;cAAK4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,MAAM;gBAAEiB,mBAAmB,EAAE,sCAAsC;gBAAEZ,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,gBACxG7M,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoK,SAAS,EAAE;gBAAS,CAAE;gBAAAnE,QAAA,gBAClC7M,OAAA;kBAAK4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE,MAAM;oBAAE7K,KAAK,EAAE;kBAAU,CAAE;kBAAAmK,QAAA,EACpEpH,eAAe,CAAC+B;gBAAM;kBAAAtB,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACN1N,OAAA;kBAAK4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,UAAU;oBAAE5K,KAAK,EAAE;kBAAU,CAAE;kBAAAmK,QAAA,EAAC;gBAAgB;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eAEN1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoK,SAAS,EAAE;gBAAS,CAAE;gBAAAnE,QAAA,gBAClC7M,OAAA;kBAAK4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE,MAAM;oBAAE7K,KAAK,EAAE;kBAAU,CAAE;kBAAAmK,QAAA,EACpEpH,eAAe,CAACwM,MAAM,CAAC,CAACC,GAAG,EAAExG,GAAG,KAAKwG,GAAG,GAAGxG,GAAG,CAAC1F,SAAS,EAAE,CAAC;gBAAC;kBAAAE,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACN1N,OAAA;kBAAK4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,UAAU;oBAAE5K,KAAK,EAAE;kBAAU,CAAE;kBAAAmK,QAAA,EAAC;gBAAe;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eAEN1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoK,SAAS,EAAE;gBAAS,CAAE;gBAAAnE,QAAA,gBAClC7M,OAAA;kBAAK4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE,MAAM;oBAAE7K,KAAK,EAAE;kBAAU,CAAE;kBAAAmK,QAAA,EACpE5K,OAAO,CAACuF;gBAAM;kBAAAtB,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACN1N,OAAA;kBAAK4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,UAAU;oBAAE5K,KAAK,EAAE;kBAAU,CAAE;kBAAAmK,QAAA,EAAC;gBAAc;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eAEN1N,OAAA;gBAAK4G,KAAK,EAAE;kBAAEoK,SAAS,EAAE;gBAAS,CAAE;gBAAAnE,QAAA,gBAClC7M,OAAA;kBAAK4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE,MAAM;oBAAE7K,KAAK,EAAE;kBAAU,CAAE;kBAAAmK,QAAA,EACpE5K,OAAO,CAACgQ,MAAM,CAAC,CAACC,GAAG,EAAE9M,MAAM;oBAAA,IAAA+M,iBAAA;oBAAA,OAAKD,GAAG,IAAI,EAAAC,iBAAA,GAAA/M,MAAM,CAACtC,SAAS,cAAAqP,iBAAA,uBAAhBA,iBAAA,CAAkB3K,MAAM,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC;gBAAC;kBAAAtB,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACN1N,OAAA;kBAAK4G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,UAAU;oBAAE5K,KAAK,EAAE;kBAAU,CAAE;kBAAAmK,QAAA,EAAC;gBAAgB;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAhJ,cAAc,iBACb1E,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE;QACZ,CAAE;QAAAD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM;cACbhJ,iBAAiB,CAAC,KAAK,CAAC;cACxBE,gBAAgB,CAAC,EAAE,CAAC;YACtB,CAAE;YACF+B,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBACtE7M,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1N,OAAA;cACE4F,IAAI,EAAC,UAAU;cACfuI,KAAK,EAAEvJ,aAAc;cACrB4L,QAAQ,EAAG9I,CAAC,IAAK7C,gBAAgB,CAAC6C,CAAC,CAAC6B,MAAM,CAAC4E,KAAK,CAAE;cAClDuC,WAAW,EAAC,sBAAsB;cAClC9J,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ,CAAE;cACFmD,UAAU,EAAG/I,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAIuE,gBAAgB,CAAC;YAAE;cAAAhG,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAK4G,KAAK,EAAE;cAAEoG,OAAO,EAAE,MAAM;cAAEK,GAAG,EAAE;YAAO,CAAE;YAAAR,QAAA,gBAC3C7M,OAAA;cACE2N,OAAO,EAAEA,CAAA,KAAM;gBACbhJ,iBAAiB,CAAC,KAAK,CAAC;gBACxBE,gBAAgB,CAAC,EAAE,CAAC;cACtB,CAAE;cACF+B,KAAK,EAAE;gBACLmI,IAAI,EAAE,CAAC;gBACPnC,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,SAAS;gBAChBmL,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE,QAAQ;gBACtBE,MAAM,EAAE;cACV,CAAE;cAAAjB,QAAA,EACH;YAED;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1N,OAAA;cACE2N,OAAO,EAAEzB,gBAAiB;cAC1BtF,KAAK,EAAE;gBACLmI,IAAI,EAAE,CAAC;gBACPnC,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,OAAO;gBACdmL,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE,QAAQ;gBACtBE,MAAM,EAAE;cACV,CAAE;cAAAjB,QAAA,EACH;YAED;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1N,OAAA;YAAK4G,KAAK,EAAE;cACV+F,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,MAAM;cACfgB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE;YACV,CAAE;YAAAhB,QAAA,eACA7M,OAAA;cAAG4G,KAAK,EAAE;gBAAE0G,QAAQ,EAAE,UAAU;gBAAE5K,KAAK,EAAE,SAAS;gBAAEqK,MAAM,EAAE;cAAE,CAAE;cAAAF,QAAA,gBAC9D7M,OAAA;gBAAA6M,QAAA,EAAQ;cAAc;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,aACjC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA5I,mBAAmB,iBAClB9E,OAAA;MAAK4G,KAAK,EAAE;QACVoI,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTtD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBkD,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eACA7M,OAAA;QAAK4G,KAAK,EAAE;UACV+F,UAAU,EAAE,OAAO;UACnBiB,YAAY,EAAE,MAAM;UACpBhB,OAAO,EAAE,MAAM;UACfgC,KAAK,EAAE,KAAK;UACZ9B,QAAQ,EAAE,OAAO;UACjB+D,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAxD,QAAA,gBACA7M,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G7M,OAAA;YAAI4G,KAAK,EAAE;cAAE0G,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE7K,KAAK,EAAE,SAAS;cAAEqK,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAA3G,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1N,OAAA;YACE2N,OAAO,EAAEA,CAAA,KAAM5I,sBAAsB,CAAC,KAAK,CAAE;YAC7C6B,KAAK,EAAE;cACL+F,UAAU,EAAE,MAAM;cAClBkB,MAAM,EAAE,MAAM;cACdnL,KAAK,EAAE,SAAS;cAChBoL,MAAM,EAAE,SAAS;cACjBR,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF7M,OAAA,CAACR,GAAG;cAAA0G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1N,OAAA;UAAK4G,KAAK,EAAE;YAAEoG,OAAO,EAAE,MAAM;YAAEyB,aAAa,EAAE,QAAQ;YAAEpB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBACtE7M,OAAA;YAAK4G,KAAK,EAAE;cAAEoG,OAAO,EAAE,MAAM;cAAEiB,mBAAmB,EAAE,SAAS;cAAEZ,GAAG,EAAE;YAAO,CAAE;YAAAR,QAAA,gBAC3E7M,OAAA;cAAA6M,QAAA,gBACE7M,OAAA;gBAAO4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,OAAO;kBAAEG,YAAY,EAAE,QAAQ;kBAAEI,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAE7E;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1N,OAAA;gBACE4F,IAAI,EAAC,MAAM;gBACXuI,KAAK,EAAE5I,aAAa,CAACL,QAAS;gBAC9BsL,QAAQ,EAAG9I,CAAC,IAAKlC,gBAAgB,CAAC0B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEhC,QAAQ,EAAEwC,CAAC,CAAC6B,MAAM,CAAC4E;gBAAM,CAAC,CAAC,CAAE;gBACnFuC,WAAW,EAAC,iBAAiB;gBAC7B9J,KAAK,EAAE;kBACLgI,KAAK,EAAE,MAAM;kBACbhC,OAAO,EAAE,SAAS;kBAClBiB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBN,QAAQ,EAAE;gBACZ;cAAE;gBAAApH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1N,OAAA;cAAA6M,QAAA,gBACE7M,OAAA;gBAAO4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,OAAO;kBAAEG,YAAY,EAAE,QAAQ;kBAAEI,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAE7E;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1N,OAAA;gBACE4F,IAAI,EAAC,OAAO;gBACZuI,KAAK,EAAE5I,aAAa,CAACJ,KAAM;gBAC3BqL,QAAQ,EAAG9I,CAAC,IAAKlC,gBAAgB,CAAC0B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE/B,KAAK,EAAEuC,CAAC,CAAC6B,MAAM,CAAC4E;gBAAM,CAAC,CAAC,CAAE;gBAChFuC,WAAW,EAAC,kBAAkB;gBAC9B9J,KAAK,EAAE;kBACLgI,KAAK,EAAE,MAAM;kBACbhC,OAAO,EAAE,SAAS;kBAClBiB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBN,QAAQ,EAAE;gBACZ;cAAE;gBAAApH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1N,OAAA;YAAK4G,KAAK,EAAE;cAAEoG,OAAO,EAAE,MAAM;cAAEiB,mBAAmB,EAAE,aAAa;cAAEZ,GAAG,EAAE;YAAO,CAAE;YAAAR,QAAA,gBAC/E7M,OAAA;cAAA6M,QAAA,gBACE7M,OAAA;gBAAO4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,OAAO;kBAAEG,YAAY,EAAE,QAAQ;kBAAEI,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAE7E;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1N,OAAA;gBACEmO,KAAK,EAAE5I,aAAa,CAACH,MAAO;gBAC5BoL,QAAQ,EAAG9I,CAAC,IAAKlC,gBAAgB,CAAC0B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE9B,MAAM,EAAEsC,CAAC,CAAC6B,MAAM,CAAC4E;gBAAM,CAAC,CAAC,CAAE;gBACjFvH,KAAK,EAAE;kBACLgI,KAAK,EAAE,MAAM;kBACbhC,OAAO,EAAE,SAAS;kBAClBiB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBN,QAAQ,EAAE;gBACZ,CAAE;gBAAAT,QAAA,gBAEF7M,OAAA;kBAAQmO,KAAK,EAAC,EAAE;kBAAAtB,QAAA,EAAC;gBAAgB;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1C1N,OAAA;kBAAQmO,KAAK,EAAC,aAAa;kBAAAtB,QAAA,EAAC;gBAAW;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChD1N,OAAA;kBAAQmO,KAAK,EAAC,SAAS;kBAAAtB,QAAA,EAAC;gBAAO;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC1N,OAAA;kBAAQmO,KAAK,EAAC,kBAAkB;kBAAAtB,QAAA,EAAC;gBAAgB;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1D1N,OAAA;kBAAQmO,KAAK,EAAC,WAAW;kBAAAtB,QAAA,EAAC;gBAAS;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C1N,OAAA;kBAAQmO,KAAK,EAAC,SAAS;kBAAAtB,QAAA,EAAC;gBAAO;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC1N,OAAA;kBAAQmO,KAAK,EAAC,SAAS;kBAAAtB,QAAA,EAAC;gBAAO;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1N,OAAA;cAAA6M,QAAA,gBACE7M,OAAA;gBAAO4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,OAAO;kBAAEG,YAAY,EAAE,QAAQ;kBAAEI,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAE7E;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1N,OAAA;gBACEmO,KAAK,EAAE5I,aAAa,CAACF,YAAa;gBAClCmL,QAAQ,EAAG9I,CAAC,IAAKlC,gBAAgB,CAAC0B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE7B,YAAY,EAAEqC,CAAC,CAAC6B,MAAM,CAAC4E;gBAAM,CAAC,CAAC,CAAE;gBACvFvH,KAAK,EAAE;kBACLgI,KAAK,EAAE,MAAM;kBACbhC,OAAO,EAAE,SAAS;kBAClBiB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBN,QAAQ,EAAE;gBACZ,CAAE;gBAAAT,QAAA,gBAEF7M,OAAA;kBAAQmO,KAAK,EAAC,EAAE;kBAAAtB,QAAA,EAAC;gBAAc;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC1N,OAAA;kBAAQmO,KAAK,EAAC,UAAU;kBAAAtB,QAAA,EAAC;gBAAQ;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1C1N,OAAA;kBAAQmO,KAAK,EAAC,gBAAgB;kBAAAtB,QAAA,EAAC;gBAAc;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtD1N,OAAA;kBAAQmO,KAAK,EAAC,OAAO;kBAAAtB,QAAA,EAAC;gBAAK;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC1N,OAAA;kBAAQmO,KAAK,EAAC,eAAe;kBAAAtB,QAAA,EAAC;gBAAa;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD1N,OAAA;kBAAQmO,KAAK,EAAC,iBAAiB;kBAAAtB,QAAA,EAAC;gBAAe;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxD1N,OAAA;kBAAQmO,KAAK,EAAC,iBAAiB;kBAAAtB,QAAA,EAAC;gBAAe;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1N,OAAA;cAAA6M,QAAA,gBACE7M,OAAA;gBAAO4G,KAAK,EAAE;kBAAEoG,OAAO,EAAE,OAAO;kBAAEG,YAAY,EAAE,QAAQ;kBAAEI,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAE7E;gBAAA3G,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1N,OAAA;gBACEmO,KAAK,EAAE5I,aAAa,CAACD,QAAS;gBAC9BkL,QAAQ,EAAG9I,CAAC,IAAKlC,gBAAgB,CAAC0B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE5B,QAAQ,EAAEoC,CAAC,CAAC6B,MAAM,CAAC4E;gBAAM,CAAC,CAAC,CAAE;gBACnFvH,KAAK,EAAE;kBACLgI,KAAK,EAAE,MAAM;kBACbhC,OAAO,EAAE,SAAS;kBAClBiB,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBN,QAAQ,EAAE;gBACZ,CAAE;gBAAAT,QAAA,gBAEF7M,OAAA;kBAAQmO,KAAK,EAAC,KAAK;kBAAAtB,QAAA,EAAC;gBAAG;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChC1N,OAAA;kBAAQmO,KAAK,EAAC,QAAQ;kBAAAtB,QAAA,EAAC;gBAAM;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC1N,OAAA;kBAAQmO,KAAK,EAAC,MAAM;kBAAAtB,QAAA,EAAC;gBAAI;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC1N,OAAA;kBAAQmO,KAAK,EAAC,QAAQ;kBAAAtB,QAAA,EAAC;gBAAM;kBAAA3G,QAAA,EAAAsH,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAxH,QAAA,EAAAsH,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAxH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1N,OAAA;cACE4F,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAE5I,aAAa,CAAClD,KAAM;cAC3BmO,QAAQ,EAAG9I,CAAC,IAAKlC,gBAAgB,CAAC0B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7E,KAAK,EAAEqF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAChFuC,WAAW,EAAC,0CAA0C;cACtD9J,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE;cACZ;YAAE;cAAApH,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAA6M,QAAA,gBACE7M,OAAA;cAAO4G,KAAK,EAAE;gBAAEoG,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1N,OAAA;cACEmO,KAAK,EAAE5I,aAAa,CAACjD,WAAY;cACjCkO,QAAQ,EAAG9I,CAAC,IAAKlC,gBAAgB,CAAC0B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5E,WAAW,EAAEoF,CAAC,CAAC6B,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cACtFuC,WAAW,EAAC,+DAA+D;cAC3EI,IAAI,EAAE,CAAE;cACRlK,KAAK,EAAE;gBACLgI,KAAK,EAAE,MAAM;gBACbhC,OAAO,EAAE,SAAS;gBAClBiB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBN,QAAQ,EAAE,MAAM;gBAChByD,MAAM,EAAE;cACV;YAAE;cAAA7K,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1N,OAAA;YAAK4G,KAAK,EAAE;cAAEoG,OAAO,EAAE,MAAM;cAAEK,GAAG,EAAE;YAAO,CAAE;YAAAR,QAAA,gBAC3C7M,OAAA;cACE2N,OAAO,EAAEA,CAAA,KAAM5I,sBAAsB,CAAC,KAAK,CAAE;cAC7C6B,KAAK,EAAE;gBACLmI,IAAI,EAAE,CAAC;gBACPnC,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,SAAS;gBAChBmL,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE,QAAQ;gBACtBE,MAAM,EAAE;cACV,CAAE;cAAAjB,QAAA,EACH;YAED;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1N,OAAA;cACE2N,OAAO,EAAEtB,sBAAuB;cAChCzF,KAAK,EAAE;gBACLmI,IAAI,EAAE,CAAC;gBACPnC,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAE,SAAS;gBACrBjK,KAAK,EAAE,OAAO;gBACdmL,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE,QAAQ;gBACtBE,MAAM,EAAE;cACV,CAAE;cAAAjB,QAAA,EACH;YAED;cAAA3G,QAAA,EAAAsH,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAxH,QAAA,EAAAsH,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAxH,QAAA,EAAAsH,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAxH,QAAA,EAAAsH,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAxH,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1N,OAAA;MAAA6M,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAA3G,QAAA,EAAAsH,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAxH,QAAA,EAAAsH,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtN,EAAA,CA/9GID,iBAAiB;AAAAiS,EAAA,GAAjBjS,iBAAiB;AAi+GvB,eAAeA,iBAAiB;AAAC,IAAAiS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}