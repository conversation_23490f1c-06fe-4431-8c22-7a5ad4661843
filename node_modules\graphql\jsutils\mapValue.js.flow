// @flow strict
import objectEntries from '../polyfills/objectEntries';

import type { ObjMap } from './ObjMap';

/**
 * Creates an object map with the same keys as `map` and values generated by
 * running each value of `map` thru `fn`.
 */
export default function mapValue<T, V>(
  map: ObjMap<T>,
  fn: (value: T, key: string) => V,
): ObjMap<V> {
  const result = Object.create(null);

  for (const [key, value] of objectEntries(map)) {
    result[key] = fn(value, key);
  }
  return result;
}
