
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visa DSA Questions</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #818cf8;
            --secondary: #f1f5f9;
            --easy: #22c55e;
            --medium: #f59e0b;
            --hard: #ef4444;
            --text: #0f172a;
            --text-light: #64748b;
            --card-bg: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--secondary);
            color: var(--text);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        h1 {
            color: var(--primary-dark);
            text-align: center;
            margin: 40px 0;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
            }
            to {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
            }
        }

        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 20px;
            border: 2px solid var(--primary-light);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: var(--card-bg);
            color: var(--text);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .difficulty-filter {
            display: flex;
            gap: 12px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            opacity: 0.8;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            opacity: 1;
        }

        .filter-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: var(--shadow);
        }

        .filter-btn.easy {
            background-color: var(--easy);
            color: white;
        }

        .filter-btn.medium {
            background-color: var(--medium);
            color: white;
        }

        .filter-btn.hard {
            background-color: var(--hard);
            color: white;
        }

        .topics-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 20px 0 30px;
        }

        .topic-tag {
            padding: 8px 16px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.8;
            transition: all 0.3s ease;
            user-select: none;
        }

        .topic-tag:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        .topic-tag.active {
            opacity: 1;
            background-color: var(--primary-dark);
            box-shadow: var(--shadow);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 16px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .difficulty {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .difficulty-easy { background-color: var(--easy); color: white; }
        .difficulty-medium { background-color: var(--medium); color: white; }
        .difficulty-hard { background-color: var(--hard); color: white; }

        .question-title {
            font-size: 1.2em;
            margin: 10px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        .question-link {
            color: var(--primary-dark);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .question-link:hover {
            color: var(--primary);
            text-decoration: underline;
        }

        .question-stats {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            font-size: 0.95em;
            color: var(--text-light);
            padding-top: 15px;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
        }

        .topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .topic-badge:hover {
            transform: translateY(-2px);
            background-color: var(--primary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            h1 {
                font-size: 2rem;
                margin: 30px 0;
            }

            .filters {
                flex-direction: column;
                gap: 15px;
            }

            .difficulty-filter {
                justify-content: center;
                width: 100%;
            }

            .search-box {
                width: 100%;
            }

            .stat-card {
                padding: 20px;
            }

            .question-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 2em;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 1.75rem;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .topic-tag {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .question-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling and selection styles */
        html {
            scroll-behavior: smooth;
        }

        ::selection {
            background-color: var(--primary-light);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Visa DSA Questions</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">161</div>
                <div>Total Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">45</div>
                <div>Easy Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">82</div>
                <div>Medium Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">34</div>
                <div>Hard Questions</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" placeholder="Search questions..." id="searchInput">
            <div class="difficulty-filter">
                <button class="filter-btn easy active" data-difficulty="EASY">Easy</button>
                <button class="filter-btn medium active" data-difficulty="MEDIUM">Medium</button>
                <button class="filter-btn hard active" data-difficulty="HARD">Hard</button>
            </div>
        </div>

        <div class="topics-filter">
            <div class="topic-tag" data-topic="Array">Array</div><div class="topic-tag" data-topic="Backtracking">Backtracking</div><div class="topic-tag" data-topic="Binary Indexed Tree">Binary Indexed Tree</div><div class="topic-tag" data-topic="Binary Search">Binary Search</div><div class="topic-tag" data-topic="Binary Tree">Binary Tree</div><div class="topic-tag" data-topic="Brainteaser">Brainteaser</div><div class="topic-tag" data-topic="Breadth-First Search">Breadth-First Search</div><div class="topic-tag" data-topic="Bucket Sort">Bucket Sort</div><div class="topic-tag" data-topic="Counting">Counting</div><div class="topic-tag" data-topic="Database">Database</div><div class="topic-tag" data-topic="Depth-First Search">Depth-First Search</div><div class="topic-tag" data-topic="Design">Design</div><div class="topic-tag" data-topic="Divide and Conquer">Divide and Conquer</div><div class="topic-tag" data-topic="Doubly-Linked List">Doubly-Linked List</div><div class="topic-tag" data-topic="Dynamic Programming">Dynamic Programming</div><div class="topic-tag" data-topic="Enumeration">Enumeration</div><div class="topic-tag" data-topic="Game Theory">Game Theory</div><div class="topic-tag" data-topic="Geometry">Geometry</div><div class="topic-tag" data-topic="Graph">Graph</div><div class="topic-tag" data-topic="Greedy">Greedy</div><div class="topic-tag" data-topic="Hash Function">Hash Function</div><div class="topic-tag" data-topic="Hash Table">Hash Table</div><div class="topic-tag" data-topic="Heap (Priority Queue)">Heap (Priority Queue)</div><div class="topic-tag" data-topic="Linked List">Linked List</div><div class="topic-tag" data-topic="Math">Math</div><div class="topic-tag" data-topic="Matrix">Matrix</div><div class="topic-tag" data-topic="Memoization">Memoization</div><div class="topic-tag" data-topic="Monotonic Queue">Monotonic Queue</div><div class="topic-tag" data-topic="Monotonic Stack">Monotonic Stack</div><div class="topic-tag" data-topic="Ordered Set">Ordered Set</div><div class="topic-tag" data-topic="Prefix Sum">Prefix Sum</div><div class="topic-tag" data-topic="Queue">Queue</div><div class="topic-tag" data-topic="Quickselect">Quickselect</div><div class="topic-tag" data-topic="Recursion">Recursion</div><div class="topic-tag" data-topic="Rolling Hash">Rolling Hash</div><div class="topic-tag" data-topic="Segment Tree">Segment Tree</div><div class="topic-tag" data-topic="Simulation">Simulation</div><div class="topic-tag" data-topic="Sliding Window">Sliding Window</div><div class="topic-tag" data-topic="Sorting">Sorting</div><div class="topic-tag" data-topic="Stack">Stack</div><div class="topic-tag" data-topic="String">String</div><div class="topic-tag" data-topic="String Matching">String Matching</div><div class="topic-tag" data-topic="Topological Sort">Topological Sort</div><div class="topic-tag" data-topic="Tree">Tree</div><div class="topic-tag" data-topic="Trie">Trie</div><div class="topic-tag" data-topic="Two Pointers">Two Pointers</div><div class="topic-tag" data-topic="Union Find">Union Find</div>
        </div>

        <div class="question-grid">

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming", "Memoization", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/length-of-longest-v-shaped-diagonal-segment" class="question-link" target="_blank">Length of Longest V-Shaped Diagonal Segment</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Memoization</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 38.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 96.9%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 96.3%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Counting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/split-the-array" class="question-link" target="_blank">Split the Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 95.2%</span>
                    <span>Acceptance: 59.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 92.1%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 92.1%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/create-target-array-in-the-given-order" class="question-link" target="_blank">Create Target Array in the Given Order</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.4%</span>
                    <span>Acceptance: 86.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-events-that-can-be-attended" class="question-link" target="_blank">Maximum Number of Events That Can Be Attended</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.2%</span>
                    <span>Acceptance: 32.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Counting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/split-the-array" class="question-link" target="_blank">Split the Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.1%</span>
                    <span>Acceptance: 59.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-furthest-houses-with-different-colors" class="question-link" target="_blank">Two Furthest Houses With Different Colors</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.8%</span>
                    <span>Acceptance: 65.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Dynamic Programming", "Brainteaser", "Game Theory"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/divisor-game" class="question-link" target="_blank">Divisor Game</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Brainteaser</span><span class="topic-badge">Game Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.8%</span>
                    <span>Acceptance: 69.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-nodes-with-the-highest-score" class="question-link" target="_blank">Count Nodes With the Highest Score</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.8%</span>
                    <span>Acceptance: 50.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/earliest-possible-day-of-full-bloom" class="question-link" target="_blank">Earliest Possible Day of Full Bloom</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.8%</span>
                    <span>Acceptance: 71.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/match-alphanumerical-pattern-in-matrix-i" class="question-link" target="_blank">Match Alphanumerical Pattern in Matrix I</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.8%</span>
                    <span>Acceptance: 63.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/odd-string-difference" class="question-link" target="_blank">Odd String Difference</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.8%</span>
                    <span>Acceptance: 60.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 84.2%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 83.4%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 81.3%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Trie"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-common-prefix" class="question-link" target="_blank">Longest Common Prefix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 81.3%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 81.3%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 81.3%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Trie"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-common-prefix" class="question-link" target="_blank">Longest Common Prefix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 81.0%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.0%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/text-justification" class="question-link" target="_blank">Text Justification</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.2%</span>
                    <span>Acceptance: 47.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.2%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.2%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-words-in-a-string" class="question-link" target="_blank">Reverse Words in a String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 50.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-events-that-can-be-attended" class="question-link" target="_blank">Maximum Number of Events That Can Be Attended</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 32.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Breadth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/walls-and-gates" class="question-link" target="_blank">Walls and Gates</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 62.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-substring" class="question-link" target="_blank">Longest Palindromic Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 35.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/create-target-array-in-the-given-order" class="question-link" target="_blank">Create Target Array in the Given Order</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 86.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "String Matching"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-string" class="question-link" target="_blank">Rotate String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">String Matching</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 63.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/text-justification" class="question-link" target="_blank">Text Justification</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 47.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-a-2d-matrix" class="question-link" target="_blank">Search a 2D Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 51.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Divide and Conquer"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/median-of-two-sorted-arrays" class="question-link" target="_blank">Median of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Divide and Conquer</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/daily-temperatures" class="question-link" target="_blank">Daily Temperatures</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 67.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/course-schedule" class="question-link" target="_blank">Course Schedule</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.1%</span>
                    <span>Acceptance: 48.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.8%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.8%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.8%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.8%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.7%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.1%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.1%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Trie"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-common-prefix" class="question-link" target="_blank">Longest Common Prefix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.1%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/daily-temperatures" class="question-link" target="_blank">Daily Temperatures</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.2%</span>
                    <span>Acceptance: 67.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Divide and Conquer"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/median-of-two-sorted-arrays" class="question-link" target="_blank">Median of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Divide and Conquer</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.2%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Breadth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/walls-and-gates" class="question-link" target="_blank">Walls and Gates</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.2%</span>
                    <span>Acceptance: 62.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-substring" class="question-link" target="_blank">Longest Palindromic Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.2%</span>
                    <span>Acceptance: 35.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.8%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-furthest-houses-with-different-colors" class="question-link" target="_blank">Two Furthest Houses With Different Colors</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 65.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/odd-string-difference" class="question-link" target="_blank">Odd String Difference</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 60.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/match-alphanumerical-pattern-in-matrix-i" class="question-link" target="_blank">Match Alphanumerical Pattern in Matrix I</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 63.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-nodes-with-the-highest-score" class="question-link" target="_blank">Count Nodes With the Highest Score</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 50.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Math", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/add-two-numbers" class="question-link" target="_blank">Add Two Numbers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 45.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-falling-path-sum" class="question-link" target="_blank">Minimum Falling Path Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 62.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-pairs-of-strings-with-concatenation-equal-to-target" class="question-link" target="_blank">Number of Pairs of Strings With Concatenation Equal to Target</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 75.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "Enumeration"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/consecutive-numbers-sum" class="question-link" target="_blank">Consecutive Numbers Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 42.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Math", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/roman-to-integer" class="question-link" target="_blank">Roman to Integer</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 64.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/largest-number" class="question-link" target="_blank">Largest Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 40.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Greedy", "Heap (Priority Queue)", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-servers-that-handled-most-number-of-requests" class="question-link" target="_blank">Find Servers That Handled Most Number of Requests</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 43.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String", "Backtracking", "Depth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-search" class="question-link" target="_blank">Word Search</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 44.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Binary Search", "Enumeration"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/split-message-based-on-limit" class="question-link" target="_blank">Split Message Based on Limit</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 43.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Math"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/plus-one" class="question-link" target="_blank">Plus One</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 47.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-sum-equals-k" class="question-link" target="_blank">Subarray Sum Equals K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-to-complete-trips" class="question-link" target="_blank">Minimum Time to Complete Trips</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 38.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/product-of-array-except-self" class="question-link" target="_blank">Product of Array Except Self</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/generate-parentheses" class="question-link" target="_blank">Generate Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 76.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Dynamic Programming", "Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-cameras" class="question-link" target="_blank">Binary Tree Cameras</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 47.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/earliest-possible-day-of-full-bloom" class="question-link" target="_blank">Earliest Possible Day of Full Bloom</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 71.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Dynamic Programming", "Brainteaser", "Game Theory"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/divisor-game" class="question-link" target="_blank">Divisor Game</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Brainteaser</span><span class="topic-badge">Game Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 69.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Rolling Hash", "String Matching", "Hash Function"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-palindrome" class="question-link" target="_blank">Shortest Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Rolling Hash</span><span class="topic-badge">String Matching</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 40.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock-with-cooldown" class="question-link" target="_blank">Best Time to Buy and Sell Stock with Cooldown</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 59.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Math", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/happy-number" class="question-link" target="_blank">Happy Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 57.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Queue", "Sliding Window", "Heap (Priority Queue)", "Ordered Set", "Monotonic Queue"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-continuous-subarray-with-absolute-diff-less-than-or-equal-to-limit" class="question-link" target="_blank">Longest Continuous Subarray With Absolute Diff Less Than or Equal to Limit</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Ordered Set</span><span class="topic-badge">Monotonic Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.8%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/text-justification" class="question-link" target="_blank">Text Justification</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.8%</span>
                    <span>Acceptance: 47.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.3%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.3%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.3%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Trie"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-common-prefix" class="question-link" target="_blank">Longest Common Prefix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.3%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/text-justification" class="question-link" target="_blank">Text Justification</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.3%</span>
                    <span>Acceptance: 47.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.3%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/course-schedule" class="question-link" target="_blank">Course Schedule</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 48.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-a-2d-matrix" class="question-link" target="_blank">Search a 2D Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 51.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "String Matching"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-string" class="question-link" target="_blank">Rotate String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">String Matching</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 63.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-words-in-a-string" class="question-link" target="_blank">Reverse Words in a String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 50.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String", "Backtracking", "Depth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-search" class="question-link" target="_blank">Word Search</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 44.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotating-the-box" class="question-link" target="_blank">Rotating the Box</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 79.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Queue", "Sliding Window", "Heap (Priority Queue)", "Ordered Set", "Monotonic Queue"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-continuous-subarray-with-absolute-diff-less-than-or-equal-to-limit" class="question-link" target="_blank">Longest Continuous Subarray With Absolute Diff Less Than or Equal to Limit</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Ordered Set</span><span class="topic-badge">Monotonic Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Binary Indexed Tree", "Segment Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/block-placement-queries" class="question-link" target="_blank">Block Placement Queries</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Binary Indexed Tree</span><span class="topic-badge">Segment Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 16.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Math"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/plus-one" class="question-link" target="_blank">Plus One</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 57.1%</span>
                    <span>Acceptance: 47.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/text-justification" class="question-link" target="_blank">Text Justification</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 55.4%</span>
                    <span>Acceptance: 47.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 55.4%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Trie"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-common-prefix" class="question-link" target="_blank">Longest Common Prefix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 55.4%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 55.4%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Queue", "Sliding Window", "Heap (Priority Queue)", "Ordered Set", "Monotonic Queue"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-continuous-subarray-with-absolute-diff-less-than-or-equal-to-limit" class="question-link" target="_blank">Longest Continuous Subarray With Absolute Diff Less Than or Equal to Limit</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Ordered Set</span><span class="topic-badge">Monotonic Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 55.4%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder" class="question-link" target="_blank">Word Ladder</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 55.4%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Binary Indexed Tree", "Segment Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/block-placement-queries" class="question-link" target="_blank">Block Placement Queries</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Binary Indexed Tree</span><span class="topic-badge">Segment Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 55.4%</span>
                    <span>Acceptance: 16.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 55.4%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Matrix"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/image-smoother" class="question-link" target="_blank">Image Smoother</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 68.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Sorting", "Heap (Priority Queue)", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-score-of-an-array-after-marking-all-elements" class="question-link" target="_blank">Find Score of an Array After Marking All Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 64.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Queue", "Sliding Window", "Heap (Priority Queue)", "Monotonic Queue"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sliding-window-maximum" class="question-link" target="_blank">Sliding Window Maximum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Monotonic Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 47.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder" class="question-link" target="_blank">Word Ladder</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-array" class="question-link" target="_blank">Rotate Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 42.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-difference" class="question-link" target="_blank">Minimum Time Difference</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 62.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/container-with-most-water" class="question-link" target="_blank">Container With Most Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Binary Indexed Tree", "Segment Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/block-placement-queries" class="question-link" target="_blank">Block Placement Queries</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Binary Indexed Tree</span><span class="topic-badge">Segment Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 16.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/restore-ip-addresses" class="question-link" target="_blank">Restore IP Addresses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 52.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/diameter-of-binary-tree" class="question-link" target="_blank">Diameter of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 62.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotating-the-box" class="question-link" target="_blank">Rotating the Box</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.9%</span>
                    <span>Acceptance: 79.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Matrix"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/image-smoother" class="question-link" target="_blank">Image Smoother</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 68.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Greedy", "Heap (Priority Queue)", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-servers-that-handled-most-number-of-requests" class="question-link" target="_blank">Find Servers That Handled Most Number of Requests</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 43.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Queue", "Sliding Window", "Heap (Priority Queue)", "Monotonic Queue"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sliding-window-maximum" class="question-link" target="_blank">Sliding Window Maximum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Monotonic Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 47.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Trie"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-the-length-of-the-longest-common-prefix" class="question-link" target="_blank">Find the Length of the Longest Common Prefix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 56.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Sorting", "Heap (Priority Queue)", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-score-of-an-array-after-marking-all-elements" class="question-link" target="_blank">Find Score of an Array After Marking All Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 64.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-to-complete-trips" class="question-link" target="_blank">Minimum Time to Complete Trips</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 38.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/product-of-array-except-self" class="question-link" target="_blank">Product of Array Except Self</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/restore-ip-addresses" class="question-link" target="_blank">Restore IP Addresses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 52.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Math", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/add-two-numbers" class="question-link" target="_blank">Add Two Numbers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 45.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-sum-equals-k" class="question-link" target="_blank">Subarray Sum Equals K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock-with-cooldown" class="question-link" target="_blank">Best Time to Buy and Sell Stock with Cooldown</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 59.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Bucket Sort", "Counting", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/top-k-frequent-elements" class="question-link" target="_blank">Top K Frequent Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Bucket Sort</span><span class="topic-badge">Counting</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/generate-parentheses" class="question-link" target="_blank">Generate Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 76.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/check-if-array-pairs-are-divisible-by-k" class="question-link" target="_blank">Check If Array Pairs Are Divisible by k</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 46.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "Enumeration"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/consecutive-numbers-sum" class="question-link" target="_blank">Consecutive Numbers Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 42.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-nodes-in-k-group" class="question-link" target="_blank">Reverse Nodes in k-Group</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 62.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-falling-path-sum" class="question-link" target="_blank">Minimum Falling Path Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 62.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-pairs-of-strings-with-concatenation-equal-to-target" class="question-link" target="_blank">Number of Pairs of Strings With Concatenation Equal to Target</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 75.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Dynamic Programming", "Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-cameras" class="question-link" target="_blank">Binary Tree Cameras</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 47.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/container-with-most-water" class="question-link" target="_blank">Container With Most Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Math", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/roman-to-integer" class="question-link" target="_blank">Roman to Integer</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 64.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/palindrome-number" class="question-link" target="_blank">Palindrome Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 58.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Divide and Conquer", "Geometry", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/k-closest-points-to-origin" class="question-link" target="_blank">K Closest Points to Origin</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Geometry</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock-iii" class="question-link" target="_blank">Best Time to Buy and Sell Stock III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 50.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Matrix", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/spiral-matrix" class="question-link" target="_blank">Spiral Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Matrix</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 52.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Rolling Hash", "String Matching", "Hash Function"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-palindrome" class="question-link" target="_blank">Shortest Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Rolling Hash</span><span class="topic-badge">String Matching</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 40.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder" class="question-link" target="_blank">Word Ladder</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/largest-number" class="question-link" target="_blank">Largest Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 40.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-array" class="question-link" target="_blank">Rotate Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 42.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-vowels-of-a-string" class="question-link" target="_blank">Reverse Vowels of a String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 57.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-peak-element" class="question-link" target="_blank">Find Peak Element</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 46.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Math", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/happy-number" class="question-link" target="_blank">Happy Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 57.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/second-highest-salary" class="question-link" target="_blank">Second Highest Salary</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 43.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/diameter-of-binary-tree" class="question-link" target="_blank">Diameter of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 62.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Binary Search", "Enumeration"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/split-message-based-on-limit" class="question-link" target="_blank">Split Message Based on Limit</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 43.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-palindrome" class="question-link" target="_blank">Valid Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 50.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-anagram" class="question-link" target="_blank">Valid Anagram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 66.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-difference" class="question-link" target="_blank">Minimum Time Difference</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.2%</span>
                    <span>Acceptance: 62.3%</span>
                </div>
            </div>

        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const questionCards = document.querySelectorAll('.question-card');
        const difficultyButtons = document.querySelectorAll('.filter-btn');
        const topicTags = document.querySelectorAll('.topic-tag');
        
        function filterQuestions() {
            const searchTerm = searchInput.value.toLowerCase();
            const activeDifficulties = Array.from(difficultyButtons)
                .filter(btn => btn.classList.contains('active'))
                .map(btn => btn.dataset.difficulty);
            const activeTopics = Array.from(topicTags)
                .filter(tag => tag.classList.contains('active'))
                .map(tag => tag.dataset.topic);
            
            questionCards.forEach(card => {
                const title = card.querySelector('.question-title').textContent.toLowerCase();
                const difficulty = card.dataset.difficulty;
                const topics = JSON.parse(card.dataset.topics);
                
                const matchesSearch = title.includes(searchTerm);
                const matchesDifficulty = activeDifficulties.includes(difficulty);
                const matchesTopics = activeTopics.length === 0 || 
                    topics.some(topic => activeTopics.includes(topic));
                
                if (matchesSearch && matchesDifficulty && matchesTopics) {
                    card.style.display = 'flex';
                    card.style.opacity = '1';
                } else {
                    card.style.display = 'none';
                    card.style.opacity = '0';
                }
            });
        }
        
        // Add smooth transitions for filtering
        questionCards.forEach(card => {
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        });
        
        searchInput.addEventListener('input', filterQuestions);
        
        difficultyButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                btn.classList.toggle('active');
                filterQuestions();
            });
        });
        
        topicTags.forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('active');
                filterQuestions();
            });
        });

        // Add smooth scrolling for all links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
