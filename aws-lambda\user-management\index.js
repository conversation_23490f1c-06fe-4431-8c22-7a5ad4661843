// AWS Lambda function for user management with DynamoDB
const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.USERS_TABLE || 'EduNova-Users';

exports.handler = async (event) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        const { httpMethod, pathParameters, body } = event;
        
        switch (httpMethod) {
            case 'POST':
                return await createUser(JSON.parse(body), headers);
            case 'GET':
                return await getUser(pathParameters.userId, headers);
            case 'PUT':
                return await updateUser(pathParameters.userId, JSON.parse(body), headers);
            case 'DELETE':
                return await deleteUser(pathParameters.userId, headers);
            default:
                return {
                    statusCode: 405,
                    headers,
                    body: JSON.stringify({ error: 'Method not allowed' })
                };
        }
    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Internal server error',
                details: error.message 
            })
        };
    }
};

// Create a new user
async function createUser(userData, headers) {
    try {
        const { email, createdAt, profilePic, isVerified } = userData;
        
        if (!email) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Email is required' })
            };
        }

        const user = {
            userId: email, // Using email as primary key
            email,
            createdAt: createdAt || new Date().toISOString(),
            profilePic: profilePic || null,
            isVerified: isVerified || false,
            lastLogin: null,
            preferences: {
                theme: 'light',
                notifications: true,
                language: 'en'
            },
            progress: {
                totalQuizzes: 0,
                completedQuizzes: 0,
                totalStudyTime: 0,
                streak: 0
            }
        };

        const params = {
            TableName: TABLE_NAME,
            Item: user,
            ConditionExpression: 'attribute_not_exists(userId)' // Prevent duplicate users
        };

        await dynamodb.put(params).promise();

        return {
            statusCode: 201,
            headers,
            body: JSON.stringify({ 
                success: true, 
                data: user 
            })
        };

    } catch (error) {
        if (error.code === 'ConditionalCheckFailedException') {
            return {
                statusCode: 409,
                headers,
                body: JSON.stringify({ error: 'User already exists' })
            };
        }
        throw error;
    }
}

// Get user by ID
async function getUser(userId, headers) {
    try {
        if (!userId) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'User ID is required' })
            };
        }

        const params = {
            TableName: TABLE_NAME,
            Key: { userId }
        };

        const result = await dynamodb.get(params).promise();

        if (!result.Item) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({ error: 'User not found' })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ 
                success: true, 
                data: result.Item 
            })
        };

    } catch (error) {
        throw error;
    }
}

// Update user
async function updateUser(userId, updateData, headers) {
    try {
        if (!userId) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'User ID is required' })
            };
        }

        // Build update expression dynamically
        const updateExpression = [];
        const expressionAttributeNames = {};
        const expressionAttributeValues = {};

        Object.keys(updateData).forEach((key, index) => {
            const attributeName = `#attr${index}`;
            const attributeValue = `:val${index}`;
            
            updateExpression.push(`${attributeName} = ${attributeValue}`);
            expressionAttributeNames[attributeName] = key;
            expressionAttributeValues[attributeValue] = updateData[key];
        });

        // Add updatedAt timestamp
        updateExpression.push('#updatedAt = :updatedAt');
        expressionAttributeNames['#updatedAt'] = 'updatedAt';
        expressionAttributeValues[':updatedAt'] = new Date().toISOString();

        const params = {
            TableName: TABLE_NAME,
            Key: { userId },
            UpdateExpression: `SET ${updateExpression.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW'
        };

        const result = await dynamodb.update(params).promise();

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ 
                success: true, 
                data: result.Attributes 
            })
        };

    } catch (error) {
        throw error;
    }
}

// Delete user
async function deleteUser(userId, headers) {
    try {
        if (!userId) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'User ID is required' })
            };
        }

        const params = {
            TableName: TABLE_NAME,
            Key: { userId },
            ReturnValues: 'ALL_OLD'
        };

        const result = await dynamodb.delete(params).promise();

        if (!result.Attributes) {
            return {
                statusCode: 404,
                headers,
                body: JSON.stringify({ error: 'User not found' })
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ 
                success: true, 
                message: 'User deleted successfully' 
            })
        };

    } catch (error) {
        throw error;
    }
}
