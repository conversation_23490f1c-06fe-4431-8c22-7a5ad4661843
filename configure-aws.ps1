# AWS CLI Configuration Helper for PowerShell

Write-Host "🔧 AWS CLI Configuration Helper" -ForegroundColor Cyan
Write-Host ""
Write-Host "This script will help you configure AWS CLI with your credentials." -ForegroundColor Yellow
Write-Host ""
Write-Host "Before running this, make sure you have:" -ForegroundColor White
Write-Host "  1. AWS Account created" -ForegroundColor Green
Write-Host "  2. IAM User created with AdministratorAccess policy" -ForegroundColor Green
Write-Host "  3. Access Key ID and Secret Access Key ready" -ForegroundColor Green
Write-Host ""

$continue = Read-Host "Do you have your AWS credentials ready? (y/n)"
if ($continue -ne "y" -and $continue -ne "Y") {
    Write-Host "Please get your AWS credentials first, then run this script again." -ForegroundColor Red
    exit
}

# Add AWS CLI to PATH for this session
$env:PATH += ";C:\Program Files\Amazon\AWSCLIV2"

Write-Host ""
Write-Host "🚀 Starting AWS CLI configuration..." -ForegroundColor Cyan
Write-Host ""
Write-Host "Please enter your AWS credentials when prompted:" -ForegroundColor Yellow
Write-Host "  - AWS Access Key ID: [Your Access Key]" -ForegroundColor White
Write-Host "  - AWS Secret Access Key: [Your Secret Key]" -ForegroundColor White
Write-Host "  - Default region name: us-east-1" -ForegroundColor White
Write-Host "  - Default output format: json" -ForegroundColor White
Write-Host ""

# Configure AWS CLI
& aws configure

Write-Host ""
Write-Host "🧪 Testing configuration..." -ForegroundColor Cyan

# Test the configuration
$testResult = & aws sts get-caller-identity 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "✅ AWS CLI configured successfully!" -ForegroundColor Green
    Write-Host "🚀 Ready to create AWS resources!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your AWS Account Info:" -ForegroundColor Cyan
    Write-Host $testResult
    Write-Host ""
    Write-Host "Next step: Run 'node manual-aws-setup.js' to create AWS resources" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "❌ Configuration failed. Please check your credentials and try again." -ForegroundColor Red
    Write-Host ""
    Write-Host "Error details:" -ForegroundColor Yellow
    Write-Host $testResult -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "  1. Verify your Access Key ID and Secret Access Key" -ForegroundColor White
    Write-Host "  2. Ensure the IAM user has proper permissions" -ForegroundColor White
    Write-Host "  3. Check if the AWS account is active" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
