{"ast": null, "code": "// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nexport default class Serializer {\n  constructor() {\n    this.HEADER_LENGTH = 1;\n  }\n  decode(rawPayload, callback) {\n    if (rawPayload.constructor === ArrayBuffer) {\n      return callback(this._binaryDecode(rawPayload));\n    }\n    if (typeof rawPayload === 'string') {\n      return callback(JSON.parse(rawPayload));\n    }\n    return callback({});\n  }\n  _binaryDecode(buffer) {\n    const view = new DataView(buffer);\n    const decoder = new TextDecoder();\n    return this._decodeBroadcast(buffer, view, decoder);\n  }\n  _decodeBroadcast(buffer, view, decoder) {\n    const topicSize = view.getUint8(1);\n    const eventSize = view.getUint8(2);\n    let offset = this.HEADER_LENGTH + 2;\n    const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n    offset = offset + topicSize;\n    const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n    offset = offset + eventSize;\n    const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n    return {\n      ref: null,\n      topic: topic,\n      event: event,\n      payload: data\n    };\n  }\n}", "map": {"version": 3, "names": ["Serializer", "constructor", "HEADER_LENGTH", "decode", "rawPayload", "callback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_binaryDecode", "JSON", "parse", "buffer", "view", "DataView", "decoder", "TextDecoder", "_decodeBroadcast", "topicSize", "getUint8", "eventSize", "offset", "topic", "slice", "event", "data", "byteLength", "ref", "payload"], "sources": ["C:\\Users\\<USER>\\Downloads\\quiz\\aich (4)\\aich (3)\\aich(5)\\node_modules\\@supabase\\realtime-js\\src\\lib\\serializer.ts"], "sourcesContent": ["// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\n\nexport default class Serializer {\n  HEADER_LENGTH = 1\n\n  decode(rawPayload: ArrayBuffer | string, callback: Function) {\n    if (rawPayload.constructor === ArrayBuffer) {\n      return callback(this._binaryDecode(rawPayload))\n    }\n\n    if (typeof rawPayload === 'string') {\n      return callback(JSON.parse(rawPayload))\n    }\n\n    return callback({})\n  }\n\n  private _binaryDecode(buffer: ArrayBuffer) {\n    const view = new DataView(buffer)\n    const decoder = new TextDecoder()\n\n    return this._decodeBroadcast(buffer, view, decoder)\n  }\n\n  private _decodeBroadcast(\n    buffer: <PERSON><PERSON><PERSON><PERSON><PERSON>er,\n    view: DataView,\n    decoder: TextDecoder\n  ): {\n    ref: null\n    topic: string\n    event: string\n    payload: { [key: string]: any }\n  } {\n    const topicSize = view.getUint8(1)\n    const eventSize = view.getUint8(2)\n    let offset = this.HEADER_LENGTH + 2\n    const topic = decoder.decode(buffer.slice(offset, offset + topicSize))\n    offset = offset + topicSize\n    const event = decoder.decode(buffer.slice(offset, offset + eventSize))\n    offset = offset + eventSize\n    const data = JSON.parse(\n      decoder.decode(buffer.slice(offset, buffer.byteLength))\n    )\n\n    return { ref: null, topic: topic, event: event, payload: data }\n  }\n}\n"], "mappings": "AAAA;AACA;AAEA,eAAc,MAAOA,UAAU;EAA/BC,YAAA;IACE,KAAAC,aAAa,GAAG,CAAC;EA4CnB;EA1CEC,MAAMA,CAACC,UAAgC,EAAEC,QAAkB;IACzD,IAAID,UAAU,CAACH,WAAW,KAAKK,WAAW,EAAE;MAC1C,OAAOD,QAAQ,CAAC,IAAI,CAACE,aAAa,CAACH,UAAU,CAAC,CAAC;;IAGjD,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MAClC,OAAOC,QAAQ,CAACG,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,CAAC;;IAGzC,OAAOC,QAAQ,CAAC,EAAE,CAAC;EACrB;EAEQE,aAAaA,CAACG,MAAmB;IACvC,MAAMC,IAAI,GAAG,IAAIC,QAAQ,CAACF,MAAM,CAAC;IACjC,MAAMG,OAAO,GAAG,IAAIC,WAAW,EAAE;IAEjC,OAAO,IAAI,CAACC,gBAAgB,CAACL,MAAM,EAAEC,IAAI,EAAEE,OAAO,CAAC;EACrD;EAEQE,gBAAgBA,CACtBL,MAAmB,EACnBC,IAAc,EACdE,OAAoB;IAOpB,MAAMG,SAAS,GAAGL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAClC,MAAMC,SAAS,GAAGP,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAClC,IAAIE,MAAM,GAAG,IAAI,CAACjB,aAAa,GAAG,CAAC;IACnC,MAAMkB,KAAK,GAAGP,OAAO,CAACV,MAAM,CAACO,MAAM,CAACW,KAAK,CAACF,MAAM,EAAEA,MAAM,GAAGH,SAAS,CAAC,CAAC;IACtEG,MAAM,GAAGA,MAAM,GAAGH,SAAS;IAC3B,MAAMM,KAAK,GAAGT,OAAO,CAACV,MAAM,CAACO,MAAM,CAACW,KAAK,CAACF,MAAM,EAAEA,MAAM,GAAGD,SAAS,CAAC,CAAC;IACtEC,MAAM,GAAGA,MAAM,GAAGD,SAAS;IAC3B,MAAMK,IAAI,GAAGf,IAAI,CAACC,KAAK,CACrBI,OAAO,CAACV,MAAM,CAACO,MAAM,CAACW,KAAK,CAACF,MAAM,EAAET,MAAM,CAACc,UAAU,CAAC,CAAC,CACxD;IAED,OAAO;MAAEC,GAAG,EAAE,IAAI;MAAEL,KAAK,EAAEA,KAAK;MAAEE,KAAK,EAAEA,KAAK;MAAEI,OAAO,EAAEH;IAAI,CAAE;EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}