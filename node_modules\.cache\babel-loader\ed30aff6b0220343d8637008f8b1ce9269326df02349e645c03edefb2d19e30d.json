{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(5)\\\\src\\\\AuthPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\nconst AuthPage = ({\n  onAuthSuccess\n}) => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY).then(response => {\n      console.log('OTP sent!', response.status, response.text);\n    }, err => {\n      console.error('OTP sending failed:', err);\n    });\n  };\n  const handleProfilePicChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic\n      });\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setIsOtpSent(false);\n      setOtp('');\n      setIsSignupMode(false);\n      setError('');\n      alert('OTP Verified! You can now login with your credentials.');\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Login / Signup\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      value: email,\n      onChange: e => setEmail(e.target.value),\n      placeholder: \"Email\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 93\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      value: password,\n      onChange: e => setPassword(e.target.value),\n      type: \"password\",\n      placeholder: \"Password\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 118\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      onChange: handleProfilePicChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 62\n    }, this), profilePic && /*#__PURE__*/_jsxDEV(\"img\", {\n      src: profilePic,\n      alt: \"Profile Pic\",\n      style: {\n        width: '100px',\n        height: '100px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 22\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 109\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleSignup,\n      children: \"Signup\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleLogin,\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), isOtpSent && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Enter OTP Sent to Your Email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        value: otp,\n        onChange: e => setOtp(e.target.value),\n        placeholder: \"Enter OTP\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 97\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleOtpVerification,\n        children: \"Verify OTP\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: 'red'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthPage, \"EphZMW3pd6sf1W0TnlKPpZiisVk=\");\n_c = AuthPage;\nexport default AuthPage;\nvar _c;\n$RefreshReg$(_c, \"AuthPage\");", "map": {"version": 3, "names": ["React", "useState", "initializeApp", "getAuth", "createUserWithEmailAndPassword", "signInWithEmailAndPassword", "getFirestore", "doc", "setDoc", "emailjs", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth", "db", "EMAILJS_SERVICE_ID", "EMAILJS_TEMPLATE_ID", "EMAILJS_PUBLIC_KEY", "AuthPage", "onAuthSuccess", "_s", "email", "setEmail", "password", "setPassword", "error", "setError", "otp", "setOtp", "generatedOtp", "setGeneratedOtp", "isOtpSent", "setIsOtpSent", "profilePic", "setProfilePic", "isLoading", "setIsLoading", "isSignupMode", "setIsSignupMode", "generateOtp", "Math", "floor", "random", "sendOtpEmail", "templateParams", "to_email", "send", "then", "response", "console", "log", "status", "text", "err", "handleProfilePicChange", "e", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSignup", "userCredential", "userRef", "user", "uid", "createdAt", "Date", "dp", "message", "handleOtpVerification", "toString", "alert", "handleLogin", "style", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "placeholder", "type", "src", "alt", "width", "height", "onClick", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/AuthPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';\nimport { getFirestore, doc, setDoc } from 'firebase/firestore';\nimport emailjs from 'emailjs-com';\nimport styles from './styles';\n\n// Firebase Config\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDmRAxMJvEKmel3JpUF1dXbBNccQJibcGo\",\n  authDomain: \"exploit-f41eb.firebaseapp.com\",\n  projectId: \"exploit-f41eb\",\n  storageBucket: \"exploit-f41eb.firebasestorage.app\",\n  messagingSenderId: \"849491574761\",\n  appId: \"1:849491574761:web:22ddb4ea62063d7a709578\",\n  measurementId: \"G-R0SWYQKXCV\"\n};\n\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// EmailJS Config\nconst EMAILJS_SERVICE_ID = 'service_sthdzci';\nconst EMAILJS_TEMPLATE_ID = 'template_8mnpzqq';\nconst EMAILJS_PUBLIC_KEY = 'jEfQd05ItbAthM3QN';\n\nconst AuthPage = ({ onAuthSuccess }) => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [otp, setOtp] = useState('');\n  const [generatedOtp, setGeneratedOtp] = useState('');\n  const [isOtpSent, setIsOtpSent] = useState(false);\n  const [profilePic, setProfilePic] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignupMode, setIsSignupMode] = useState(true);\n\n  const generateOtp = () => {\n    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP\n    setGeneratedOtp(otp);\n    return otp;\n  };\n\n  const sendOtpEmail = (email, otp) => {\n    const templateParams = {\n      to_email: email,\n      otp: otp\n    };\n\n    emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY)\n      .then((response) => {\n        console.log('OTP sent!', response.status, response.text);\n      }, (err) => {\n        console.error('OTP sending failed:', err);\n      });\n  };\n\n  const handleProfilePicChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePic(reader.result); // Store image in base64\n      };\n      reader.readAsDataURL(file); // Convert file to base64\n    }\n  };\n\n  const handleSignup = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      const otp = generateOtp();\n      sendOtpEmail(email, otp);\n      setIsOtpSent(true);\n\n      // Save user data to Firestore\n      const userRef = doc(db, \"users\", userCredential.user.uid);\n      await setDoc(userRef, {\n        email: email,\n        uid: userCredential.user.uid,\n        createdAt: new Date(),\n        dp: profilePic,\n      });\n\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleOtpVerification = () => {\n    if (otp === generatedOtp.toString()) {\n      setIsOtpSent(false);\n      setOtp('');\n      setIsSignupMode(false);\n      setError('');\n      alert('OTP Verified! You can now login with your credentials.');\n    } else {\n      setError('Invalid OTP. Please try again.');\n    }\n  };\n\n  const handleLogin = async () => {\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      onAuthSuccess();\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div style={{ padding: '20px' }}>\n      <h2>Login / Signup</h2>\n      <input value={email} onChange={(e) => setEmail(e.target.value)} placeholder=\"Email\" /><br />\n      <input value={password} onChange={(e) => setPassword(e.target.value)} type=\"password\" placeholder=\"Password\" /><br />\n\n      {/* Profile Picture Upload */}\n      <input type=\"file\" onChange={handleProfilePicChange} /><br />\n      {profilePic && <img src={profilePic} alt=\"Profile Pic\" style={{ width: '100px', height: '100px' }} />}<br />\n\n      <button onClick={handleSignup}>Signup</button>\n      <button onClick={handleLogin}>Login</button>\n\n      {isOtpSent && (\n        <>\n          <h3>Enter OTP Sent to Your Email</h3>\n          <input value={otp} onChange={(e) => setOtp(e.target.value)} placeholder=\"Enter OTP\" /><br />\n          <button onClick={handleOtpVerification}>Verify OTP</button>\n        </>\n      )}\n\n      {error && <p style={{ color: 'red' }}>{error}</p>}\n    </div>\n  );\n};\n\nexport default AuthPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,8BAA8B,EAAEC,0BAA0B,QAAQ,eAAe;AACnG,SAASC,YAAY,EAAEC,GAAG,EAAEC,MAAM,QAAQ,oBAAoB;AAC9D,OAAOC,OAAO,MAAM,aAAa;AACjC,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,mCAAmC;EAClDC,iBAAiB,EAAE,cAAc;EACjCC,KAAK,EAAE,2CAA2C;EAClDC,aAAa,EAAE;AACjB,CAAC;AAED,MAAMC,GAAG,GAAGrB,aAAa,CAACa,cAAc,CAAC;AACzC,MAAMS,IAAI,GAAGrB,OAAO,CAACoB,GAAG,CAAC;AACzB,MAAME,EAAE,GAAGnB,YAAY,CAACiB,GAAG,CAAC;;AAE5B;AACA,MAAMG,kBAAkB,GAAG,iBAAiB;AAC5C,MAAMC,mBAAmB,GAAG,kBAAkB;AAC9C,MAAMC,kBAAkB,GAAG,mBAAmB;AAE9C,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqC,GAAG,EAAEC,MAAM,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMiD,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMZ,GAAG,GAAGa,IAAI,CAACC,KAAK,CAAC,MAAM,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IACzDZ,eAAe,CAACH,GAAG,CAAC;IACpB,OAAOA,GAAG;EACZ,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAACtB,KAAK,EAAEM,GAAG,KAAK;IACnC,MAAMiB,cAAc,GAAG;MACrBC,QAAQ,EAAExB,KAAK;MACfM,GAAG,EAAEA;IACP,CAAC;IAED7B,OAAO,CAACgD,IAAI,CAAC/B,kBAAkB,EAAEC,mBAAmB,EAAE4B,cAAc,EAAE3B,kBAAkB,CAAC,CACtF8B,IAAI,CAAEC,QAAQ,IAAK;MAClBC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,QAAQ,CAACG,MAAM,EAAEH,QAAQ,CAACI,IAAI,CAAC;IAC1D,CAAC,EAAGC,GAAG,IAAK;MACVJ,OAAO,CAACxB,KAAK,CAAC,qBAAqB,EAAE4B,GAAG,CAAC;IAC3C,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAIC,CAAC,IAAK;IACpC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvB3B,aAAa,CAACyB,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC3C,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMuC,cAAc,GAAG,MAAMxE,8BAA8B,CAACoB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAClF,MAAMI,GAAG,GAAGY,WAAW,CAAC,CAAC;MACzBI,YAAY,CAACtB,KAAK,EAAEM,GAAG,CAAC;MACxBK,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMkC,OAAO,GAAGtE,GAAG,CAACkB,EAAE,EAAE,OAAO,EAAEmD,cAAc,CAACE,IAAI,CAACC,GAAG,CAAC;MACzD,MAAMvE,MAAM,CAACqE,OAAO,EAAE;QACpB7C,KAAK,EAAEA,KAAK;QACZ+C,GAAG,EAAEH,cAAc,CAACE,IAAI,CAACC,GAAG;QAC5BC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,EAAE,EAAEtC;MACN,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC+C,OAAO,CAAC;IACzB,CAAC,SAAS;MACRpC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMqC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI9C,GAAG,KAAKE,YAAY,CAAC6C,QAAQ,CAAC,CAAC,EAAE;MACnC1C,YAAY,CAAC,KAAK,CAAC;MACnBJ,MAAM,CAAC,EAAE,CAAC;MACVU,eAAe,CAAC,KAAK,CAAC;MACtBZ,QAAQ,CAAC,EAAE,CAAC;MACZiD,KAAK,CAAC,wDAAwD,CAAC;IACjE,CAAC,MAAM;MACLjD,QAAQ,CAAC,gCAAgC,CAAC;IAC5C;EACF,CAAC;EAED,MAAMkD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACvD,KAAK,IAAI,CAACE,QAAQ,EAAE;MACvBG,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMuC,cAAc,GAAG,MAAMvE,0BAA0B,CAACmB,IAAI,EAAEQ,KAAK,EAAEE,QAAQ,CAAC;MAC9EJ,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC+C,OAAO,CAAC;IACzB,CAAC,SAAS;MACRpC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEnC,OAAA;IAAK4E,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9B9E,OAAA;MAAA8E,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvBlF,OAAA;MAAOmF,KAAK,EAAE/D,KAAM;MAACgE,QAAQ,EAAG9B,CAAC,IAAKjC,QAAQ,CAACiC,CAAC,CAACE,MAAM,CAAC2B,KAAK,CAAE;MAACE,WAAW,EAAC;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAAAlF,OAAA;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC5FlF,OAAA;MAAOmF,KAAK,EAAE7D,QAAS;MAAC8D,QAAQ,EAAG9B,CAAC,IAAK/B,WAAW,CAAC+B,CAAC,CAACE,MAAM,CAAC2B,KAAK,CAAE;MAACG,IAAI,EAAC,UAAU;MAACD,WAAW,EAAC;IAAU;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAAAlF,OAAA;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAGrHlF,OAAA;MAAOsF,IAAI,EAAC,MAAM;MAACF,QAAQ,EAAE/B;IAAuB;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAAAlF,OAAA;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EAC5DlD,UAAU,iBAAIhC,OAAA;MAAKuF,GAAG,EAAEvD,UAAW;MAACwD,GAAG,EAAC,aAAa;MAACZ,KAAK,EAAE;QAAEa,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAQ;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAAClF,OAAA;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAE5GlF,OAAA;MAAQ2F,OAAO,EAAE5B,YAAa;MAAAe,QAAA,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC9ClF,OAAA;MAAQ2F,OAAO,EAAEhB,WAAY;MAAAG,QAAA,EAAC;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAE3CpD,SAAS,iBACR9B,OAAA,CAAAE,SAAA;MAAA4E,QAAA,gBACE9E,OAAA;QAAA8E,QAAA,EAAI;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrClF,OAAA;QAAOmF,KAAK,EAAEzD,GAAI;QAAC0D,QAAQ,EAAG9B,CAAC,IAAK3B,MAAM,CAAC2B,CAAC,CAACE,MAAM,CAAC2B,KAAK,CAAE;QAACE,WAAW,EAAC;MAAW;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAAAlF,OAAA;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5FlF,OAAA;QAAQ2F,OAAO,EAAEnB,qBAAsB;QAAAM,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA,eAC3D,CACH,EAEA1D,KAAK,iBAAIxB,OAAA;MAAG4E,KAAK,EAAE;QAAEgB,KAAK,EAAE;MAAM,CAAE;MAAAd,QAAA,EAAEtD;IAAK;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9C,CAAC;AAEV,CAAC;AAAC/D,EAAA,CAhIIF,QAAQ;AAAA4E,EAAA,GAAR5E,QAAQ;AAkId,eAAeA,QAAQ;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}