{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich(5)\\\\src\\\\EduAIChatBot.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport Faq from './Faq';\nimport Exams from \"./Exams\";\nimport Coding from \"./Coding\";\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport globalStyles from './styles.js';\nimport { FiMenu, FiX, FiChevronDown, FiChevronRight, FiFileText, FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield, FiSearch, FiUpload, FiLogIn, FiLogOut, FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink, FiHeart, FiClock, FiFilter, FiStar, FiRefreshCw, FiTrendingUp } from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport { Bar } from 'react-chartjs-2';\nimport { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';\nimport ReactMarkdown from 'react-markdown';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nChart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n\n// Enhanced sidebar items with icons\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": /*#__PURE__*/_jsxDEV(FiFileText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 15\n    }, this),\n    \"dsa\": /*#__PURE__*/_jsxDEV(FiCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 12\n    }, this),\n    \"coding\": /*#__PURE__*/_jsxDEV(FiLayers, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 15\n    }, this),\n    \"resources\": /*#__PURE__*/_jsxDEV(FiBriefcase, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 18\n    }, this),\n    \"quizzes\": /*#__PURE__*/_jsxDEV(FiCheckCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 16\n    }, this),\n    \"aptitude\": /*#__PURE__*/_jsxDEV(FiBarChart2, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 17\n    }, this),\n    \"academics\": /*#__PURE__*/_jsxDEV(FiBook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 18\n    }, this),\n    \"faq\": /*#__PURE__*/_jsxDEV(FiHelpCircle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 12\n    }, this),\n    \"admin\": /*#__PURE__*/_jsxDEV(FiShield, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 14\n    }, this)\n  };\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || /*#__PURE__*/_jsxDEV(FiAward, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 69\n    }, this)\n  };\n});\nconst EduAIChatBot = () => {\n  _s();\n  // State declarations\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const ADMIN_EMAIL = '<EMAIL>';\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const chatEndRef = useRef(null);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // API configurations\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\", \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\", \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\", \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\", \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\", \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\", \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\", \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\", \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\", \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\", \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\", \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\", \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\", \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\", \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\", \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\", \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\", \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\", \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\", \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\", \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\", \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\", \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\", \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\", \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\", \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\", \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\", \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\", \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\", \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\", \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\", \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\", \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\", \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\", \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\", \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\", \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\", \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\", \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\", \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\", \"Zomato\", \"ZScaler\", \"Zopsmart\"];\n\n  // Quiz buttons data\n  const quizButtons = [{\n    title: \"OP and CN Quiz\",\n    description: \"Test your knowledge of Operating System and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"OOPs and DBMS Quiz\",\n    description: \"Challenge yourself with oops and dbms\",\n    link: \"https://oopsanddbms.netlify.app/\"\n  }, {\n    title: \"System Design Quiz\",\n    description: \"Test your system design knowledge\",\n    link: \"https://system-design041.netlify.app\"\n  }, {\n    title: \"Quantitative Aptitude and Reasoning Quiz\",\n    description: \"Practice common quant and reasoning questions\",\n    link: \"https://quantandreasoning.netlify.app\"\n  }, {\n    title: \"Cloud & DevOps Quiz\",\n    description: \"Test your knowledge of Cloud and DevOps concepts\",\n    link: \"https://cloud-devops.netlify.app\"\n  }, {\n    title: \"DSA Quiz\",\n    description: \"Data Structures and Algorithms quiz\",\n    link: \"https://dsa041.netlify.app\"\n  }, {\n    title: \"Operating System & Computer Networks Quiz\",\n    description: \"Quiz on OS and Computer Networks\",\n    link: \"https://opcn.netlify.app\"\n  }, {\n    title: \"Web Development Quiz\",\n    description: \"Quiz on Web Development topics\",\n    link: \"https://web-dev041.netlify.app\"\n  }];\n\n  // Use centralized styles\n  const styles = {\n    ...globalStyles,\n    appContainer: {\n      ...globalStyles.appContainer,\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text\n    },\n    navbar: {\n      ...globalStyles.navbarFixed,\n      borderBottom: `1px solid ${globalStyles.currentTheme.border}`\n    },\n    sidebar: {\n      ...globalStyles.sidebarFixed,\n      backgroundColor: globalStyles.currentTheme.surface,\n      borderRight: `1px solid ${globalStyles.currentTheme.border}`,\n      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)'\n    },\n    sidebarItem: {\n      ...globalStyles.sidebarItemEdu,\n      color: globalStyles.currentTheme.text,\n      background: globalStyles.currentTheme.surface,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: globalStyles.currentTheme.primary,\n        color: 'white'\n      }\n    },\n    sidebarItemActive: {\n      ...globalStyles.sidebarItemActiveEdu,\n      color: 'white',\n      background: globalStyles.currentTheme.primary,\n      border: `1px solid ${globalStyles.currentTheme.primary}`\n    },\n    mainContent: {\n      ...globalStyles.mainContentEdu,\n      marginLeft: sidebarOpen ? '280px' : '0',\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n      minHeight: '100vh'\n    },\n    card: {\n      ...globalStyles.cardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n      color: globalStyles.currentTheme.text,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n    },\n    buttonPrimary: {\n      ...globalStyles.buttonPrimary\n    },\n    inputField: {\n      ...globalStyles.inputField,\n      backgroundColor: '#fff',\n      color: '#333',\n      border: '1px solid #ddd',\n      '&:focus': {\n        borderColor: globalStyles.currentTheme.primary,\n        outline: 'none',\n        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`\n      }\n    },\n    chatBubbleUser: {\n      ...globalStyles.chatBubbleUser,\n      backgroundColor: globalStyles.currentTheme.primary,\n      color: 'white'\n    },\n    chatBubbleBot: {\n      ...globalStyles.chatBubbleBot,\n      backgroundColor: globalStyles.currentTheme.secondary,\n      color: globalStyles.currentTheme.text,\n      border: '1px solid transparent'\n    },\n    companyCard: {\n      ...globalStyles.companyCardEdu\n    },\n    quizCard: {\n      ...globalStyles.quizCardEdu,\n      backgroundColor: globalStyles.currentTheme.surface\n    },\n    notification: {\n      ...globalStyles.notification\n    }\n  };\n\n  // Helper function to apply styles with hover states\n  const getStyle = (styleName, hover = false) => {\n    const baseStyle = styles[styleName];\n    if (typeof baseStyle === 'function') return baseStyle();\n    if (hover && baseStyle['&:hover']) {\n      return {\n        ...baseStyle,\n        ...baseStyle['&:hover']\n      };\n    }\n    return baseStyle;\n  };\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, user => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\").then(res => res.text()).then(data => setKnowledge(data)).catch(err => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    const {\n      data: listener\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser((session === null || session === void 0 ? void 0 : session.user) || null);\n    });\n    return () => {\n      listener === null || listener === void 0 ? void 0 : listener.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resumes').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      const {\n        data: urlData\n      } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async e => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const {\n      error\n    } = await supabase.storage.from('resources').upload(filePath, file, {\n      upsert: true\n    });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Enhanced company click handler\n  const handleCompanyClick = company => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n    logActivity(`Viewed ${company} DSA questions`);\n    if (company.toLowerCase() === 'microsoft') {\n      window.location.href = '/company-dsa/Microsoft_questions.html';\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.location.href = `/company-dsa/${formattedCompany}.html`;\n  };\n\n  // Toggle favorite company\n  const toggleFavorite = (company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  };\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Get filtered companies based on category and search\n  const getFilteredCompanies = () => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company => categoryCompanies.some(catCompany => company.toLowerCase().includes(catCompany.toLowerCase()) || catCompany.toLowerCase().includes(company.toLowerCase())));\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company => company.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n    return filtered;\n  };\n\n  // Open quiz link\n  const openQuizLink = url => {\n    window.open(url, \"_blank\");\n  };\n\n  // Send message to chatbot\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n    const userMessage = {\n      role: \"user\",\n      content: input\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInput(\"\");\n    setLoading(true);\n    try {\n      var _res$data$candidates, _res$data$candidates$, _res$data$candidates$2, _res$data$candidates$3, _res$data$candidates$4;\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${input}`;\n      const res = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }]\n      }, {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      const botReply = ((_res$data$candidates = res.data.candidates) === null || _res$data$candidates === void 0 ? void 0 : (_res$data$candidates$ = _res$data$candidates[0]) === null || _res$data$candidates$ === void 0 ? void 0 : (_res$data$candidates$2 = _res$data$candidates$.content) === null || _res$data$candidates$2 === void 0 ? void 0 : (_res$data$candidates$3 = _res$data$candidates$2.parts) === null || _res$data$candidates$3 === void 0 ? void 0 : (_res$data$candidates$4 = _res$data$candidates$3[0]) === null || _res$data$candidates$4 === void 0 ? void 0 : _res$data$candidates$4.text) || \"⚠ No response received.\";\n      const botMessage = {\n        role: \"bot\",\n        content: botReply\n      };\n      setMessages(prev => [...prev, botMessage]);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages(prev => [...prev, {\n        role: \"bot\",\n        content: \"❌ Error: \" + error.message\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Authentication functionality can be added later if needed\n\n  // Handle logout\n  const handleLogout = async () => {\n    await supabase.auth.signOut();\n  };\n\n  // Show notification\n  const showNotification = (msg, type = 'info') => {\n    setNotification({\n      msg,\n      type\n    });\n    setTimeout(() => setNotification(null), 3000);\n  };\n\n  // Log activity\n  const logActivity = msg => {\n    setActivityLog(log => [{\n      type: 'activity',\n      date: new Date().toISOString(),\n      msg\n    }, ...log.slice(0, 19)]);\n  };\n\n  // Toggle menu\n  const toggleMenu = menu => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menu]: !prev[menu]\n    }));\n  };\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [{\n      label: 'Resource Uploads',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#3182ce'\n    }, {\n      label: 'Coding Practice',\n      data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n      backgroundColor: '#805ad5'\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      },\n      tooltip: {\n        enabled: true\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          stepSize: 1\n        }\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: getStyle('appContainer'),\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      style: getStyle('navbar'),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          // Always white since navbar has gradient background\n          marginRight: '20px',\n          cursor: 'pointer',\n          padding: '8px',\n          borderRadius: '4px',\n          transition: 'all 0.2s ease'\n        },\n        onClick: () => setSidebarOpen(!sidebarOpen),\n        onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.1)',\n        onMouseLeave: e => e.target.style.background = 'none',\n        children: sidebarOpen ? /*#__PURE__*/_jsxDEV(FiX, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(FiMenu, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: require('./eduai-logo.jpg'),\n          alt: \"EduAI Logo\",\n          style: {\n            height: '36px',\n            marginRight: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-header-title\": true,\n            style: {\n              fontWeight: 600,\n              fontSize: '18px',\n              color: 'white'\n            },\n            children: \"EDU NOVA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-header-subtitle\": true,\n            style: {\n              fontSize: '12px',\n              opacity: 0.8,\n              color: 'white'\n            },\n            children: \"AI POWERED LEARNING SYSTEM\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '16px'\n        },\n        children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: 'rgba(255, 255, 255, 0.2)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontWeight: 600,\n              cursor: 'pointer',\n              color: 'white',\n              backdropFilter: 'blur(10px)'\n            },\n            children: user.email === ADMIN_EMAIL ? /*#__PURE__*/_jsxDEV(FiShield, {\n              size: 20,\n              color: \"#4caf50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 47\n            }, this) : user.email[0].toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...getStyle('buttonPrimary'),\n              background: 'rgba(255, 255, 255, 0.2)',\n              color: 'white',\n              border: '1px solid rgba(255, 255, 255, 0.3)',\n              backdropFilter: 'blur(10px)'\n            },\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(FiLogOut, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this), \" Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...getStyle('buttonPrimary'),\n            background: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            backdropFilter: 'blur(10px)'\n          },\n          onClick: () => {\n            console.log('Login functionality to be implemented');\n          },\n          children: [/*#__PURE__*/_jsxDEV(FiLogIn, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this), \" Login\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n      style: getStyle('sidebar'),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px'\n        },\n        children: updatedSidebarItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...getStyle('sidebarItem'),\n              ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),\n              cursor: 'pointer',\n              transition: 'all 0.3s ease'\n            },\n            onClick: () => {\n              setActiveTab(item.tab);\n              setSidebarOpen(false);\n            },\n            onMouseEnter: e => {\n              if (activeTab !== item.tab) {\n                e.target.style.background = globalStyles.currentTheme.primary;\n                e.target.style.color = 'white';\n                e.target.style.borderColor = globalStyles.currentTheme.primary;\n              }\n            },\n            onMouseLeave: e => {\n              if (activeTab !== item.tab) {\n                e.target.style.background = globalStyles.currentTheme.surface;\n                e.target.style.color = globalStyles.currentTheme.text;\n                e.target.style.borderColor = globalStyles.currentTheme.border;\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginRight: '12px'\n              },\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                flex: 1\n              },\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 17\n            }, this), item.subItems.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: e => {\n                e.stopPropagation();\n                toggleMenu(item.title);\n              },\n              children: expandedMenus[item.title] ? /*#__PURE__*/_jsxDEV(FiChevronDown, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 50\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronRight, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this), item.subItems.length > 0 && expandedMenus[item.title] && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: '32px'\n            },\n            children: item.subItems.map((subItem, subIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('sidebarItem'),\n                padding: '8px 16px 8px 32px',\n                fontSize: '14px',\n                opacity: 0.9\n              },\n              onClick: () => {\n                setActiveTab(item.tab);\n                setSidebarOpen(false);\n              },\n              onMouseEnter: e => {\n                e.target.style.background = globalStyles.currentTheme.primary;\n                e.target.style.color = 'white';\n                e.target.style.opacity = '1';\n              },\n              onMouseLeave: e => {\n                e.target.style.background = globalStyles.currentTheme.surface;\n                e.target.style.color = globalStyles.currentTheme.text;\n                e.target.style.opacity = '0.9';\n              },\n              children: subItem.title\n            }, subIndex, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 665,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      style: getStyle('mainContent'),\n      children: [sidebarOpen && window.innerWidth < 768 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: '64px',\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0,0,0,0.5)',\n          zIndex: 800\n        },\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 11\n      }, this), activeTab === \"dashboard\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '16px',\n              marginBottom: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '64px',\n                height: '64px',\n                borderRadius: '50%',\n                background: '#e3f2fd',\n                color: '#1976d2',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '24px',\n                fontWeight: 600\n              },\n              children: user ? user.email[0].toUpperCase() : /*#__PURE__*/_jsxDEV(FiUser, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 57\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  margin: 0,\n                  fontSize: '24px',\n                  fontWeight: 600,\n                  color: globalStyles.currentTheme.text\n                },\n                children: user ? `${user.email}'s Dashboard` : 'User Dashboard'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '4px 0 0',\n                  opacity: 0.8,\n                  color: globalStyles.currentTheme.textLight\n                },\n                children: \"Track your learning progress and activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: globalStyles.currentTheme.text\n              },\n              children: \"Weekly Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '300px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Bar, {\n                data: chartData,\n                options: chartOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: globalStyles.currentTheme.text\n              },\n              children: \"Recent Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxHeight: '200px',\n                overflowY: 'auto',\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: activityLog.slice(0, 5).map((log, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '8px 0',\n                  borderBottom: '1px solid #eee',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    backgroundColor: log.type === 'login' ? '#4caf50' : '#2196f3'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '14px',\n                      color: globalStyles.currentTheme.text\n                    },\n                    children: log.msg\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      opacity: 0.7,\n                      color: globalStyles.currentTheme.textLight\n                    },\n                    children: new Date(log.date).toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333'\n              },\n              children: \"Resume Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '16px',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  ...getStyle('buttonPrimary'),\n                  background: '#f5f5f5',\n                  color: '#333',\n                  border: '1px solid #ddd',\n                  cursor: resumeUploadLoading ? 'not-allowed' : 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiUpload, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 21\n                }, this), resumeUploadLoading ? 'Uploading...' : 'Upload Resume', /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"application/pdf\",\n                  onChange: handleResumeUpload,\n                  disabled: resumeUploadLoading,\n                  style: {\n                    display: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 19\n              }, this), resumeUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n                href: resumeUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                style: {\n                  ...getStyle('buttonPrimary'),\n                  background: 'none',\n                  border: '1px solid #4caf50',\n                  color: '#4caf50',\n                  textDecoration: 'none'\n                },\n                children: \"View Resume\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 11\n      }, this), activeTab === \"resume\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px',\n          maxWidth: '1200px',\n          margin: '0 auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: '#333'\n            },\n            children: \"Career Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: '#666'\n            },\n            children: \"Get personalized resume advice and career guidance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '50vh',\n              overflowY: 'auto',\n              marginBottom: '24px',\n              padding: '16px',\n              backgroundColor: '#f5f5f5',\n              border: '1px solid #e0e0e0',\n              borderRadius: '8px'\n            },\n            children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                textAlign: 'center',\n                opacity: 0.7\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px'\n                },\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 942,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  color: '#333'\n                },\n                children: \"Start a conversation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#666'\n                },\n                children: \"Ask about resumes, interviews, or career advice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 19\n            }, this) : messages.map((msg, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                animation: 'fadeIn 0.3s ease'\n              },\n              children: msg.role === 'bot' ? /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n                children: msg.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 25\n              }, this) : msg.content\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 21\n            }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('chatBubbleBot'),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.2s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '10px',\n                    height: '10px',\n                    borderRadius: '50%',\n                    backgroundColor: '#1976d2',\n                    animation: 'pulse 1.4s infinite ease-in-out',\n                    animationDelay: '0.4s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 986,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: chatEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            style: {\n              display: 'flex',\n              gap: '12px'\n            },\n            onSubmit: e => {\n              e.preventDefault();\n              sendMessage();\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Type your message...\",\n              style: getStyle('inputField'),\n              value: input,\n              onChange: e => setInput(e.target.value),\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              style: {\n                ...getStyle('buttonPrimary'),\n                minWidth: '100px'\n              },\n              disabled: loading || !input.trim(),\n              children: loading ? 'Sending...' : 'Send'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 11\n      }, this), activeTab === \"dsa\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  marginTop: 0,\n                  marginBottom: '8px'\n                },\n                children: \"\\uD83D\\uDE80 Company Wise DSA Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  opacity: 0.8,\n                  margin: 0\n                },\n                children: \"Explore DSA questions from top companies with enhanced filtering and favorites\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 17\n            }, this), showRevertButton && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: revertHeaderChanges,\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: '#ff6b6b',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '14px',\n                padding: '8px 16px',\n                border: 'none',\n                borderRadius: '8px',\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => e.target.style.background = '#ff5252',\n              onMouseLeave: e => e.target.style.background = '#ff6b6b',\n              children: [/*#__PURE__*/_jsxDEV(FiRefreshCw, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1063,\n                columnNumber: 21\n              }, this), \"Revert Header Color\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '8px',\n              marginBottom: '20px',\n              flexWrap: 'wrap',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '16px'\n            },\n            children: ['all', ...Object.keys(companyCategories)].map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedCategory(category),\n              style: {\n                padding: '8px 16px',\n                borderRadius: '20px',\n                border: selectedCategory === category ? 'none' : '1px solid #ddd',\n                background: selectedCategory === category ? globalStyles.currentTheme.primary : 'transparent',\n                color: selectedCategory === category ? 'white' : '#666',\n                cursor: 'pointer',\n                fontSize: '14px',\n                fontWeight: selectedCategory === category ? 600 : 400,\n                transition: 'all 0.3s ease',\n                textTransform: 'capitalize'\n              },\n              onMouseEnter: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = '#f5f5f5';\n                }\n              },\n              onMouseLeave: e => {\n                if (selectedCategory !== category) {\n                  e.target.style.background = 'transparent';\n                }\n              },\n              children: category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1070,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px',\n              flexWrap: 'wrap',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative',\n                flex: 1,\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  left: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#666'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1133,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search companies...\",\n                style: {\n                  ...getStyle('inputField'),\n                  paddingLeft: '48px',\n                  width: '100%'\n                },\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1135,\n                columnNumber: 19\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  position: 'absolute',\n                  right: '16px',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  background: 'none',\n                  border: 'none',\n                  color: '#666',\n                  cursor: 'pointer'\n                },\n                onClick: () => setSearchTerm(\"\"),\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1160,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              style: {\n                ...getStyle('inputField'),\n                width: 'auto',\n                minWidth: '150px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"name\",\n                children: \"\\uD83D\\uDCDD Sort by Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1175,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"favorites\",\n                children: \"\\u2B50 Favorites First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1166,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 15\n          }, this), recentCompanies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px',\n              padding: '16px',\n              borderRadius: '12px',\n              background: '#f8f9fa',\n              border: '1px solid #e9ecef'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                fontSize: '16px',\n                marginBottom: '12px',\n                color: darkMode ? '#e0e0e0' : '#333',\n                margin: '0 0 12px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                color: darkMode ? '#e0e0e0' : '#666'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 21\n              }, this), \" Recently Viewed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1189,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '8px',\n                flexWrap: 'wrap'\n              },\n              children: recentCompanies.map(company => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleCompanyClick(company),\n                style: {\n                  padding: '6px 12px',\n                  borderRadius: '16px',\n                  border: `1px solid ${globalStyles.currentTheme.primary}`,\n                  background: darkMode ? '#1a1a1a' : 'transparent',\n                  color: globalStyles.currentTheme.primary,\n                  cursor: 'pointer',\n                  fontSize: '12px',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.target.style.background = globalStyles.currentTheme.primary;\n                  e.target.style.color = 'white';\n                },\n                onMouseLeave: e => {\n                  e.target.style.background = darkMode ? '#1a1a1a' : 'transparent';\n                  e.target.style.color = globalStyles.currentTheme.primary;\n                },\n                children: company\n              }, company, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1206,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1200,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1182,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n              gap: '16px',\n              marginTop: '24px'\n            },\n            children: getFilteredCompanies().map((company, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...getStyle('companyCard'),\n                position: 'relative',\n                transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                border: favoriteCompanies.includes(company) ? `2px solid ${globalStyles.currentTheme.primary}` : `1px solid ${darkMode ? '#444' : globalStyles.currentTheme.border}`,\n                background: darkMode ? '#2a2a2a' : globalStyles.currentTheme.surface,\n                color: darkMode ? '#e0e0e0' : globalStyles.currentTheme.text,\n                animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                boxShadow: darkMode ? '0 4px 6px rgba(0, 0, 0, 0.3)' : `0 4px 6px ${globalStyles.currentTheme.shadow}`\n              },\n              onClick: () => handleCompanyClick(company),\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => toggleFavorite(company, e),\n                style: {\n                  position: 'absolute',\n                  top: '8px',\n                  right: '8px',\n                  background: 'none',\n                  border: 'none',\n                  cursor: 'pointer',\n                  color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                  transition: 'all 0.3s ease',\n                  fontSize: '18px'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                  fill: favoriteCompanies.includes(company) ? 'currentColor' : 'none'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1276,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1262,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '56px',\n                  height: '56px',\n                  borderRadius: '50%',\n                  background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                  color: 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '24px',\n                  fontWeight: 700,\n                  marginBottom: '12px',\n                  boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                },\n                children: company.charAt(0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1280,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 600,\n                  textAlign: 'center',\n                  fontSize: '14px',\n                  marginBottom: '8px'\n                },\n                children: company\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1298,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  fontSize: '12px',\n                  opacity: 0.7,\n                  marginTop: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDCCA \", Math.floor(Math.random() * 50) + 10, \" Questions\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1315,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u2B50 \", (Math.random() * 2 + 3).toFixed(1)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1316,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1308,\n                columnNumber: 21\n              }, this), Object.entries(companyCategories).map(([category, companies]) => {\n                if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      top: '8px',\n                      left: '8px',\n                      background: globalStyles.currentTheme.primary,\n                      color: 'white',\n                      padding: '2px 6px',\n                      borderRadius: '8px',\n                      fontSize: '10px',\n                      fontWeight: 600\n                    },\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1323,\n                    columnNumber: 27\n                  }, this);\n                }\n                return null;\n              })]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1236,\n            columnNumber: 15\n          }, this), getFilteredCompanies().length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px',\n              opacity: 0.7,\n              color: darkMode ? '#e0e0e0' : '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px'\n              },\n              children: \"\\uD83D\\uDD0D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1355,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: darkMode ? '#e0e0e0' : '#333'\n              },\n              children: \"No companies found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1356,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: darkMode ? '#ccc' : '#666'\n              },\n              children: \"Try adjusting your search or category filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1357,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1349,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1034,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1033,\n        columnNumber: 11\n      }, this), activeTab === \"quizzes\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0\n            },\n            children: \"Career Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1368,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px'\n            },\n            children: \"Test your knowledge with our career-focused quizzes!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1369,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n              gap: '16px'\n            },\n            children: quizButtons.map((quiz, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: getStyle('quizCard'),\n              onClick: () => openQuizLink(quiz.link),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 8px 0'\n                  },\n                  children: quiz.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1385,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '14px',\n                    opacity: 0.8\n                  },\n                  children: quiz.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1386,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1384,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: darkMode ? '#90caf9' : '#1976d2'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1395,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1394,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1379,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1373,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1367,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1366,\n        columnNumber: 11\n      }, this), activeTab === \"coding\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Coding, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1407,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1406,\n        columnNumber: 11\n      }, this), activeTab === \"resources\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: darkMode ? '#e0e0e0' : '#333'\n            },\n            children: \"Resources\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '24px',\n              color: darkMode ? '#ccc' : '#666'\n            },\n            children: \"Upload and manage your study materials and notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1417,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '24px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: darkMode ? '#3a3a3a' : '#f5f5f5',\n                color: darkMode ? '#e0e0e0' : '#333',\n                border: `1px solid ${darkMode ? '#555' : '#ddd'}`,\n                cursor: resourceUploadLoading ? 'not-allowed' : 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FiUpload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1433,\n                columnNumber: 19\n              }, this), resourceUploadLoading ? 'Uploading...' : 'Upload Resource', /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".pdf,.doc,.docx,.txt\",\n                onChange: handleResourceUpload,\n                disabled: resourceUploadLoading,\n                style: {\n                  display: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1435,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1426,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1425,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: darkMode ? '#e0e0e0' : '#333'\n              },\n              children: \"Your Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1446,\n              columnNumber: 17\n            }, this), userResources.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                opacity: 0.7,\n                color: darkMode ? '#ccc' : '#666'\n              },\n              children: \"No resources uploaded yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1451,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: darkMode ? '#2a2a2a' : '#f5f5f5',\n                border: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: userResources.map((file, idx) => {\n                const {\n                  data: urlData\n                } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '12px',\n                    borderBottom: darkMode ? '1px solid #444' : '1px solid #eee',\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: darkMode ? '#e0e0e0' : '#333'\n                    },\n                    children: file.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1472,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: urlData.publicUrl,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    style: {\n                      color: darkMode ? '#90caf9' : '#1976d2',\n                      textDecoration: 'none',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '4px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FiExternalLink, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1487,\n                      columnNumber: 29\n                    }, this), \"Open\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1475,\n                    columnNumber: 27\n                  }, this)]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1465,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1456,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1445,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1412,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1411,\n        columnNumber: 11\n      }, this), activeTab === \"academics\" && /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1499,\n        columnNumber: 39\n      }, this), activeTab === \"faq\" && /*#__PURE__*/_jsxDEV(Faq, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1500,\n        columnNumber: 33\n      }, this), activeTab === \"admin\" && (user === null || user === void 0 ? void 0 : user.email) === ADMIN_EMAIL && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getStyle('card'),\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: 0,\n              color: darkMode ? '#e0e0e0' : '#333'\n            },\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1504,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '16px',\n              marginBottom: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'users' ? globalStyles.currentTheme.primary : darkMode ? '#2a2a2a' : 'transparent',\n                color: adminTab === 'users' ? 'white' : darkMode ? '#e0e0e0' : '#333',\n                border: `1px solid ${darkMode ? '#444' : '#ddd'}`\n              },\n              onClick: () => setAdminTab('users'),\n              children: \"Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1513,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...getStyle('buttonPrimary'),\n                background: adminTab === 'resources' ? globalStyles.currentTheme.primary : darkMode ? '#2a2a2a' : 'transparent',\n                color: adminTab === 'resources' ? 'white' : darkMode ? '#e0e0e0' : '#333',\n                border: `1px solid ${darkMode ? '#444' : '#ddd'}`\n              },\n              onClick: () => setAdminTab('resources'),\n              children: \"Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1526,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1508,\n            columnNumber: 15\n          }, this), adminTab === 'users' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: darkMode ? '#e0e0e0' : '#333'\n              },\n              children: \"All Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1543,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: darkMode ? '#2a2a2a' : '#f5f5f5',\n                border: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,\n                borderRadius: '8px',\n                padding: '16px'\n              },\n              children: allUsers.map((user, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '12px',\n                  borderBottom: darkMode ? '1px solid #444' : '1px solid #eee',\n                  color: darkMode ? '#e0e0e0' : '#333'\n                },\n                children: user.email\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1554,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1547,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1542,\n            columnNumber: 17\n          }, this), adminTab === 'resources' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: '16px',\n                color: darkMode ? '#e0e0e0' : '#333'\n              },\n              children: \"All Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1568,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                opacity: 0.7,\n                color: darkMode ? '#ccc' : '#666'\n              },\n              children: \"Resource management coming soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1572,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1567,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1503,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1502,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...getStyle('notification'),\n        backgroundColor: notification.type === 'error' ? '#f44336' : notification.type === 'success' ? '#4caf50' : '#2196f3',\n        color: 'white',\n        border: darkMode ? `1px solid ${notification.type === 'error' ? '#d32f2f' : notification.type === 'success' ? '#388e3c' : '#1976d2'}` : 'none'\n      },\n      children: notification.msg\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1585,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        @keyframes slideIn {\n          from { transform: translateX(100%); }\n          to { transform: translateX(0); }\n        }\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n        @keyframes bounce {\n          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }\n          40%, 43% { transform: translate3d(0,-8px,0); }\n          70% { transform: translate3d(0,-4px,0); }\n          90% { transform: translate3d(0,-2px,0); }\n        }\n        @keyframes glow {\n          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }\n          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }\n        }\n        @keyframes shimmer {\n          0% { background-position: -200px 0; }\n          100% { background-position: calc(200px + 100%) 0; }\n        }\n\n        /* Enhanced hover effects */\n        .company-card:hover {\n          animation: bounce 0.6s ease;\n        }\n\n        .favorite-btn:hover {\n          animation: pulse 0.5s ease;\n        }\n\n        /* Smooth transitions for all interactive elements */\n        button, input, select {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        button:hover {\n          transform: translateY(-1px);\n        }\n\n        * {\n          box-sizing: border-box;\n        }\n        body {\n          margin: 0;\n          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1597,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 573,\n    columnNumber: 5\n  }, this);\n};\n_s(EduAIChatBot, \"bF4POR5FqJzGLsk5Rrw+G5M/wf0=\");\n_c = EduAIChatBot;\nexport default EduAIChatBot;\nvar _c;\n$RefreshReg$(_c, \"EduAIChatBot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getDoc", "doc", "Faq", "<PERSON><PERSON>", "Coding", "auth", "db", "axios", "sidebarItems", "onAuthStateChanged", "globalStyles", "FiMenu", "FiX", "FiChevronDown", "FiChevronRight", "FiFileText", "FiCode", "FiHelpCircle", "FiAward", "FiBook", "FiUser", "FiShield", "FiSearch", "FiUpload", "FiLogIn", "FiLogOut", "FiBriefcase", "FiBarChart2", "FiLayers", "FiCheckCircle", "FiExternalLink", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "FiStar", "FiRefreshCw", "FiTrendingUp", "createClient", "Bar", "Chart", "BarElement", "CategoryScale", "LinearScale", "<PERSON><PERSON><PERSON>", "Legend", "ReactMarkdown", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "register", "updatedSidebarItems", "map", "item", "iconMap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "tab", "title", "toLowerCase", "EduAIChatBot", "_s", "input", "setInput", "messages", "setMessages", "userId", "setUserId", "loading", "setLoading", "knowledge", "setKnowledge", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "sidebarOpen", "setSidebarOpen", "expandedMenus", "setExpandedMenus", "user", "setUser", "resumeUploadLoading", "setResumeUploadLoading", "resumeUrl", "setResumeUrl", "resourceUploadLoading", "setResourceUploadLoading", "userResources", "ADMIN_EMAIL", "allUsers", "adminTab", "setAdminTab", "notification", "setNotification", "activityLog", "setActivityLog", "chatEndRef", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "sortBy", "setSortBy", "favoriteCompanies", "setFavoriteCompanies", "recentCompanies", "setRecentCompanies", "showRevertButton", "setShowRevertButton", "API_KEY", "SUPABASE_URL", "SUPABASE_ANON_KEY", "supabase", "companyCategories", "companies", "quizButtons", "description", "link", "styles", "appContainer", "backgroundColor", "currentTheme", "background", "color", "text", "navbar", "navbarFixed", "borderBottom", "border", "sidebar", "sidebarFixed", "surface", "borderRight", "transform", "sidebarItem", "sidebarItemEdu", "transition", "primary", "sidebarItemActive", "sidebarItemActiveEdu", "mainContent", "mainContentEdu", "marginLeft", "minHeight", "card", "cardEdu", "boxShadow", "shadow", "buttonPrimary", "inputField", "borderColor", "outline", "chatBubbleUser", "chatBubbleBot", "secondary", "companyCard", "companyCardEdu", "quizCard", "quizCardEdu", "getStyle", "styleName", "hover", "baseStyle", "unsubscribe", "uid", "console", "log", "fetchUserProfile", "userRef", "userDoc", "exists", "userData", "data", "dp", "fetch", "then", "res", "catch", "err", "error", "getSession", "session", "listener", "onAuthStateChange", "_event", "subscription", "handleResumeUpload", "e", "file", "target", "files", "filePath", "id", "name", "storage", "from", "upload", "upsert", "urlData", "getPublicUrl", "publicUrl", "showNotification", "logActivity", "handleResourceUpload", "handleCompanyClick", "company", "prev", "filtered", "filter", "c", "slice", "window", "location", "href", "formattedCompany", "replace", "toggleFavorite", "stopPropagation", "includes", "revertHeaderChanges", "eduNovaElement", "document", "querySelector", "subtitleElement", "style", "getFilteredCompanies", "categoryCompanies", "some", "catCompany", "sort", "a", "b", "aFav", "bFav", "localeCompare", "openQuizLink", "url", "open", "sendMessage", "trim", "userMessage", "role", "content", "_res$data$candidates", "_res$data$candidates$", "_res$data$candidates$2", "_res$data$candidates$3", "_res$data$candidates$4", "prompt", "post", "contents", "parts", "headers", "botReply", "candidates", "botMessage", "message", "handleLogout", "signOut", "msg", "type", "setTimeout", "date", "Date", "toISOString", "toggleMenu", "menu", "current", "scrollIntoView", "behavior", "getLast7Days", "days", "i", "d", "setDate", "getDate", "push", "toLocaleDateString", "chartLabels", "chartData", "labels", "datasets", "label", "day", "startsWith", "length", "chartOptions", "responsive", "plugins", "legend", "position", "tooltip", "enabled", "scales", "y", "beginAtZero", "ticks", "stepSize", "children", "marginRight", "cursor", "padding", "borderRadius", "onClick", "onMouseEnter", "onMouseLeave", "size", "flex", "display", "alignItems", "src", "require", "alt", "height", "fontWeight", "fontSize", "opacity", "gap", "width", "justifyContent", "<PERSON><PERSON>ilter", "email", "toUpperCase", "index", "subItems", "subItem", "subIndex", "innerWidth", "top", "left", "right", "bottom", "zIndex", "marginBottom", "margin", "textLight", "options", "maxHeight", "overflowY", "toLocaleString", "flexWrap", "accept", "onChange", "disabled", "rel", "textDecoration", "max<PERSON><PERSON><PERSON>", "marginTop", "flexDirection", "textAlign", "idx", "animation", "animationDelay", "ref", "onSubmit", "preventDefault", "placeholder", "value", "min<PERSON><PERSON><PERSON>", "paddingBottom", "Object", "keys", "category", "textTransform", "paddingLeft", "darkMode", "gridTemplateColumns", "fill", "primaryDark", "char<PERSON>t", "Math", "floor", "random", "toFixed", "entries", "quiz", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/EduAIChatBot.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport { getDoc, doc } from 'firebase/firestore';\nimport Faq from './Faq';\nimport Exams from \"./Exams\";\nimport Coding from \"./Coding\";\nimport { auth, db } from './firebaseConfig';\nimport axios from \"axios\";\nimport { sidebarItems } from './sidebarItems';\nimport { onAuthStateChanged } from 'firebase/auth';\nimport globalStyles from './styles.js';\nimport {\n  FiMenu, FiX, FiChevronDown, FiChevronRight, FiFileText,\n  FiCode, FiHelpCircle, FiAward, FiBook, FiUser, FiShield,\n  FiSearch, FiUpload, FiLogIn, FiLogOut,\n  FiBriefcase, FiBarChart2, FiLayers, FiCheckCircle, FiExternalLink,\n  FiHeart, FiClock, FiFilter, FiStar, FiRefreshCw, FiTrendingUp\n} from \"react-icons/fi\";\nimport { createClient } from '@supabase/supabase-js';\nimport { Bar } from 'react-chartjs-2';\nimport { Chart, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';\nimport ReactMarkdown from 'react-markdown';\n\nChart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);\n\n// Enhanced sidebar items with icons\nconst updatedSidebarItems = sidebarItems.map(item => {\n  const iconMap = {\n    \"resume\": <FiFileText />,\n    \"dsa\": <FiCode />,\n    \"coding\": <FiLayers />,\n    \"resources\": <FiBriefcase />,\n    \"quizzes\": <FiCheckCircle />,\n    \"aptitude\": <FiBarChart2 />,\n    \"academics\": <FiBook />,\n    \"faq\": <FiHelpCircle />,\n    \"admin\": <FiShield />\n  };\n\n  return {\n    ...item,\n    icon: iconMap[item.tab] || iconMap[item.title.toLowerCase()] || <FiAward />\n  };\n});\n\nconst EduAIChatBot = () => {\n  // State declarations\n  const [input, setInput] = useState(\"\");\n  const [messages, setMessages] = useState([]);\n  const [userId, setUserId] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [knowledge, setKnowledge] = useState(\"\");\n  const [activeTab, setActiveTab] = useState(\"dashboard\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [expandedMenus, setExpandedMenus] = useState({});\n  const [user, setUser] = useState(null);\n\n  const [resumeUploadLoading, setResumeUploadLoading] = useState(false);\n  const [resumeUrl, setResumeUrl] = useState(null);\n  const [resourceUploadLoading, setResourceUploadLoading] = useState(false);\n  const [userResources] = useState([]);\n  const ADMIN_EMAIL = '<EMAIL>';\n  const [allUsers] = useState([]);\n  const [adminTab, setAdminTab] = useState('users');\n  const [notification, setNotification] = useState(null);\n  const [activityLog, setActivityLog] = useState([]);\n  const chatEndRef = useRef(null);\n\n  // Enhanced DSA section states\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [favoriteCompanies, setFavoriteCompanies] = useState([]);\n  const [recentCompanies, setRecentCompanies] = useState([]);\n  const [showRevertButton, setShowRevertButton] = useState(true);\n\n  // API configurations\n  const API_KEY = \"AIzaSyC6kHWto78QdqHz7Uu9RzEXb443ZO7tG5M\";\n  const SUPABASE_URL = 'https://gziaptswfepiveyylven.supabase.co';\n  const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd6aWFwdHN3ZmVwaXZleXlsdmVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzczNTYsImV4cCI6MjA2MTI1MzM1Nn0.wmqXZGffrox8E_PuCwbzh4xJEffsvFmZCVcF6WFAX6Q';\n  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\n  // Company categories for enhanced DSA section\n  const companyCategories = {\n    'FAANG': ['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google'],\n    'Big Tech': ['Microsoft', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Nvidia'],\n    'Startups': ['Stripe', 'Airbnb', 'Uber', 'Lyft', 'DoorDash', 'Instacart', 'Coinbase'],\n    'Finance': ['Goldman Sachs', 'JPMorgan', 'Morgan Stanley', 'BlackRock', 'Citadel', 'Two Sigma'],\n    'Consulting': ['McKinsey', 'BCG', 'Bain', 'Deloitte', 'Accenture', 'PwC'],\n    'E-commerce': ['Amazon', 'eBay', 'Shopify', 'Etsy', 'Wayfair', 'Booking.com'],\n    'Gaming': ['Blizzard', 'Epic Games', 'Riot Games', 'Unity', 'Roblox'],\n    'Indian': ['TCS', 'Infosys', 'Wipro', 'HCL', 'Flipkart', 'Paytm', 'Zomato', 'Swiggy']\n  };\n\n  // Complete list of companies\n  const companies = [\n     \"Accenture\", \"Accolite\", \"Adobe\", \"Affirm\", \"Agoda\", \"Airbnb\", \"Airtel\",\n    \"Akamar\", \"Akuna Capital\", \"Alibaba\", \"Altimetrik\", \"Amazon\", \"AMD\",\n    \"Amdocs\", \"American Express\", \"Anduril\", \"Apple\", \"Arista Networks\",\n    \"Arcesium\", \"Atlassian\", \"Attentive\", \"athenahealth\", \"Autodesk\",\n    \"Avito\", \"Baidu\", \"Barclays\", \"BitGo\", \"BlackRock\", \"Blizzard\",\n    \"Block\", \"Bloomberg\", \"BNY Mellon\", \"Boft\", \"Booking.com\", \"Bos\",\n    \"BP\", \"ByteDance\", \"Cadence\", \"Capgemini\", \"Capital One\", \"CARS24\",\n    \"carwale\", \"Cashfree\", \"Chewy\", \"Cisco\", \"Citadel\", \"Citrix\",\n    \"Cloudera\", \"Cloudflare\", \"Cognizant\", \"Coinbase\", \"Commvault\",\n    \"Confluent\", \"Coupang\", \"Coursera\", \"CrowdStrike\", \"Cruise\",\n    \"Curefit\", \"Databricks\", \"Datadog\", \"DE Shaw\", \"Deloitte\", \"Dell\",\n    \"Deliveroo\", \"Derantior\", \"Deutsche Bank\", \"Devflev\", \"Directi\",\n    \"Disney\", \"Docusign\", \"DoorDash\", \"Dream11\", \"Dropbox\", \"DRW\",\n    \"Dunzo\", \"eBay\", \"EPAM Systems\", \"Epic Systems\", \"Expedia\",\n    \"FactSet\", \"Flexport\", \"Flipkart\", \"Freshworks\", \"GE Healthcare\",\n    \"Geico\", \"Goldman Sachs\", \"Google\", \"Grab\", \"Grammarly\", \"Graviton\",\n    \"Groww\", \"GSN Games\", \"Hashedin\", \"HCL\", \"HPE\", \"Hubspot\",\n    \"Hudson River Trading\", \"Huawei\", \"IBM\", \"IMC\", \"Indeed\", \"Infosys\",\n    \"InMobi\", \"Intel\", \"Intuit\", \"JPMorgan\", \"Jane Street\",\n    \"Josh Technology\", \"Jump Trading\", \"Juspay\", \"Karat\", \"KLA\",\n    \"LinkedIn\", \"LiveRamp\", \"Lowe's\", \"Lucid\", \"Lyft\", \"MakeMyTrip\",\n    \"Mastercard\", \"MathWorks\", \"Media.net\", \"Meesho\", \"Mercari\", \"Meta\",\n    \"Microsoft\", \"Millennium\", \"Mitsogo\", \"Moloco\", \"MongoDB\",\n    \"Morgan Stanley\", \"Moveworks\", \"Myntra\", \"Nagarro\", \"NetApp\",\n    \"Netflix\", \"Nextdoor\", \"Nielsen\", \"Nike\", \"Niantic\", \"Nordstrom\",\n    \"Nutanix\", \"Nvidia\", \"Okta\", \"OKX\", \"OpenAI\", \"OpenText\", \"Oracle\",\n    \"Otter.ai\", \"Oyo\", \"Ozon\", \"Palantir Technologies\", \"Palo Alto Networks\",\n    \"PayPal\", \"Paytm\", \"Persistent Systems\", \"PhonePe\", \"Pinterest\",\n    \"Pocket Gems\", \"Point72\", \"Pure Storage\", \"Qualcomm\", \"Quora\",\n    \"Rakuten\", \"Razorpay\", \"RBC\", \"Reddit\", \"Revolut\", \"Robinhood\",\n    \"Roblox\", \"Rubrik\", \"Salesforce\", \"Samsung\", \"SAP\", \"ServiceNow\",\n    \"Shopify\", \"Siemens\", \"Sigmoid\", \"SIG\", \"Snowflake\", \"Snap\", \"Sofi\",\n    \"Splunk\", \"Spotify\", \"Sprinklr\", \"Squarepoint Capital\", \"Stripe\",\n    \"Swiggy\", \"TCS\", \"Tekion\", \"Tencent\", \"Tesla\", \"ThoughtSpot\",\n    \"ThoughtWorks\", \"TikTok\", \"Tinkoff\", \"Trilogy\", \"Turing\", \"Turo\",\n    \"Twilio\", \"Twitch\", \"Two Sigma\", \"Uber\", \"UiPath\", \"UKG\",\n    \"Veeva Systems\", \"Verily\", \"Verkada\", \"Virtu Financial\", \"Visa\",\n    \"VK\", \"VMware\", \"Walmart Labs\", \"WarnerMedia\", \"Wayfair\",\n    \"Wells Fargo\", \"Wipro\", \"Wix\", \"Workday\", \"X\", \"Yahoo\", \"Yandex\",\n    \"Yelp\", \"Zalando\", \"Zenefits\", \"Zepto\", \"Zeta\", \"Zillow\", \"Zoho\",\n    \"Zomato\", \"ZScaler\", \"Zopsmart\"\n  ];\n\n  // Quiz buttons data\n  const quizButtons = [\n    {\n      title: \"OP and CN Quiz\",\n      description: \"Test your knowledge of Operating System and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n    {\n      title: \"OOPs and DBMS Quiz\",\n      description: \"Challenge yourself with oops and dbms\",\n      link: \"https://oopsanddbms.netlify.app/\",\n    },\n    {\n      title: \"System Design Quiz\",\n      description: \"Test your system design knowledge\",\n      link: \"https://system-design041.netlify.app\",\n    },\n    {\n      title: \"Quantitative Aptitude and Reasoning Quiz\",\n      description: \"Practice common quant and reasoning questions\",\n      link: \"https://quantandreasoning.netlify.app\",\n    },\n    {\n      title: \"Cloud & DevOps Quiz\",\n      description: \"Test your knowledge of Cloud and DevOps concepts\",\n      link: \"https://cloud-devops.netlify.app\",\n    },\n    {\n      title: \"DSA Quiz\",\n      description: \"Data Structures and Algorithms quiz\",\n      link: \"https://dsa041.netlify.app\",\n    },\n    {\n      title: \"Operating System & Computer Networks Quiz\",\n      description: \"Quiz on OS and Computer Networks\",\n      link: \"https://opcn.netlify.app\",\n    },\n     {\n      title: \"Web Development Quiz\",\n      description: \"Quiz on Web Development topics\",\n      link: \"https://web-dev041.netlify.app\",\n\n    },\n  ];\n\n  // Use centralized styles\n  const styles = {\n    ...globalStyles,\n    appContainer: {\n      ...globalStyles.appContainer,\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n    },\n    navbar: {\n      ...globalStyles.navbarFixed,\n      borderBottom: `1px solid ${globalStyles.currentTheme.border}`\n    },\n    sidebar: {\n      ...globalStyles.sidebarFixed,\n      backgroundColor: globalStyles.currentTheme.surface,\n      borderRight: `1px solid ${globalStyles.currentTheme.border}`,\n      transform: sidebarOpen ? 'translateX(0)' : 'translateX(-100%)',\n    },\n    sidebarItem: {\n      ...globalStyles.sidebarItemEdu,\n      color: globalStyles.currentTheme.text,\n      background: globalStyles.currentTheme.surface,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        background: globalStyles.currentTheme.primary,\n        color: 'white'\n      }\n    },\n    sidebarItemActive: {\n      ...globalStyles.sidebarItemActiveEdu,\n      color: 'white',\n      background: globalStyles.currentTheme.primary,\n      border: `1px solid ${globalStyles.currentTheme.primary}`,\n    },\n    mainContent: {\n      ...globalStyles.mainContentEdu,\n      marginLeft: sidebarOpen ? '280px' : '0',\n      backgroundColor: globalStyles.currentTheme.background,\n      color: globalStyles.currentTheme.text,\n      minHeight: '100vh'\n    },\n    card: {\n      ...globalStyles.cardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n      color: globalStyles.currentTheme.text,\n      border: `1px solid ${globalStyles.currentTheme.border}`,\n      boxShadow: `0 4px 6px ${globalStyles.currentTheme.shadow}`\n    },\n    buttonPrimary: {\n      ...globalStyles.buttonPrimary,\n    },\n    inputField: {\n      ...globalStyles.inputField,\n      backgroundColor: '#fff',\n      color: '#333',\n      border: '1px solid #ddd',\n      '&:focus': {\n        borderColor: globalStyles.currentTheme.primary,\n        outline: 'none',\n        boxShadow: `0 0 0 2px ${globalStyles.currentTheme.primary}20`\n      }\n    },\n    chatBubbleUser: {\n      ...globalStyles.chatBubbleUser,\n      backgroundColor: globalStyles.currentTheme.primary,\n      color: 'white'\n    },\n    chatBubbleBot: {\n      ...globalStyles.chatBubbleBot,\n      backgroundColor: globalStyles.currentTheme.secondary,\n      color: globalStyles.currentTheme.text,\n      border: '1px solid transparent'\n    },\n    companyCard: {\n      ...globalStyles.companyCardEdu,\n    },\n    quizCard: {\n      ...globalStyles.quizCardEdu,\n      backgroundColor: globalStyles.currentTheme.surface,\n    },\n    notification: {\n      ...globalStyles.notification,\n    }\n  };\n\n  // Helper function to apply styles with hover states\n  const getStyle = (styleName, hover = false) => {\n    const baseStyle = styles[styleName];\n    if (typeof baseStyle === 'function') return baseStyle();\n    if (hover && baseStyle['&:hover']) {\n      return { ...baseStyle, ...baseStyle['&:hover'] };\n    }\n    return baseStyle;\n  };\n\n  // Fetch user profile\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      if (user) {\n        setUserId(user.uid);\n      } else {\n        console.log('User is not authenticated');\n        setLoading(false);\n      }\n    });\n    return () => unsubscribe();\n  }, []);\n\n  useEffect(() => {\n    if (userId) {\n      const fetchUserProfile = async () => {\n        const userRef = doc(db, \"users\", userId);\n        const userDoc = await getDoc(userRef);\n\n        if (userDoc.exists()) {\n          const userData = userDoc.data();\n          // Profile pic functionality can be added later\n          console.log(\"User data loaded:\", userData.dp);\n        } else {\n          console.log(\"No such user!\");\n        }\n        setLoading(false);\n      };\n      fetchUserProfile();\n    }\n  }, [userId]);\n\n  // Fetch training data\n  useEffect(() => {\n    fetch(\"/training-data.txt\")\n      .then((res) => res.text())\n      .then((data) => setKnowledge(data))\n      .catch((err) => console.error(\"Failed to load training data:\", err));\n  }, []);\n\n  // Supabase auth state\n  useEffect(() => {\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user || null);\n    });\n    const { data: listener } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user || null);\n    });\n    return () => {\n      listener?.subscription.unsubscribe();\n    };\n  }, [supabase.auth]);\n\n  // Handle resume upload\n  const handleResumeUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResumeUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resumes').upload(filePath, file, { upsert: true });\n    if (!error) {\n      const { data: urlData } = supabase.storage.from('resumes').getPublicUrl(filePath);\n      setResumeUrl(urlData.publicUrl);\n      showNotification('Resume uploaded successfully!', 'success');\n      logActivity('Uploaded a resume');\n    } else {\n      showNotification('Resume upload failed.', 'error');\n    }\n    setResumeUploadLoading(false);\n  };\n\n  // Handle resource upload\n  const handleResourceUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file || !user) return;\n    setResourceUploadLoading(true);\n    const filePath = `${user.id}/${file.name}`;\n    const { error } = await supabase.storage.from('resources').upload(filePath, file, { upsert: true });\n    if (!error) {\n      showNotification('Resource uploaded!', 'success');\n      logActivity(`Uploaded resource: ${file.name}`);\n    } else {\n      showNotification('Resource upload failed.', 'error');\n    }\n    setResourceUploadLoading(false);\n  };\n\n  // Enhanced company click handler\n  const handleCompanyClick = (company) => {\n    // Add to recent companies\n    setRecentCompanies(prev => {\n      const filtered = prev.filter(c => c !== company);\n      return [company, ...filtered].slice(0, 5); // Keep only 5 recent\n    });\n\n    logActivity(`Viewed ${company} DSA questions`);\n\n    if (company.toLowerCase() === 'microsoft') {\n      window.location.href = '/company-dsa/Microsoft_questions.html';\n      return;\n    }\n    const formattedCompany = company.replace(/\\s+/g, '');\n    window.location.href = `/company-dsa/${formattedCompany}.html`;\n  };\n\n  // Toggle favorite company\n  const toggleFavorite = (company, e) => {\n    e.stopPropagation(); // Prevent company click\n    setFavoriteCompanies(prev => {\n      if (prev.includes(company)) {\n        return prev.filter(c => c !== company);\n      } else {\n        return [...prev, company];\n      }\n    });\n  };\n\n  // Revert header color changes\n  const revertHeaderChanges = () => {\n    setShowRevertButton(false);\n    showNotification('Header text color reverted to theme default!', 'success');\n\n    // Actually revert the header text color by updating the DOM\n    const eduNovaElement = document.querySelector('[data-header-title]');\n    const subtitleElement = document.querySelector('[data-header-subtitle]');\n\n    if (eduNovaElement) {\n      eduNovaElement.style.color = '#333';\n    }\n    if (subtitleElement) {\n      subtitleElement.style.color = '#333';\n    }\n  };\n\n  // Get filtered companies based on category and search\n  const getFilteredCompanies = () => {\n    let filtered = companies;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      const categoryCompanies = companyCategories[selectedCategory] || [];\n      filtered = filtered.filter(company =>\n        categoryCompanies.some(catCompany =>\n          company.toLowerCase().includes(catCompany.toLowerCase()) ||\n          catCompany.toLowerCase().includes(company.toLowerCase())\n        )\n      );\n    }\n\n    // Filter by search term\n    filtered = filtered.filter(company =>\n      company.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n\n    // Sort companies\n    if (sortBy === 'name') {\n      filtered.sort();\n    } else if (sortBy === 'favorites') {\n      filtered.sort((a, b) => {\n        const aFav = favoriteCompanies.includes(a);\n        const bFav = favoriteCompanies.includes(b);\n        if (aFav && !bFav) return -1;\n        if (!aFav && bFav) return 1;\n        return a.localeCompare(b);\n      });\n    }\n\n    return filtered;\n  };\n\n  // Open quiz link\n  const openQuizLink = (url) => {\n    window.open(url, \"_blank\");\n  };\n\n  // Send message to chatbot\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n\n    const userMessage = { role: \"user\", content: input };\n    setMessages((prev) => [...prev, userMessage]);\n    setInput(\"\");\n    setLoading(true);\n\n    try {\n      const prompt = `You are a resume assistant. Help users improve their resumes, provide suggestions, and answer career-related questions. Use the following knowledge if it helps. If it's not relevant, use your own intelligence.\\n\\nKnowledge:\\n${knowledge}\\n\\nQuestion: ${input}`;\n\n      const res = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`,\n        {\n          contents: [\n            {\n              parts: [{ text: prompt }],\n            },\n          ],\n        },\n        {\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n        }\n      );\n\n      const botReply =\n        res.data.candidates?.[0]?.content?.parts?.[0]?.text ||\n        \"⚠ No response received.\";\n      const botMessage = { role: \"bot\", content: botReply };\n      setMessages((prev) => [...prev, botMessage]);\n    } catch (error) {\n      console.error(\"Gemini API Error:\", error);\n      setMessages((prev) => [\n        ...prev,\n        { role: \"bot\", content: \"❌ Error: \" + error.message },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Authentication functionality can be added later if needed\n\n  // Handle logout\n  const handleLogout = async () => {\n    await supabase.auth.signOut();\n  };\n\n  // Show notification\n  const showNotification = (msg, type = 'info') => {\n    setNotification({ msg, type });\n    setTimeout(() => setNotification(null), 3000);\n  };\n\n  // Log activity\n  const logActivity = (msg) => {\n    setActivityLog(log => [\n      { type: 'activity', date: new Date().toISOString(), msg },\n      ...log.slice(0, 19)\n    ]);\n  };\n\n  // Toggle menu\n  const toggleMenu = (menu) => {\n    setExpandedMenus(prev => ({\n      ...prev,\n      [menu]: !prev[menu]\n    }));\n  };\n\n  // Auto-scroll chat\n  useEffect(() => {\n    if (chatEndRef.current) chatEndRef.current.scrollIntoView({ behavior: 'smooth' });\n  }, [messages, loading]);\n\n  // Chart data\n  const getLast7Days = () => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const d = new Date();\n      d.setDate(d.getDate() - i);\n      days.push(d.toLocaleDateString());\n    }\n    return days;\n  };\n\n  const chartLabels = getLast7Days();\n  const chartData = {\n    labels: chartLabels,\n    datasets: [\n      {\n        label: 'Resource Uploads',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg.startsWith('Uploaded resource') && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#3182ce',\n      },\n      {\n        label: 'Coding Practice',\n        data: chartLabels.map(day => activityLog.filter(a => a.type === 'activity' && a.msg === 'Clicked coding practice link' && new Date(a.date).toLocaleDateString() === day).length),\n        backgroundColor: '#805ad5',\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: { position: 'top' },\n      tooltip: { enabled: true },\n    },\n    scales: {\n      y: { beginAtZero: true, ticks: { stepSize: 1 } },\n    },\n  };\n\n  return (\n    <div style={getStyle('appContainer')}>\n      {/* Top Navigation Bar */}\n      <nav style={getStyle('navbar')}>\n        <button\n          style={{\n            background: 'none',\n            border: 'none',\n            color: 'white', // Always white since navbar has gradient background\n            marginRight: '20px',\n            cursor: 'pointer',\n            padding: '8px',\n            borderRadius: '4px',\n            transition: 'all 0.2s ease'\n          }}\n          onClick={() => setSidebarOpen(!sidebarOpen)}\n          onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}\n          onMouseLeave={(e) => e.target.style.background = 'none'}\n        >\n          {sidebarOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n        </button>\n\n        <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>\n          <img\n            src={require('./eduai-logo.jpg')}\n            alt=\"EduAI Logo\"\n            style={{ height: '36px', marginRight: '12px' }}\n          />\n          <div>\n            <div\n              data-header-title\n              style={{ fontWeight: 600, fontSize: '18px', color: 'white' }}\n            >\n              EDU NOVA\n            </div>\n            <div\n              data-header-subtitle\n              style={{ fontSize: '12px', opacity: 0.8, color: 'white' }}\n            >\n              AI POWERED LEARNING SYSTEM\n            </div>\n          </div>\n        </div>\n\n        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n          {user ? (\n            <>\n              <div style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: '50%',\n                background: 'rgba(255, 255, 255, 0.2)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontWeight: 600,\n                cursor: 'pointer',\n                color: 'white',\n                backdropFilter: 'blur(10px)'\n              }}>\n                {user.email === ADMIN_EMAIL ? <FiShield size={20} color=\"#4caf50\" /> : user.email[0].toUpperCase()}\n              </div>\n              <button\n                style={{\n                  ...getStyle('buttonPrimary'),\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  border: '1px solid rgba(255, 255, 255, 0.3)',\n                  backdropFilter: 'blur(10px)'\n                }}\n                onClick={handleLogout}\n              >\n                <FiLogOut /> Logout\n              </button>\n            </>\n          ) : (\n            <button\n              style={{\n                ...getStyle('buttonPrimary'),\n                background: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                border: '1px solid rgba(255, 255, 255, 0.3)',\n                backdropFilter: 'blur(10px)'\n              }}\n              onClick={() => { console.log('Login functionality to be implemented'); }}\n            >\n              <FiLogIn /> Login\n            </button>\n          )}\n        </div>\n      </nav>\n\n      {/* Sidebar */}\n      <aside style={getStyle('sidebar')}>\n        <div style={{ padding: '16px' }}>\n          {updatedSidebarItems.map((item, index) => (\n            <div key={index}>\n              <div\n                style={{\n                  ...getStyle('sidebarItem'),\n                  ...(activeTab === item.tab ? getStyle('sidebarItemActive') : {}),\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease'\n                }}\n                onClick={() => {\n                  setActiveTab(item.tab);\n                  setSidebarOpen(false);\n                }}\n                onMouseEnter={(e) => {\n                  if (activeTab !== item.tab) {\n                    e.target.style.background = globalStyles.currentTheme.primary;\n                    e.target.style.color = 'white';\n                    e.target.style.borderColor = globalStyles.currentTheme.primary;\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (activeTab !== item.tab) {\n                    e.target.style.background = globalStyles.currentTheme.surface;\n                    e.target.style.color = globalStyles.currentTheme.text;\n                    e.target.style.borderColor = globalStyles.currentTheme.border;\n                  }\n                }}\n              >\n                <div style={{ marginRight: '12px' }}>{item.icon}</div>\n                <span style={{ flex: 1 }}>{item.title}</span>\n                {item.subItems.length > 0 && (\n                  <div onClick={(e) => {\n                    e.stopPropagation();\n                    toggleMenu(item.title);\n                  }}>\n                    {expandedMenus[item.title] ? <FiChevronDown /> : <FiChevronRight />}\n                  </div>\n                )}\n              </div>\n\n              {item.subItems.length > 0 && expandedMenus[item.title] && (\n                <div style={{ marginLeft: '32px' }}>\n                  {item.subItems.map((subItem, subIndex) => (\n                    <div\n                      key={subIndex}\n                      style={{\n                        ...getStyle('sidebarItem'),\n                        padding: '8px 16px 8px 32px',\n                        fontSize: '14px',\n                        opacity: 0.9\n                      }}\n                      onClick={() => {\n                        setActiveTab(item.tab);\n                        setSidebarOpen(false);\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.background = globalStyles.currentTheme.primary;\n                        e.target.style.color = 'white';\n                        e.target.style.opacity = '1';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.background = globalStyles.currentTheme.surface;\n                        e.target.style.color = globalStyles.currentTheme.text;\n                        e.target.style.opacity = '0.9';\n                      }}\n                    >\n                      {subItem.title}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </aside>\n\n      {/* Main Content */}\n      <main style={getStyle('mainContent')}>\n        {/* Overlay when sidebar is open on mobile */}\n        {sidebarOpen && window.innerWidth < 768 && (\n          <div\n            style={{\n              position: 'fixed',\n              top: '64px',\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0,0,0,0.5)',\n              zIndex: 800\n            }}\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* Dashboard Content */}\n        {activeTab === \"dashboard\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '24px' }}>\n                <div style={{\n                  width: '64px',\n                  height: '64px',\n                  borderRadius: '50%',\n                  background: '#e3f2fd',\n                  color: '#1976d2',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '24px',\n                  fontWeight: 600\n                }}>\n                  {user ? user.email[0].toUpperCase() : <FiUser size={32} />}\n                </div>\n                <div>\n                  <h2 style={{\n                    margin: 0,\n                    fontSize: '24px',\n                    fontWeight: 600,\n                    color: globalStyles.currentTheme.text\n                  }}>\n                    {user ? `${user.email}'s Dashboard` : 'User Dashboard'}\n                  </h2>\n                  <p style={{\n                    margin: '4px 0 0',\n                    opacity: 0.8,\n                    color: globalStyles.currentTheme.textLight\n                  }}>\n                    Track your learning progress and activities\n                  </p>\n                </div>\n              </div>\n\n              <div style={{ marginBottom: '24px' }}>\n                <h3 style={{\n                  marginBottom: '16px',\n                  color: globalStyles.currentTheme.text\n                }}>Weekly Activity</h3>\n                <div style={{ height: '300px' }}>\n                  <Bar data={chartData} options={chartOptions} />\n                </div>\n              </div>\n\n              <div style={{ marginBottom: '24px' }}>\n                <h3 style={{\n                  marginBottom: '16px',\n                  color: globalStyles.currentTheme.text\n                }}>Recent Activity</h3>\n                <div style={{\n                  maxHeight: '200px',\n                  overflowY: 'auto',\n                  backgroundColor: '#f5f5f5',\n                  border: '1px solid #e0e0e0',\n                  borderRadius: '8px',\n                  padding: '16px'\n                }}>\n                  {activityLog.slice(0, 5).map((log, index) => (\n                    <div key={index} style={{\n                      padding: '8px 0',\n                      borderBottom: '1px solid #eee',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px'\n                    }}>\n                      <div style={{\n                        width: '8px',\n                        height: '8px',\n                        borderRadius: '50%',\n                        backgroundColor: log.type === 'login' ? '#4caf50' : '#2196f3'\n                      }} />\n                      <div style={{ flex: 1 }}>\n                        <div style={{\n                          fontSize: '14px',\n                          color: globalStyles.currentTheme.text\n                        }}>{log.msg}</div>\n                        <div style={{\n                          fontSize: '12px',\n                          opacity: 0.7,\n                          color: globalStyles.currentTheme.textLight\n                        }}>\n                          {new Date(log.date).toLocaleString()}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              <div>\n                <h3 style={{\n                  marginBottom: '16px',\n                  color: '#333'\n                }}>Resume Management</h3>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '16px',\n                  flexWrap: 'wrap'\n                }}>\n                  <label style={{\n                    ...getStyle('buttonPrimary'),\n                    background: '#f5f5f5',\n                    color: '#333',\n                    border: '1px solid #ddd',\n                    cursor: resumeUploadLoading ? 'not-allowed' : 'pointer'\n                  }}>\n                    <FiUpload />\n                    {resumeUploadLoading ? 'Uploading...' : 'Upload Resume'}\n                    <input\n                      type=\"file\"\n                      accept=\"application/pdf\"\n                      onChange={handleResumeUpload}\n                      disabled={resumeUploadLoading}\n                      style={{ display: 'none' }}\n                    />\n                  </label>\n\n                  {resumeUrl && (\n                    <a\n                      href={resumeUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      style={{\n                        ...getStyle('buttonPrimary'),\n                        background: 'none',\n                        border: '1px solid #4caf50',\n                        color: '#4caf50',\n                        textDecoration: 'none'\n                      }}\n                    >\n                      View Resume\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Chat Interface */}\n        {activeTab === \"resume\" && (\n          <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: '#333'\n              }}>Career Assistant</h2>\n              <p style={{\n                opacity: 0.8,\n                marginBottom: '24px',\n                color: '#666'\n              }}>\n                Get personalized resume advice and career guidance\n              </p>\n\n              {/* Chat messages */}\n              <div style={{\n                height: '50vh',\n                overflowY: 'auto',\n                marginBottom: '24px',\n                padding: '16px',\n                backgroundColor: '#f5f5f5',\n                border: '1px solid #e0e0e0',\n                borderRadius: '8px'\n              }}>\n\n                {messages.length === 0 ? (\n                  <div style={{\n                    height: '100%',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    textAlign: 'center',\n                    opacity: 0.7\n                  }}>\n                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                    <h3 style={{\n                      margin: 0,\n                      color: '#333'\n                    }}>Start a conversation</h3>\n                    <p style={{\n                      color: '#666'\n                    }}>Ask about resumes, interviews, or career advice</p>\n                  </div>\n                ) : (\n                  messages.map((msg, idx) => (\n                    <div\n                      key={idx}\n                      style={{\n                        ...(msg.role === 'user' ? getStyle('chatBubbleUser') : getStyle('chatBubbleBot')),\n                        animation: 'fadeIn 0.3s ease'\n                      }}\n                    >\n                      {msg.role === 'bot' ? (\n                        <ReactMarkdown>{msg.content}</ReactMarkdown>\n                      ) : (\n                        msg.content\n                      )}\n                    </div>\n                  ))\n                )}\n                {loading && (\n                  <div style={getStyle('chatBubbleBot')}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.2s'\n                      }} />\n                      <div style={{\n                        width: '10px',\n                        height: '10px',\n                        borderRadius: '50%',\n                        backgroundColor: '#1976d2',\n                        animation: 'pulse 1.4s infinite ease-in-out',\n                        animationDelay: '0.4s'\n                      }} />\n                    </div>\n                  </div>\n                )}\n                <div ref={chatEndRef} />\n              </div>\n\n              {/* Input area */}\n              <form\n                style={{ display: 'flex', gap: '12px' }}\n                onSubmit={e => {\n                  e.preventDefault();\n                  sendMessage();\n                }}\n              >\n                <input\n                  type=\"text\"\n                  placeholder=\"Type your message...\"\n                  style={getStyle('inputField')}\n                  value={input}\n                  onChange={e => setInput(e.target.value)}\n                  disabled={loading}\n                />\n                <button\n                  type=\"submit\"\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    minWidth: '100px'\n                  }}\n                  disabled={loading || !input.trim()}\n                >\n                  {loading ? 'Sending...' : 'Send'}\n                </button>\n              </form>\n            </div>\n          </div>\n        )}\n\n        {/* Enhanced DSA Company Questions */}\n        {activeTab === \"dsa\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              {/* Header with revert button */}\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>\n                <div>\n                  <h2 style={{ marginTop: 0, marginBottom: '8px' }}>🚀 Company Wise DSA Questions</h2>\n                  <p style={{ opacity: 0.8, margin: 0 }}>\n                    Explore DSA questions from top companies with enhanced filtering and favorites\n                  </p>\n                </div>\n                {showRevertButton && (\n                  <button\n                    onClick={revertHeaderChanges}\n                    style={{\n                      ...getStyle('buttonPrimary'),\n                      background: '#ff6b6b',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px',\n                      fontSize: '14px',\n                      padding: '8px 16px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      color: 'white',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => e.target.style.background = '#ff5252'}\n                    onMouseLeave={(e) => e.target.style.background = '#ff6b6b'}\n                  >\n                    <FiRefreshCw size={16} />\n                    Revert Header Color\n                  </button>\n                )}\n              </div>\n\n              {/* Category Tabs */}\n              <div style={{\n                display: 'flex',\n                gap: '8px',\n                marginBottom: '20px',\n                flexWrap: 'wrap',\n                borderBottom: '1px solid #eee',\n                paddingBottom: '16px'\n              }}>\n                {['all', ...Object.keys(companyCategories)].map(category => (\n                  <button\n                    key={category}\n                    onClick={() => setSelectedCategory(category)}\n                    style={{\n                      padding: '8px 16px',\n                      borderRadius: '20px',\n                      border: selectedCategory === category\n                        ? 'none'\n                        : '1px solid #ddd',\n                      background: selectedCategory === category\n                        ? globalStyles.currentTheme.primary\n                        : 'transparent',\n                      color: selectedCategory === category\n                        ? 'white'\n                        : '#666',\n                      cursor: 'pointer',\n                      fontSize: '14px',\n                      fontWeight: selectedCategory === category ? 600 : 400,\n                      transition: 'all 0.3s ease',\n                      textTransform: 'capitalize'\n                    }}\n                    onMouseEnter={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = '#f5f5f5';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (selectedCategory !== category) {\n                        e.target.style.background = 'transparent';\n                      }\n                    }}\n                  >\n                    {category === 'all' ? '🌟 All' : `${category === 'FAANG' ? '🔥' : category === 'Big Tech' ? '💻' : category === 'Startups' ? '🚀' : category === 'Finance' ? '💰' : category === 'Indian' ? '🇮🇳' : '🏢'} ${category}`}\n                  </button>\n                ))}\n              </div>\n\n              {/* Controls Row */}\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px',\n                flexWrap: 'wrap',\n                alignItems: 'center'\n              }}>\n                {/* Search box */}\n                <div style={{ position: 'relative', flex: 1, minWidth: '300px' }}>\n                  <div style={{\n                    position: 'absolute',\n                    left: '16px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#666'\n                  }}>\n                    <FiSearch size={20} />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search companies...\"\n                    style={{\n                      ...getStyle('inputField'),\n                      paddingLeft: '48px',\n                      width: '100%'\n                    }}\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  {searchTerm && (\n                    <button\n                      style={{\n                        position: 'absolute',\n                        right: '16px',\n                        top: '50%',\n                        transform: 'translateY(-50%)',\n                        background: 'none',\n                        border: 'none',\n                        color: '#666',\n                        cursor: 'pointer'\n                      }}\n                      onClick={() => setSearchTerm(\"\")}\n                    >\n                      <FiX size={20} />\n                    </button>\n                  )}\n                </div>\n\n                {/* Sort dropdown */}\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  style={{\n                    ...getStyle('inputField'),\n                    width: 'auto',\n                    minWidth: '150px'\n                  }}\n                >\n                  <option value=\"name\">📝 Sort by Name</option>\n                  <option value=\"favorites\">⭐ Favorites First</option>\n                </select>\n              </div>\n\n              {/* Recent Companies */}\n              {recentCompanies.length > 0 && (\n                <div style={{\n                  marginBottom: '24px',\n                  padding: '16px',\n                  borderRadius: '12px',\n                  background: '#f8f9fa',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <h3 style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    fontSize: '16px',\n                    marginBottom: '12px',\n                    color: darkMode ? '#e0e0e0' : '#333',\n                    margin: '0 0 12px 0'\n                  }}>\n                    <FiClock color={darkMode ? '#e0e0e0' : '#666'} /> Recently Viewed\n                  </h3>\n                  <div style={{\n                    display: 'flex',\n                    gap: '8px',\n                    flexWrap: 'wrap'\n                  }}>\n                    {recentCompanies.map(company => (\n                      <button\n                        key={company}\n                        onClick={() => handleCompanyClick(company)}\n                        style={{\n                          padding: '6px 12px',\n                          borderRadius: '16px',\n                          border: `1px solid ${globalStyles.currentTheme.primary}`,\n                          background: darkMode ? '#1a1a1a' : 'transparent',\n                          color: globalStyles.currentTheme.primary,\n                          cursor: 'pointer',\n                          fontSize: '12px',\n                          transition: 'all 0.3s ease'\n                        }}\n                        onMouseEnter={(e) => {\n                          e.target.style.background = globalStyles.currentTheme.primary;\n                          e.target.style.color = 'white';\n                        }}\n                        onMouseLeave={(e) => {\n                          e.target.style.background = darkMode ? '#1a1a1a' : 'transparent';\n                          e.target.style.color = globalStyles.currentTheme.primary;\n                        }}\n                      >\n                        {company}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Companies grid */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',\n                gap: '16px',\n                marginTop: '24px'\n              }}>\n                {getFilteredCompanies().map((company, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      ...getStyle('companyCard'),\n                      position: 'relative',\n                      transform: favoriteCompanies.includes(company) ? 'scale(1.02)' : 'scale(1)',\n                      border: favoriteCompanies.includes(company)\n                        ? `2px solid ${globalStyles.currentTheme.primary}`\n                        : `1px solid ${darkMode ? '#444' : globalStyles.currentTheme.border}`,\n                      background: darkMode ? '#2a2a2a' : globalStyles.currentTheme.surface,\n                      color: darkMode ? '#e0e0e0' : globalStyles.currentTheme.text,\n                      animation: `fadeIn 0.3s ease ${index * 0.1}s both`,\n                      boxShadow: darkMode\n                        ? '0 4px 6px rgba(0, 0, 0, 0.3)'\n                        : `0 4px 6px ${globalStyles.currentTheme.shadow}`\n                    }}\n                    onClick={() => handleCompanyClick(company)}\n                  >\n                    {/* Favorite button */}\n                    <button\n                      onClick={(e) => toggleFavorite(company, e)}\n                      style={{\n                        position: 'absolute',\n                        top: '8px',\n                        right: '8px',\n                        background: 'none',\n                        border: 'none',\n                        cursor: 'pointer',\n                        color: favoriteCompanies.includes(company) ? '#ff6b6b' : '#ccc',\n                        transition: 'all 0.3s ease',\n                        fontSize: '18px'\n                      }}\n                    >\n                      <FiHeart fill={favoriteCompanies.includes(company) ? 'currentColor' : 'none'} />\n                    </button>\n\n                    {/* Company initial with gradient */}\n                    <div style={{\n                      width: '56px',\n                      height: '56px',\n                      borderRadius: '50%',\n                      background: `linear-gradient(135deg, ${globalStyles.currentTheme.primary}, ${globalStyles.currentTheme.primaryDark})`,\n                      color: 'white',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '24px',\n                      fontWeight: 700,\n                      marginBottom: '12px',\n                      boxShadow: `0 4px 8px ${globalStyles.currentTheme.shadow}`\n                    }}>\n                      {company.charAt(0)}\n                    </div>\n\n                    {/* Company name */}\n                    <div style={{\n                      fontWeight: 600,\n                      textAlign: 'center',\n                      fontSize: '14px',\n                      marginBottom: '8px'\n                    }}>\n                      {company}\n                    </div>\n\n                    {/* Mock stats */}\n                    <div style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      fontSize: '12px',\n                      opacity: 0.7,\n                      marginTop: '8px'\n                    }}>\n                      <span>📊 {Math.floor(Math.random() * 50) + 10} Questions</span>\n                      <span>⭐ {(Math.random() * 2 + 3).toFixed(1)}</span>\n                    </div>\n\n                    {/* Category badge */}\n                    {Object.entries(companyCategories).map(([category, companies]) => {\n                      if (companies.some(c => c.toLowerCase() === company.toLowerCase())) {\n                        return (\n                          <div\n                            key={category}\n                            style={{\n                              position: 'absolute',\n                              top: '8px',\n                              left: '8px',\n                              background: globalStyles.currentTheme.primary,\n                              color: 'white',\n                              padding: '2px 6px',\n                              borderRadius: '8px',\n                              fontSize: '10px',\n                              fontWeight: 600\n                            }}\n                          >\n                            {category}\n                          </div>\n                        );\n                      }\n                      return null;\n                    })}\n                  </div>\n                ))}\n              </div>\n\n              {/* No results message */}\n              {getFilteredCompanies().length === 0 && (\n                <div style={{\n                  textAlign: 'center',\n                  padding: '40px',\n                  opacity: 0.7,\n                  color: darkMode ? '#e0e0e0' : '#666'\n                }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>\n                  <h3 style={{ color: darkMode ? '#e0e0e0' : '#333' }}>No companies found</h3>\n                  <p style={{ color: darkMode ? '#ccc' : '#666' }}>Try adjusting your search or category filter</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Quizzes Section */}\n        {activeTab === \"quizzes\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{ marginTop: 0 }}>Career Quizzes</h2>\n              <p style={{ opacity: 0.8, marginBottom: '24px' }}>\n                Test your knowledge with our career-focused quizzes!\n              </p>\n\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                gap: '16px'\n              }}>\n                {quizButtons.map((quiz, index) => (\n                  <div\n                    key={index}\n                    style={getStyle('quizCard')}\n                    onClick={() => openQuizLink(quiz.link)}\n                  >\n                    <div>\n                      <h3 style={{ margin: '0 0 8px 0' }}>{quiz.title}</h3>\n                      <p style={{\n                        margin: 0,\n                        fontSize: '14px',\n                        opacity: 0.8\n                      }}>\n                        {quiz.description}\n                      </p>\n                    </div>\n                    <div style={{ color: darkMode ? '#90caf9' : '#1976d2' }}>\n                      <FiExternalLink size={20} />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Other tabs */}\n        {activeTab === \"coding\" && (\n          <div style={{ padding: '24px' }}>\n            <Coding />\n          </div>\n        )}\n        {activeTab === \"resources\" && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: darkMode ? '#e0e0e0' : '#333'\n              }}>Resources</h2>\n              <p style={{\n                opacity: 0.8,\n                marginBottom: '24px',\n                color: darkMode ? '#ccc' : '#666'\n              }}>\n                Upload and manage your study materials and notes\n              </p>\n\n              <div style={{ marginBottom: '24px' }}>\n                <label style={{\n                  ...getStyle('buttonPrimary'),\n                  background: darkMode ? '#3a3a3a' : '#f5f5f5',\n                  color: darkMode ? '#e0e0e0' : '#333',\n                  border: `1px solid ${darkMode ? '#555' : '#ddd'}`,\n                  cursor: resourceUploadLoading ? 'not-allowed' : 'pointer'\n                }}>\n                  <FiUpload />\n                  {resourceUploadLoading ? 'Uploading...' : 'Upload Resource'}\n                  <input\n                    type=\"file\"\n                    accept=\".pdf,.doc,.docx,.txt\"\n                    onChange={handleResourceUpload}\n                    disabled={resourceUploadLoading}\n                    style={{ display: 'none' }}\n                  />\n                </label>\n              </div>\n\n              <div>\n                <h3 style={{\n                  marginBottom: '16px',\n                  color: darkMode ? '#e0e0e0' : '#333'\n                }}>Your Resources</h3>\n                {userResources.length === 0 ? (\n                  <p style={{\n                    opacity: 0.7,\n                    color: darkMode ? '#ccc' : '#666'\n                  }}>No resources uploaded yet</p>\n                ) : (\n                  <div style={{\n                    backgroundColor: darkMode ? '#2a2a2a' : '#f5f5f5',\n                    border: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,\n                    borderRadius: '8px',\n                    padding: '16px'\n                  }}>\n                    {userResources.map((file, idx) => {\n                      const { data: urlData } = supabase.storage.from('resources').getPublicUrl(`${user.id}/${file.name}`);\n                      return (\n                        <div key={idx} style={{\n                          padding: '12px',\n                          borderBottom: darkMode ? '1px solid #444' : '1px solid #eee',\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          alignItems: 'center'\n                        }}>\n                          <span style={{\n                            color: darkMode ? '#e0e0e0' : '#333'\n                          }}>{file.name}</span>\n                          <a\n                            href={urlData.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            style={{\n                              color: darkMode ? '#90caf9' : '#1976d2',\n                              textDecoration: 'none',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '4px'\n                            }}\n                          >\n                            <FiExternalLink size={16} />\n                            Open\n                          </a>\n                        </div>\n                      );\n                    })}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n        {activeTab === \"academics\" && <Exams />}\n        {activeTab === \"faq\" && <Faq />}\n        {activeTab === \"admin\" && user?.email === ADMIN_EMAIL && (\n          <div style={{ padding: '24px' }}>\n            <div style={getStyle('card')}>\n              <h2 style={{\n                marginTop: 0,\n                color: darkMode ? '#e0e0e0' : '#333'\n              }}>Admin Panel</h2>\n              <div style={{\n                display: 'flex',\n                gap: '16px',\n                marginBottom: '24px'\n              }}>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'users' ?\n                      globalStyles.currentTheme.primary : (darkMode ? '#2a2a2a' : 'transparent'),\n                    color: adminTab === 'users' ?\n                      'white' : (darkMode ? '#e0e0e0' : '#333'),\n                    border: `1px solid ${darkMode ? '#444' : '#ddd'}`\n                  }}\n                  onClick={() => setAdminTab('users')}\n                >\n                  Users\n                </button>\n                <button\n                  style={{\n                    ...getStyle('buttonPrimary'),\n                    background: adminTab === 'resources' ?\n                      globalStyles.currentTheme.primary : (darkMode ? '#2a2a2a' : 'transparent'),\n                    color: adminTab === 'resources' ?\n                      'white' : (darkMode ? '#e0e0e0' : '#333'),\n                    border: `1px solid ${darkMode ? '#444' : '#ddd'}`\n                  }}\n                  onClick={() => setAdminTab('resources')}\n                >\n                  Resources\n                </button>\n              </div>\n\n              {adminTab === 'users' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: darkMode ? '#e0e0e0' : '#333'\n                  }}>All Users</h3>\n                  <div style={{\n                    backgroundColor: darkMode ? '#2a2a2a' : '#f5f5f5',\n                    border: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,\n                    borderRadius: '8px',\n                    padding: '16px'\n                  }}>\n                    {allUsers.map((user, idx) => (\n                      <div key={idx} style={{\n                        padding: '12px',\n                        borderBottom: darkMode ? '1px solid #444' : '1px solid #eee',\n                        color: darkMode ? '#e0e0e0' : '#333'\n                      }}>\n                        {user.email}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {adminTab === 'resources' && (\n                <div>\n                  <h3 style={{\n                    marginBottom: '16px',\n                    color: darkMode ? '#e0e0e0' : '#333'\n                  }}>All Resources</h3>\n                  <p style={{\n                    opacity: 0.7,\n                    color: darkMode ? '#ccc' : '#666'\n                  }}>Resource management coming soon</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Notification */}\n      {notification && (\n        <div style={{\n          ...getStyle('notification'),\n          backgroundColor: notification.type === 'error' ? '#f44336' :\n                         notification.type === 'success' ? '#4caf50' : '#2196f3',\n          color: 'white',\n          border: darkMode ? `1px solid ${notification.type === 'error' ? '#d32f2f' : notification.type === 'success' ? '#388e3c' : '#1976d2'}` : 'none'\n        }}>\n          {notification.msg}\n        </div>\n      )}\n\n      {/* Enhanced Global styles */}\n      <style>{`\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        @keyframes slideIn {\n          from { transform: translateX(100%); }\n          to { transform: translateX(0); }\n        }\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.5; }\n        }\n        @keyframes bounce {\n          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }\n          40%, 43% { transform: translate3d(0,-8px,0); }\n          70% { transform: translate3d(0,-4px,0); }\n          90% { transform: translate3d(0,-2px,0); }\n        }\n        @keyframes glow {\n          0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.3); }\n          50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.6); }\n        }\n        @keyframes shimmer {\n          0% { background-position: -200px 0; }\n          100% { background-position: calc(200px + 100%) 0; }\n        }\n\n        /* Enhanced hover effects */\n        .company-card:hover {\n          animation: bounce 0.6s ease;\n        }\n\n        .favorite-btn:hover {\n          animation: pulse 0.5s ease;\n        }\n\n        /* Smooth transitions for all interactive elements */\n        button, input, select {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        button:hover {\n          transform: translateY(-1px);\n        }\n\n        * {\n          box-sizing: border-box;\n        }\n        body {\n          margin: 0;\n          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n        }\n      `}</style>\n    </div>\n  );\n};\nexport default EduAIChatBot;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,GAAG,QAAQ,oBAAoB;AAChD,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,IAAI,EAAEC,EAAE,QAAQ,kBAAkB;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,OAAOC,YAAY,MAAM,aAAa;AACtC,SACEC,MAAM,EAAEC,GAAG,EAAEC,aAAa,EAAEC,cAAc,EAAEC,UAAU,EACtDC,MAAM,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EACvDC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EACrCC,WAAW,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,cAAc,EACjEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,QACxD,gBAAgB;AACvB,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,KAAK,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AACzF,OAAOC,aAAa,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3CV,KAAK,CAACW,QAAQ,CAACV,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,CAAC;;AAEvE;AACA,MAAMO,mBAAmB,GAAG3C,YAAY,CAAC4C,GAAG,CAACC,IAAI,IAAI;EACnD,MAAMC,OAAO,GAAG;IACd,QAAQ,eAAEP,OAAA,CAAChC,UAAU;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxB,KAAK,eAAEX,OAAA,CAAC/B,MAAM;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjB,QAAQ,eAAEX,OAAA,CAACnB,QAAQ;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtB,WAAW,eAAEX,OAAA,CAACrB,WAAW;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,SAAS,eAAEX,OAAA,CAAClB,aAAa;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5B,UAAU,eAAEX,OAAA,CAACpB,WAAW;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3B,WAAW,eAAEX,OAAA,CAAC5B,MAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,KAAK,eAAEX,OAAA,CAAC9B,YAAY;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvB,OAAO,eAAEX,OAAA,CAAC1B,QAAQ;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC;EAED,OAAO;IACL,GAAGL,IAAI;IACPM,IAAI,EAAEL,OAAO,CAACD,IAAI,CAACO,GAAG,CAAC,IAAIN,OAAO,CAACD,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,iBAAIf,OAAA,CAAC7B,OAAO;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC5E,CAAC;AACH,CAAC,CAAC;AAEF,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsE,QAAQ,EAAEC,WAAW,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwE,MAAM,EAAEC,SAAS,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC0E,OAAO,EAAEC,UAAU,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4E,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8E,SAAS,EAAEC,YAAY,CAAC,GAAG/E,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACgF,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkF,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoF,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACsF,IAAI,EAAEC,OAAO,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAEtC,MAAM,CAACwF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0F,SAAS,EAAEC,YAAY,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4F,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC8F,aAAa,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM+F,WAAW,GAAG,mBAAmB;EACvC,MAAM,CAACC,QAAQ,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAC/B,MAAM,CAACiG,QAAQ,EAAEC,WAAW,CAAC,GAAGlG,QAAQ,CAAC,OAAO,CAAC;EACjD,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqG,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMuG,UAAU,GAAGrG,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACA,MAAM,CAACsG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0G,MAAM,EAAEC,SAAS,CAAC,GAAG3G,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAAC4G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8G,eAAe,EAAEC,kBAAkB,CAAC,GAAG/G,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjH,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMkH,OAAO,GAAG,yCAAyC;EACzD,MAAMC,YAAY,GAAG,0CAA0C;EAC/D,MAAMC,iBAAiB,GAAG,kNAAkN;EAC5O,MAAMC,QAAQ,GAAG7E,YAAY,CAAC2E,YAAY,EAAEC,iBAAiB,CAAC;;EAE9D;EACA,MAAME,iBAAiB,GAAG;IACxB,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC7D,UAAU,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IACpF,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;IACrF,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC;IAC/F,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC;IACzE,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;IAC7E,QAAQ,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;IACrE,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;EACtF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,CACf,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EACxE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EACnE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EACnE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAChE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAC9D,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAChE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAClE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAC5D,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAC9D,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAC3D,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EACjE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAC/D,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAC7D,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAC1D,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAChE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EACnE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EACzD,sBAAsB,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EACnE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EACtD,iBAAiB,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAC3D,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAC/D,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EACnE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EACzD,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAC5D,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAChE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAClE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,uBAAuB,EAAE,oBAAoB,EACxE,QAAQ,EAAE,OAAO,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAC/D,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAC7D,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAC9D,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAChE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EACnE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,qBAAqB,EAAE,QAAQ,EAChE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,EAC5D,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EACxD,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,EAC/D,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EACxD,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAChE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAChE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAChC;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB;IACExD,KAAK,EAAE,gBAAgB;IACvByD,WAAW,EAAE,+DAA+D;IAC5EC,IAAI,EAAE;EACR,CAAC,EACD;IACE1D,KAAK,EAAE,oBAAoB;IAC3ByD,WAAW,EAAE,uCAAuC;IACpDC,IAAI,EAAE;EACR,CAAC,EACD;IACE1D,KAAK,EAAE,oBAAoB;IAC3ByD,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE;EACR,CAAC,EACD;IACE1D,KAAK,EAAE,0CAA0C;IACjDyD,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE;EACR,CAAC,EACD;IACE1D,KAAK,EAAE,qBAAqB;IAC5ByD,WAAW,EAAE,kDAAkD;IAC/DC,IAAI,EAAE;EACR,CAAC,EACD;IACE1D,KAAK,EAAE,UAAU;IACjByD,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE;EACR,CAAC,EACD;IACE1D,KAAK,EAAE,2CAA2C;IAClDyD,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAE;EACR,CAAC,EACA;IACC1D,KAAK,EAAE,sBAAsB;IAC7ByD,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE;EAER,CAAC,CACF;;EAED;EACA,MAAMC,MAAM,GAAG;IACb,GAAG9G,YAAY;IACf+G,YAAY,EAAE;MACZ,GAAG/G,YAAY,CAAC+G,YAAY;MAC5BC,eAAe,EAAEhH,YAAY,CAACiH,YAAY,CAACC,UAAU;MACrDC,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG;IACnC,CAAC;IACDC,MAAM,EAAE;MACN,GAAGrH,YAAY,CAACsH,WAAW;MAC3BC,YAAY,EAAE,aAAavH,YAAY,CAACiH,YAAY,CAACO,MAAM;IAC7D,CAAC;IACDC,OAAO,EAAE;MACP,GAAGzH,YAAY,CAAC0H,YAAY;MAC5BV,eAAe,EAAEhH,YAAY,CAACiH,YAAY,CAACU,OAAO;MAClDC,WAAW,EAAE,aAAa5H,YAAY,CAACiH,YAAY,CAACO,MAAM,EAAE;MAC5DK,SAAS,EAAExD,WAAW,GAAG,eAAe,GAAG;IAC7C,CAAC;IACDyD,WAAW,EAAE;MACX,GAAG9H,YAAY,CAAC+H,cAAc;MAC9BZ,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG,IAAI;MACrCF,UAAU,EAAElH,YAAY,CAACiH,YAAY,CAACU,OAAO;MAC7CH,MAAM,EAAE,aAAaxH,YAAY,CAACiH,YAAY,CAACO,MAAM,EAAE;MACvDQ,UAAU,EAAE,eAAe;MAC3B,SAAS,EAAE;QACTd,UAAU,EAAElH,YAAY,CAACiH,YAAY,CAACgB,OAAO;QAC7Cd,KAAK,EAAE;MACT;IACF,CAAC;IACDe,iBAAiB,EAAE;MACjB,GAAGlI,YAAY,CAACmI,oBAAoB;MACpChB,KAAK,EAAE,OAAO;MACdD,UAAU,EAAElH,YAAY,CAACiH,YAAY,CAACgB,OAAO;MAC7CT,MAAM,EAAE,aAAaxH,YAAY,CAACiH,YAAY,CAACgB,OAAO;IACxD,CAAC;IACDG,WAAW,EAAE;MACX,GAAGpI,YAAY,CAACqI,cAAc;MAC9BC,UAAU,EAAEjE,WAAW,GAAG,OAAO,GAAG,GAAG;MACvC2C,eAAe,EAAEhH,YAAY,CAACiH,YAAY,CAACC,UAAU;MACrDC,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG,IAAI;MACrCmB,SAAS,EAAE;IACb,CAAC;IACDC,IAAI,EAAE;MACJ,GAAGxI,YAAY,CAACyI,OAAO;MACvBzB,eAAe,EAAEhH,YAAY,CAACiH,YAAY,CAACU,OAAO;MAClDR,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG,IAAI;MACrCI,MAAM,EAAE,aAAaxH,YAAY,CAACiH,YAAY,CAACO,MAAM,EAAE;MACvDkB,SAAS,EAAE,aAAa1I,YAAY,CAACiH,YAAY,CAAC0B,MAAM;IAC1D,CAAC;IACDC,aAAa,EAAE;MACb,GAAG5I,YAAY,CAAC4I;IAClB,CAAC;IACDC,UAAU,EAAE;MACV,GAAG7I,YAAY,CAAC6I,UAAU;MAC1B7B,eAAe,EAAE,MAAM;MACvBG,KAAK,EAAE,MAAM;MACbK,MAAM,EAAE,gBAAgB;MACxB,SAAS,EAAE;QACTsB,WAAW,EAAE9I,YAAY,CAACiH,YAAY,CAACgB,OAAO;QAC9Cc,OAAO,EAAE,MAAM;QACfL,SAAS,EAAE,aAAa1I,YAAY,CAACiH,YAAY,CAACgB,OAAO;MAC3D;IACF,CAAC;IACDe,cAAc,EAAE;MACd,GAAGhJ,YAAY,CAACgJ,cAAc;MAC9BhC,eAAe,EAAEhH,YAAY,CAACiH,YAAY,CAACgB,OAAO;MAClDd,KAAK,EAAE;IACT,CAAC;IACD8B,aAAa,EAAE;MACb,GAAGjJ,YAAY,CAACiJ,aAAa;MAC7BjC,eAAe,EAAEhH,YAAY,CAACiH,YAAY,CAACiC,SAAS;MACpD/B,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG,IAAI;MACrCI,MAAM,EAAE;IACV,CAAC;IACD2B,WAAW,EAAE;MACX,GAAGnJ,YAAY,CAACoJ;IAClB,CAAC;IACDC,QAAQ,EAAE;MACR,GAAGrJ,YAAY,CAACsJ,WAAW;MAC3BtC,eAAe,EAAEhH,YAAY,CAACiH,YAAY,CAACU;IAC7C,CAAC;IACDrC,YAAY,EAAE;MACZ,GAAGtF,YAAY,CAACsF;IAClB;EACF,CAAC;;EAED;EACA,MAAMiE,QAAQ,GAAGA,CAACC,SAAS,EAAEC,KAAK,GAAG,KAAK,KAAK;IAC7C,MAAMC,SAAS,GAAG5C,MAAM,CAAC0C,SAAS,CAAC;IACnC,IAAI,OAAOE,SAAS,KAAK,UAAU,EAAE,OAAOA,SAAS,CAAC,CAAC;IACvD,IAAID,KAAK,IAAIC,SAAS,CAAC,SAAS,CAAC,EAAE;MACjC,OAAO;QAAE,GAAGA,SAAS;QAAE,GAAGA,SAAS,CAAC,SAAS;MAAE,CAAC;IAClD;IACA,OAAOA,SAAS;EAClB,CAAC;;EAED;EACAtK,SAAS,CAAC,MAAM;IACd,MAAMuK,WAAW,GAAG5J,kBAAkB,CAACJ,IAAI,EAAG8E,IAAI,IAAK;MACrD,IAAIA,IAAI,EAAE;QACRb,SAAS,CAACa,IAAI,CAACmF,GAAG,CAAC;MACrB,CAAC,MAAM;QACLC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxChG,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IACF,OAAO,MAAM6F,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAENvK,SAAS,CAAC,MAAM;IACd,IAAIuE,MAAM,EAAE;MACV,MAAMoG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;QACnC,MAAMC,OAAO,GAAGzK,GAAG,CAACK,EAAE,EAAE,OAAO,EAAE+D,MAAM,CAAC;QACxC,MAAMsG,OAAO,GAAG,MAAM3K,MAAM,CAAC0K,OAAO,CAAC;QAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;UACpB,MAAMC,QAAQ,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UAC/B;UACAP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,QAAQ,CAACE,EAAE,CAAC;QAC/C,CAAC,MAAM;UACLR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC9B;QACAhG,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;MACDiG,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACpG,MAAM,CAAC,CAAC;;EAEZ;EACAvE,SAAS,CAAC,MAAM;IACdkL,KAAK,CAAC,oBAAoB,CAAC,CACxBC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACpD,IAAI,CAAC,CAAC,CAAC,CACzBmD,IAAI,CAAEH,IAAI,IAAKpG,YAAY,CAACoG,IAAI,CAAC,CAAC,CAClCK,KAAK,CAAEC,GAAG,IAAKb,OAAO,CAACc,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtL,SAAS,CAAC,MAAM;IACdoH,QAAQ,CAAC7G,IAAI,CAACiL,UAAU,CAAC,CAAC,CAACL,IAAI,CAAC,CAAC;MAAEH,IAAI,EAAE;QAAES;MAAQ;IAAE,CAAC,KAAK;MACzDnG,OAAO,CAAC,CAAAmG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEpG,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,MAAM;MAAE2F,IAAI,EAAEU;IAAS,CAAC,GAAGtE,QAAQ,CAAC7G,IAAI,CAACoL,iBAAiB,CAAC,CAACC,MAAM,EAAEH,OAAO,KAAK;MAC9EnG,OAAO,CAAC,CAAAmG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEpG,IAAI,KAAI,IAAI,CAAC;IAChC,CAAC,CAAC;IACF,OAAO,MAAM;MACXqG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,YAAY,CAACtB,WAAW,CAAC,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,CAACnD,QAAQ,CAAC7G,IAAI,CAAC,CAAC;;EAEnB;EACA,MAAMuL,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAAC3G,IAAI,EAAE;IACpBG,sBAAsB,CAAC,IAAI,CAAC;IAC5B,MAAM2G,QAAQ,GAAG,GAAG9G,IAAI,CAAC+G,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMnE,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACjG,IAAI,CAAClB,KAAK,EAAE;MACV,MAAM;QAAEP,IAAI,EAAE0B;MAAQ,CAAC,GAAGtF,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,SAAS,CAAC,CAACI,YAAY,CAACR,QAAQ,CAAC;MACjFzG,YAAY,CAACgH,OAAO,CAACE,SAAS,CAAC;MAC/BC,gBAAgB,CAAC,+BAA+B,EAAE,SAAS,CAAC;MAC5DC,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,MAAM;MACLD,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC;IACpD;IACArH,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMuH,oBAAoB,GAAG,MAAOhB,CAAC,IAAK;IACxC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,IAAI,CAAC3G,IAAI,EAAE;IACpBO,wBAAwB,CAAC,IAAI,CAAC;IAC9B,MAAMuG,QAAQ,GAAG,GAAG9G,IAAI,CAAC+G,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE;IAC1C,MAAM;MAAEd;IAAM,CAAC,GAAG,MAAMnE,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACC,MAAM,CAACL,QAAQ,EAAEH,IAAI,EAAE;MAAES,MAAM,EAAE;IAAK,CAAC,CAAC;IACnG,IAAI,CAAClB,KAAK,EAAE;MACVsB,gBAAgB,CAAC,oBAAoB,EAAE,SAAS,CAAC;MACjDC,WAAW,CAAC,sBAAsBd,IAAI,CAACK,IAAI,EAAE,CAAC;IAChD,CAAC,MAAM;MACLQ,gBAAgB,CAAC,yBAAyB,EAAE,OAAO,CAAC;IACtD;IACAjH,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAMoH,kBAAkB,GAAIC,OAAO,IAAK;IACtC;IACAnG,kBAAkB,CAACoG,IAAI,IAAI;MACzB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;MAChD,OAAO,CAACA,OAAO,EAAE,GAAGE,QAAQ,CAAC,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFR,WAAW,CAAC,UAAUG,OAAO,gBAAgB,CAAC;IAE9C,IAAIA,OAAO,CAACjJ,WAAW,CAAC,CAAC,KAAK,WAAW,EAAE;MACzCuJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uCAAuC;MAC9D;IACF;IACA,MAAMC,gBAAgB,GAAGT,OAAO,CAACU,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACpDJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgBC,gBAAgB,OAAO;EAChE,CAAC;;EAED;EACA,MAAME,cAAc,GAAGA,CAACX,OAAO,EAAElB,CAAC,KAAK;IACrCA,CAAC,CAAC8B,eAAe,CAAC,CAAC,CAAC,CAAC;IACrBjH,oBAAoB,CAACsG,IAAI,IAAI;MAC3B,IAAIA,IAAI,CAACY,QAAQ,CAACb,OAAO,CAAC,EAAE;QAC1B,OAAOC,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,OAAO,CAAC;MACxC,CAAC,MAAM;QACL,OAAO,CAAC,GAAGC,IAAI,EAAED,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChC/G,mBAAmB,CAAC,KAAK,CAAC;IAC1B6F,gBAAgB,CAAC,8CAA8C,EAAE,SAAS,CAAC;;IAE3E;IACA,MAAMmB,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACpE,MAAMC,eAAe,GAAGF,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC;IAExE,IAAIF,cAAc,EAAE;MAClBA,cAAc,CAACI,KAAK,CAACrG,KAAK,GAAG,MAAM;IACrC;IACA,IAAIoG,eAAe,EAAE;MACnBA,eAAe,CAACC,KAAK,CAACrG,KAAK,GAAG,MAAM;IACtC;EACF,CAAC;;EAED;EACA,MAAMsG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIlB,QAAQ,GAAG7F,SAAS;;IAExB;IACA,IAAIf,gBAAgB,KAAK,KAAK,EAAE;MAC9B,MAAM+H,iBAAiB,GAAGjH,iBAAiB,CAACd,gBAAgB,CAAC,IAAI,EAAE;MACnE4G,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAChCqB,iBAAiB,CAACC,IAAI,CAACC,UAAU,IAC/BvB,OAAO,CAACjJ,WAAW,CAAC,CAAC,CAAC8J,QAAQ,CAACU,UAAU,CAACxK,WAAW,CAAC,CAAC,CAAC,IACxDwK,UAAU,CAACxK,WAAW,CAAC,CAAC,CAAC8J,QAAQ,CAACb,OAAO,CAACjJ,WAAW,CAAC,CAAC,CACzD,CACF,CAAC;IACH;;IAEA;IACAmJ,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAChCA,OAAO,CAACjJ,WAAW,CAAC,CAAC,CAAC8J,QAAQ,CAAC/I,UAAU,CAACf,WAAW,CAAC,CAAC,CACzD,CAAC;;IAED;IACA,IAAIyC,MAAM,KAAK,MAAM,EAAE;MACrB0G,QAAQ,CAACsB,IAAI,CAAC,CAAC;IACjB,CAAC,MAAM,IAAIhI,MAAM,KAAK,WAAW,EAAE;MACjC0G,QAAQ,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,MAAMC,IAAI,GAAGjI,iBAAiB,CAACmH,QAAQ,CAACY,CAAC,CAAC;QAC1C,MAAMG,IAAI,GAAGlI,iBAAiB,CAACmH,QAAQ,CAACa,CAAC,CAAC;QAC1C,IAAIC,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAACD,IAAI,IAAIC,IAAI,EAAE,OAAO,CAAC;QAC3B,OAAOH,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC;MAC3B,CAAC,CAAC;IACJ;IAEA,OAAOxB,QAAQ;EACjB,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAIC,GAAG,IAAK;IAC5BzB,MAAM,CAAC0B,IAAI,CAACD,GAAG,EAAE,QAAQ,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC/K,KAAK,CAACgL,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,WAAW,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEnL;IAAM,CAAC;IACpDG,WAAW,CAAE4I,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEkC,WAAW,CAAC,CAAC;IAC7ChL,QAAQ,CAAC,EAAE,CAAC;IACZM,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MAAA,IAAA6K,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMC,MAAM,GAAG,oOAAoOjL,SAAS,iBAAiBR,KAAK,EAAE;MAEpR,MAAMiH,GAAG,GAAG,MAAM3K,KAAK,CAACoP,IAAI,CAC1B,gGAAgG5I,OAAO,EAAE,EACzG;QACE6I,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,CAAC;YAAE/H,IAAI,EAAE4H;UAAO,CAAC;QAC1B,CAAC;MAEL,CAAC,EACD;QACEI,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,MAAMC,QAAQ,GACZ,EAAAV,oBAAA,GAAAnE,GAAG,CAACJ,IAAI,CAACkF,UAAU,cAAAX,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAsB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BF,OAAO,cAAAG,sBAAA,wBAAAC,sBAAA,GAAjCD,sBAAA,CAAmCM,KAAK,cAAAL,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA2C,CAAC,CAAC,cAAAC,sBAAA,uBAA7CA,sBAAA,CAA+C3H,IAAI,KACnD,yBAAyB;MAC3B,MAAMmI,UAAU,GAAG;QAAEd,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAEW;MAAS,CAAC;MACrD3L,WAAW,CAAE4I,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEiD,UAAU,CAAC,CAAC;IAC9C,CAAC,CAAC,OAAO5E,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCjH,WAAW,CAAE4I,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEmC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,WAAW,GAAG/D,KAAK,CAAC6E;MAAQ,CAAC,CACtD,CAAC;IACJ,CAAC,SAAS;MACR1L,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;;EAEA;EACA,MAAM2L,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMjJ,QAAQ,CAAC7G,IAAI,CAAC+P,OAAO,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMzD,gBAAgB,GAAGA,CAAC0D,GAAG,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC/CrK,eAAe,CAAC;MAAEoK,GAAG;MAAEC;IAAK,CAAC,CAAC;IAC9BC,UAAU,CAAC,MAAMtK,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM2G,WAAW,GAAIyD,GAAG,IAAK;IAC3BlK,cAAc,CAACqE,GAAG,IAAI,CACpB;MAAE8F,IAAI,EAAE,UAAU;MAAEE,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAAEL;IAAI,CAAC,EACzD,GAAG7F,GAAG,CAAC4C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACpB,CAAC;EACJ,CAAC;;EAED;EACA,MAAMuD,UAAU,GAAIC,IAAI,IAAK;IAC3B1L,gBAAgB,CAAC8H,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAAC4D,IAAI,GAAG,CAAC5D,IAAI,CAAC4D,IAAI;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA9Q,SAAS,CAAC,MAAM;IACd,IAAIsG,UAAU,CAACyK,OAAO,EAAEzK,UAAU,CAACyK,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACnF,CAAC,EAAE,CAAC5M,QAAQ,EAAEI,OAAO,CAAC,CAAC;;EAEvB;EACA,MAAMyM,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,CAAC,GAAG,IAAIV,IAAI,CAAC,CAAC;MACpBU,CAAC,CAACC,OAAO,CAACD,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;MAC1BD,IAAI,CAACK,IAAI,CAACH,CAAC,CAACI,kBAAkB,CAAC,CAAC,CAAC;IACnC;IACA,OAAON,IAAI;EACb,CAAC;EAED,MAAMO,WAAW,GAAGR,YAAY,CAAC,CAAC;EAClC,MAAMS,SAAS,GAAG;IAChBC,MAAM,EAAEF,WAAW;IACnBG,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzB9G,IAAI,EAAE0G,WAAW,CAACpO,GAAG,CAACyO,GAAG,IAAI3L,WAAW,CAACgH,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAAC8B,IAAI,KAAK,UAAU,IAAI9B,CAAC,CAAC6B,GAAG,CAACyB,UAAU,CAAC,mBAAmB,CAAC,IAAI,IAAIrB,IAAI,CAACjC,CAAC,CAACgC,IAAI,CAAC,CAACe,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACE,MAAM,CAAC;MAC7KrK,eAAe,EAAE;IACnB,CAAC,EACD;MACEkK,KAAK,EAAE,iBAAiB;MACxB9G,IAAI,EAAE0G,WAAW,CAACpO,GAAG,CAACyO,GAAG,IAAI3L,WAAW,CAACgH,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAAC8B,IAAI,KAAK,UAAU,IAAI9B,CAAC,CAAC6B,GAAG,KAAK,8BAA8B,IAAI,IAAII,IAAI,CAACjC,CAAC,CAACgC,IAAI,CAAC,CAACe,kBAAkB,CAAC,CAAC,KAAKM,GAAG,CAAC,CAACE,MAAM,CAAC;MAChLrK,eAAe,EAAE;IACnB,CAAC;EAEL,CAAC;EAED,MAAMsK,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAM,CAAC;MAC3BC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC3B,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QAAEC,WAAW,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAE;MAAE;IACjD;EACF,CAAC;EAED,oBACE5P,OAAA;IAAKmL,KAAK,EAAEjE,QAAQ,CAAC,cAAc,CAAE;IAAA2I,QAAA,gBAEnC7P,OAAA;MAAKmL,KAAK,EAAEjE,QAAQ,CAAC,QAAQ,CAAE;MAAA2I,QAAA,gBAC7B7P,OAAA;QACEmL,KAAK,EAAE;UACLtG,UAAU,EAAE,MAAM;UAClBM,MAAM,EAAE,MAAM;UACdL,KAAK,EAAE,OAAO;UAAE;UAChBgL,WAAW,EAAE,MAAM;UACnBC,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE,KAAK;UACnBtK,UAAU,EAAE;QACd,CAAE;QACFuK,OAAO,EAAEA,CAAA,KAAMjO,cAAc,CAAC,CAACD,WAAW,CAAE;QAC5CmO,YAAY,EAAGrH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,0BAA2B;QAC5EuL,YAAY,EAAGtH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,MAAO;QAAAgL,QAAA,EAEvD7N,WAAW,gBAAGhC,OAAA,CAACnC,GAAG;UAACwS,IAAI,EAAE;QAAG;UAAA7P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGX,OAAA,CAACpC,MAAM;UAACyS,IAAI,EAAE;QAAG;UAAA7P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAETX,OAAA;QAAKmL,KAAK,EAAE;UAAEmF,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAX,QAAA,gBAC7D7P,OAAA;UACEyQ,GAAG,EAAEC,OAAO,CAAC,kBAAkB,CAAE;UACjCC,GAAG,EAAC,YAAY;UAChBxF,KAAK,EAAE;YAAEyF,MAAM,EAAE,MAAM;YAAEd,WAAW,EAAE;UAAO;QAAE;UAAAtP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACFX,OAAA;UAAA6P,QAAA,gBACE7P,OAAA;YACE,yBAAiB;YACjBmL,KAAK,EAAE;cAAE0F,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE,MAAM;cAAEhM,KAAK,EAAE;YAAQ,CAAE;YAAA+K,QAAA,EAC9D;UAED;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNX,OAAA;YACE,4BAAoB;YACpBmL,KAAK,EAAE;cAAE2F,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE,GAAG;cAAEjM,KAAK,EAAE;YAAQ,CAAE;YAAA+K,QAAA,EAC3D;UAED;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAKmL,KAAK,EAAE;UAAEoF,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEQ,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EAChEzN,IAAI,gBACHpC,OAAA,CAAAE,SAAA;UAAA2P,QAAA,gBACE7P,OAAA;YAAKmL,KAAK,EAAE;cACV8F,KAAK,EAAE,MAAM;cACbL,MAAM,EAAE,MAAM;cACdX,YAAY,EAAE,KAAK;cACnBpL,UAAU,EAAE,0BAA0B;cACtC0L,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBU,cAAc,EAAE,QAAQ;cACxBL,UAAU,EAAE,GAAG;cACfd,MAAM,EAAE,SAAS;cACjBjL,KAAK,EAAE,OAAO;cACdqM,cAAc,EAAE;YAClB,CAAE;YAAAtB,QAAA,EACCzN,IAAI,CAACgP,KAAK,KAAKvO,WAAW,gBAAG7C,OAAA,CAAC1B,QAAQ;cAAC+R,IAAI,EAAE,EAAG;cAACvL,KAAK,EAAC;YAAS;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGyB,IAAI,CAACgP,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAA7Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACNX,OAAA;YACEmL,KAAK,EAAE;cACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;cAC5BrC,UAAU,EAAE,0BAA0B;cACtCC,KAAK,EAAE,OAAO;cACdK,MAAM,EAAE,oCAAoC;cAC5CgM,cAAc,EAAE;YAClB,CAAE;YACFjB,OAAO,EAAE9C,YAAa;YAAAyC,QAAA,gBAEtB7P,OAAA,CAACtB,QAAQ;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WACd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHX,OAAA;UACEmL,KAAK,EAAE;YACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;YAC5BrC,UAAU,EAAE,0BAA0B;YACtCC,KAAK,EAAE,OAAO;YACdK,MAAM,EAAE,oCAAoC;YAC5CgM,cAAc,EAAE;UAClB,CAAE;UACFjB,OAAO,EAAEA,CAAA,KAAM;YAAE1I,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UAAE,CAAE;UAAAoI,QAAA,gBAEzE7P,OAAA,CAACvB,OAAO;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNX,OAAA;MAAOmL,KAAK,EAAEjE,QAAQ,CAAC,SAAS,CAAE;MAAA2I,QAAA,eAChC7P,OAAA;QAAKmL,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,EAC7BzP,mBAAmB,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEgR,KAAK,kBACnCtR,OAAA;UAAA6P,QAAA,gBACE7P,OAAA;YACEmL,KAAK,EAAE;cACL,GAAGjE,QAAQ,CAAC,aAAa,CAAC;cAC1B,IAAItF,SAAS,KAAKtB,IAAI,CAACO,GAAG,GAAGqG,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;cAChE6I,MAAM,EAAE,SAAS;cACjBpK,UAAU,EAAE;YACd,CAAE;YACFuK,OAAO,EAAEA,CAAA,KAAM;cACbrO,YAAY,CAACvB,IAAI,CAACO,GAAG,CAAC;cACtBoB,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YACFkO,YAAY,EAAGrH,CAAC,IAAK;cACnB,IAAIlH,SAAS,KAAKtB,IAAI,CAACO,GAAG,EAAE;gBAC1BiI,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGlH,YAAY,CAACiH,YAAY,CAACgB,OAAO;gBAC7DkD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACrG,KAAK,GAAG,OAAO;gBAC9BgE,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC1E,WAAW,GAAG9I,YAAY,CAACiH,YAAY,CAACgB,OAAO;cAChE;YACF,CAAE;YACFwK,YAAY,EAAGtH,CAAC,IAAK;cACnB,IAAIlH,SAAS,KAAKtB,IAAI,CAACO,GAAG,EAAE;gBAC1BiI,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGlH,YAAY,CAACiH,YAAY,CAACU,OAAO;gBAC7DwD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACrG,KAAK,GAAGnH,YAAY,CAACiH,YAAY,CAACG,IAAI;gBACrD+D,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC1E,WAAW,GAAG9I,YAAY,CAACiH,YAAY,CAACO,MAAM;cAC/D;YACF,CAAE;YAAA0K,QAAA,gBAEF7P,OAAA;cAAKmL,KAAK,EAAE;gBAAE2E,WAAW,EAAE;cAAO,CAAE;cAAAD,QAAA,EAAEvP,IAAI,CAACM;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDX,OAAA;cAAMmL,KAAK,EAAE;gBAAEmF,IAAI,EAAE;cAAE,CAAE;cAAAT,QAAA,EAAEvP,IAAI,CAACQ;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC5CL,IAAI,CAACiR,QAAQ,CAACvC,MAAM,GAAG,CAAC,iBACvBhP,OAAA;cAAKkQ,OAAO,EAAGpH,CAAC,IAAK;gBACnBA,CAAC,CAAC8B,eAAe,CAAC,CAAC;gBACnBgD,UAAU,CAACtN,IAAI,CAACQ,KAAK,CAAC;cACxB,CAAE;cAAA+O,QAAA,EACC3N,aAAa,CAAC5B,IAAI,CAACQ,KAAK,CAAC,gBAAGd,OAAA,CAAClC,aAAa;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGX,OAAA,CAACjC,cAAc;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELL,IAAI,CAACiR,QAAQ,CAACvC,MAAM,GAAG,CAAC,IAAI9M,aAAa,CAAC5B,IAAI,CAACQ,KAAK,CAAC,iBACpDd,OAAA;YAAKmL,KAAK,EAAE;cAAElF,UAAU,EAAE;YAAO,CAAE;YAAA4J,QAAA,EAChCvP,IAAI,CAACiR,QAAQ,CAAClR,GAAG,CAAC,CAACmR,OAAO,EAAEC,QAAQ,kBACnCzR,OAAA;cAEEmL,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,aAAa,CAAC;gBAC1B8I,OAAO,EAAE,mBAAmB;gBAC5Bc,QAAQ,EAAE,MAAM;gBAChBC,OAAO,EAAE;cACX,CAAE;cACFb,OAAO,EAAEA,CAAA,KAAM;gBACbrO,YAAY,CAACvB,IAAI,CAACO,GAAG,CAAC;gBACtBoB,cAAc,CAAC,KAAK,CAAC;cACvB,CAAE;cACFkO,YAAY,EAAGrH,CAAC,IAAK;gBACnBA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGlH,YAAY,CAACiH,YAAY,CAACgB,OAAO;gBAC7DkD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACrG,KAAK,GAAG,OAAO;gBAC9BgE,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC4F,OAAO,GAAG,GAAG;cAC9B,CAAE;cACFX,YAAY,EAAGtH,CAAC,IAAK;gBACnBA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGlH,YAAY,CAACiH,YAAY,CAACU,OAAO;gBAC7DwD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACrG,KAAK,GAAGnH,YAAY,CAACiH,YAAY,CAACG,IAAI;gBACrD+D,CAAC,CAACE,MAAM,CAACmC,KAAK,CAAC4F,OAAO,GAAG,KAAK;cAChC,CAAE;cAAAlB,QAAA,EAED2B,OAAO,CAAC1Q;YAAK,GAtBT2Q,QAAQ;cAAAjR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,GArEO2Q,KAAK;UAAA9Q,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRX,OAAA;MAAMmL,KAAK,EAAEjE,QAAQ,CAAC,aAAa,CAAE;MAAA2I,QAAA,GAElC7N,WAAW,IAAIsI,MAAM,CAACoH,UAAU,GAAG,GAAG,iBACrC1R,OAAA;QACEmL,KAAK,EAAE;UACLkE,QAAQ,EAAE,OAAO;UACjBsC,GAAG,EAAE,MAAM;UACXC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTnN,eAAe,EAAE,iBAAiB;UAClCoN,MAAM,EAAE;QACV,CAAE;QACF7B,OAAO,EAAEA,CAAA,KAAMjO,cAAc,CAAC,KAAK;MAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACF,EAGAiB,SAAS,KAAK,WAAW,iBACxB5B,OAAA;QAAKmL,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B7P,OAAA;UAAKmL,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3B7P,OAAA;YAAKmL,KAAK,EAAE;cAAEoF,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEQ,GAAG,EAAE,MAAM;cAAEgB,YAAY,EAAE;YAAO,CAAE;YAAAnC,QAAA,gBACvF7P,OAAA;cAAKmL,KAAK,EAAE;gBACV8F,KAAK,EAAE,MAAM;gBACbL,MAAM,EAAE,MAAM;gBACdX,YAAY,EAAE,KAAK;gBACnBpL,UAAU,EAAE,SAAS;gBACrBC,KAAK,EAAE,SAAS;gBAChByL,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBU,cAAc,EAAE,QAAQ;gBACxBJ,QAAQ,EAAE,MAAM;gBAChBD,UAAU,EAAE;cACd,CAAE;cAAAhB,QAAA,EACCzN,IAAI,GAAGA,IAAI,CAACgP,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,gBAAGrR,OAAA,CAAC3B,MAAM;gBAACgS,IAAI,EAAE;cAAG;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNX,OAAA;cAAA6P,QAAA,gBACE7P,OAAA;gBAAImL,KAAK,EAAE;kBACT8G,MAAM,EAAE,CAAC;kBACTnB,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAE,GAAG;kBACf/L,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG;gBACnC,CAAE;gBAAA8K,QAAA,EACCzN,IAAI,GAAG,GAAGA,IAAI,CAACgP,KAAK,cAAc,GAAG;cAAgB;gBAAA5Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACLX,OAAA;gBAAGmL,KAAK,EAAE;kBACR8G,MAAM,EAAE,SAAS;kBACjBlB,OAAO,EAAE,GAAG;kBACZjM,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACsN;gBACnC,CAAE;gBAAArC,QAAA,EAAC;cAEH;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENX,OAAA;YAAKmL,KAAK,EAAE;cAAE6G,YAAY,EAAE;YAAO,CAAE;YAAAnC,QAAA,gBACnC7P,OAAA;cAAImL,KAAK,EAAE;gBACT6G,YAAY,EAAE,MAAM;gBACpBlN,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG;cACnC,CAAE;cAAA8K,QAAA,EAAC;YAAe;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBX,OAAA;cAAKmL,KAAK,EAAE;gBAAEyF,MAAM,EAAE;cAAQ,CAAE;cAAAf,QAAA,eAC9B7P,OAAA,CAACT,GAAG;gBAACwI,IAAI,EAAE2G,SAAU;gBAACyD,OAAO,EAAElD;cAAa;gBAAAzO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENX,OAAA;YAAKmL,KAAK,EAAE;cAAE6G,YAAY,EAAE;YAAO,CAAE;YAAAnC,QAAA,gBACnC7P,OAAA;cAAImL,KAAK,EAAE;gBACT6G,YAAY,EAAE,MAAM;gBACpBlN,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG;cACnC,CAAE;cAAA8K,QAAA,EAAC;YAAe;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBX,OAAA;cAAKmL,KAAK,EAAE;gBACViH,SAAS,EAAE,OAAO;gBAClBC,SAAS,EAAE,MAAM;gBACjB1N,eAAe,EAAE,SAAS;gBAC1BQ,MAAM,EAAE,mBAAmB;gBAC3B8K,YAAY,EAAE,KAAK;gBACnBD,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EACC1M,WAAW,CAACkH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChK,GAAG,CAAC,CAACoH,GAAG,EAAE6J,KAAK,kBACtCtR,OAAA;gBAAiBmL,KAAK,EAAE;kBACtB6E,OAAO,EAAE,OAAO;kBAChB9K,YAAY,EAAE,gBAAgB;kBAC9BqL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBQ,GAAG,EAAE;gBACP,CAAE;gBAAAnB,QAAA,gBACA7P,OAAA;kBAAKmL,KAAK,EAAE;oBACV8F,KAAK,EAAE,KAAK;oBACZL,MAAM,EAAE,KAAK;oBACbX,YAAY,EAAE,KAAK;oBACnBtL,eAAe,EAAE8C,GAAG,CAAC8F,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG;kBACtD;gBAAE;kBAAA/M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLX,OAAA;kBAAKmL,KAAK,EAAE;oBAAEmF,IAAI,EAAE;kBAAE,CAAE;kBAAAT,QAAA,gBACtB7P,OAAA;oBAAKmL,KAAK,EAAE;sBACV2F,QAAQ,EAAE,MAAM;sBAChBhM,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACG;oBACnC,CAAE;oBAAA8K,QAAA,EAAEpI,GAAG,CAAC6F;kBAAG;oBAAA9M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBX,OAAA;oBAAKmL,KAAK,EAAE;sBACV2F,QAAQ,EAAE,MAAM;sBAChBC,OAAO,EAAE,GAAG;sBACZjM,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACsN;oBACnC,CAAE;oBAAArC,QAAA,EACC,IAAInC,IAAI,CAACjG,GAAG,CAACgG,IAAI,CAAC,CAAC6E,cAAc,CAAC;kBAAC;oBAAA9R,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAzBE2Q,KAAK;gBAAA9Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENX,OAAA;YAAA6P,QAAA,gBACE7P,OAAA;cAAImL,KAAK,EAAE;gBACT6G,YAAY,EAAE,MAAM;gBACpBlN,KAAK,EAAE;cACT,CAAE;cAAA+K,QAAA,EAAC;YAAiB;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBX,OAAA;cAAKmL,KAAK,EAAE;gBACVoF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,MAAM;gBACXuB,QAAQ,EAAE;cACZ,CAAE;cAAA1C,QAAA,gBACA7P,OAAA;gBAAOmL,KAAK,EAAE;kBACZ,GAAGjE,QAAQ,CAAC,eAAe,CAAC;kBAC5BrC,UAAU,EAAE,SAAS;kBACrBC,KAAK,EAAE,MAAM;kBACbK,MAAM,EAAE,gBAAgB;kBACxB4K,MAAM,EAAEzN,mBAAmB,GAAG,aAAa,GAAG;gBAChD,CAAE;gBAAAuN,QAAA,gBACA7P,OAAA,CAACxB,QAAQ;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACX2B,mBAAmB,GAAG,cAAc,GAAG,eAAe,eACvDtC,OAAA;kBACEuN,IAAI,EAAC,MAAM;kBACXiF,MAAM,EAAC,iBAAiB;kBACxBC,QAAQ,EAAE5J,kBAAmB;kBAC7B6J,QAAQ,EAAEpQ,mBAAoB;kBAC9B6I,KAAK,EAAE;oBAAEoF,OAAO,EAAE;kBAAO;gBAAE;kBAAA/P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,EAEP6B,SAAS,iBACRxC,OAAA;gBACEwK,IAAI,EAAEhI,SAAU;gBAChBwG,MAAM,EAAC,QAAQ;gBACf2J,GAAG,EAAC,qBAAqB;gBACzBxH,KAAK,EAAE;kBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;kBAC5BrC,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,mBAAmB;kBAC3BL,KAAK,EAAE,SAAS;kBAChB8N,cAAc,EAAE;gBAClB,CAAE;gBAAA/C,QAAA,EACH;cAED;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,QAAQ,iBACrB5B,OAAA;QAAKmL,KAAK,EAAE;UAAE6E,OAAO,EAAE,MAAM;UAAE6C,QAAQ,EAAE,QAAQ;UAAEZ,MAAM,EAAE;QAAS,CAAE;QAAApC,QAAA,eACpE7P,OAAA;UAAKmL,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3B7P,OAAA;YAAImL,KAAK,EAAE;cACT2H,SAAS,EAAE,CAAC;cACZhO,KAAK,EAAE;YACT,CAAE;YAAA+K,QAAA,EAAC;UAAgB;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBX,OAAA;YAAGmL,KAAK,EAAE;cACR4F,OAAO,EAAE,GAAG;cACZiB,YAAY,EAAE,MAAM;cACpBlN,KAAK,EAAE;YACT,CAAE;YAAA+K,QAAA,EAAC;UAEH;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJX,OAAA;YAAKmL,KAAK,EAAE;cACVyF,MAAM,EAAE,MAAM;cACdyB,SAAS,EAAE,MAAM;cACjBL,YAAY,EAAE,MAAM;cACpBhC,OAAO,EAAE,MAAM;cACfrL,eAAe,EAAE,SAAS;cAC1BQ,MAAM,EAAE,mBAAmB;cAC3B8K,YAAY,EAAE;YAChB,CAAE;YAAAJ,QAAA,GAECzO,QAAQ,CAAC4N,MAAM,KAAK,CAAC,gBACpBhP,OAAA;cAAKmL,KAAK,EAAE;gBACVyF,MAAM,EAAE,MAAM;gBACdL,OAAO,EAAE,MAAM;gBACfwC,aAAa,EAAE,QAAQ;gBACvBvC,UAAU,EAAE,QAAQ;gBACpBU,cAAc,EAAE,QAAQ;gBACxB8B,SAAS,EAAE,QAAQ;gBACnBjC,OAAO,EAAE;cACX,CAAE;cAAAlB,QAAA,gBACA7P,OAAA;gBAAKmL,KAAK,EAAE;kBAAE2F,QAAQ,EAAE,MAAM;kBAAEkB,YAAY,EAAE;gBAAO,CAAE;gBAAAnC,QAAA,EAAC;cAAE;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEX,OAAA;gBAAImL,KAAK,EAAE;kBACT8G,MAAM,EAAE,CAAC;kBACTnN,KAAK,EAAE;gBACT,CAAE;gBAAA+K,QAAA,EAAC;cAAoB;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BX,OAAA;gBAAGmL,KAAK,EAAE;kBACRrG,KAAK,EAAE;gBACT,CAAE;gBAAA+K,QAAA,EAAC;cAA+C;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,GAENS,QAAQ,CAACf,GAAG,CAAC,CAACiN,GAAG,EAAE2F,GAAG,kBACpBjT,OAAA;cAEEmL,KAAK,EAAE;gBACL,IAAImC,GAAG,CAAClB,IAAI,KAAK,MAAM,GAAGlF,QAAQ,CAAC,gBAAgB,CAAC,GAAGA,QAAQ,CAAC,eAAe,CAAC,CAAC;gBACjFgM,SAAS,EAAE;cACb,CAAE;cAAArD,QAAA,EAEDvC,GAAG,CAAClB,IAAI,KAAK,KAAK,gBACjBpM,OAAA,CAACF,aAAa;gBAAA+P,QAAA,EAAEvC,GAAG,CAACjB;cAAO;gBAAA7L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgB,CAAC,GAE5C2M,GAAG,CAACjB;YACL,GAVI4G,GAAG;cAAAzS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWL,CACN,CACF,EACAa,OAAO,iBACNxB,OAAA;cAAKmL,KAAK,EAAEjE,QAAQ,CAAC,eAAe,CAAE;cAAA2I,QAAA,eACpC7P,OAAA;gBAAKmL,KAAK,EAAE;kBAAEoF,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEQ,GAAG,EAAE;gBAAM,CAAE;gBAAAnB,QAAA,gBAChE7P,OAAA;kBAAKmL,KAAK,EAAE;oBACV8F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBtL,eAAe,EAAE,SAAS;oBAC1BuO,SAAS,EAAE;kBACb;gBAAE;kBAAA1S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLX,OAAA;kBAAKmL,KAAK,EAAE;oBACV8F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBtL,eAAe,EAAE,SAAS;oBAC1BuO,SAAS,EAAE,iCAAiC;oBAC5CC,cAAc,EAAE;kBAClB;gBAAE;kBAAA3S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACLX,OAAA;kBAAKmL,KAAK,EAAE;oBACV8F,KAAK,EAAE,MAAM;oBACbL,MAAM,EAAE,MAAM;oBACdX,YAAY,EAAE,KAAK;oBACnBtL,eAAe,EAAE,SAAS;oBAC1BuO,SAAS,EAAE,iCAAiC;oBAC5CC,cAAc,EAAE;kBAClB;gBAAE;kBAAA3S,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eACDX,OAAA;cAAKoT,GAAG,EAAE/P;YAAW;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAGNX,OAAA;YACEmL,KAAK,EAAE;cAAEoF,OAAO,EAAE,MAAM;cAAES,GAAG,EAAE;YAAO,CAAE;YACxCqC,QAAQ,EAAEvK,CAAC,IAAI;cACbA,CAAC,CAACwK,cAAc,CAAC,CAAC;cAClBrH,WAAW,CAAC,CAAC;YACf,CAAE;YAAA4D,QAAA,gBAEF7P,OAAA;cACEuN,IAAI,EAAC,MAAM;cACXgG,WAAW,EAAC,sBAAsB;cAClCpI,KAAK,EAAEjE,QAAQ,CAAC,YAAY,CAAE;cAC9BsM,KAAK,EAAEtS,KAAM;cACbuR,QAAQ,EAAE3J,CAAC,IAAI3H,QAAQ,CAAC2H,CAAC,CAACE,MAAM,CAACwK,KAAK,CAAE;cACxCd,QAAQ,EAAElR;YAAQ;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFX,OAAA;cACEuN,IAAI,EAAC,QAAQ;cACbpC,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BuM,QAAQ,EAAE;cACZ,CAAE;cACFf,QAAQ,EAAElR,OAAO,IAAI,CAACN,KAAK,CAACgL,IAAI,CAAC,CAAE;cAAA2D,QAAA,EAElCrO,OAAO,GAAG,YAAY,GAAG;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,KAAK,iBAClB5B,OAAA;QAAKmL,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B7P,OAAA;UAAKmL,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAE3B7P,OAAA;YAAKmL,KAAK,EAAE;cAAEoF,OAAO,EAAE,MAAM;cAAEW,cAAc,EAAE,eAAe;cAAEV,UAAU,EAAE,QAAQ;cAAEwB,YAAY,EAAE;YAAO,CAAE;YAAAnC,QAAA,gBAC3G7P,OAAA;cAAA6P,QAAA,gBACE7P,OAAA;gBAAImL,KAAK,EAAE;kBAAE2H,SAAS,EAAE,CAAC;kBAAEd,YAAY,EAAE;gBAAM,CAAE;gBAAAnC,QAAA,EAAC;cAA6B;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFX,OAAA;gBAAGmL,KAAK,EAAE;kBAAE4F,OAAO,EAAE,GAAG;kBAAEkB,MAAM,EAAE;gBAAE,CAAE;gBAAApC,QAAA,EAAC;cAEvC;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACLmD,gBAAgB,iBACf9D,OAAA;cACEkQ,OAAO,EAAEpF,mBAAoB;cAC7BK,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE,SAAS;gBACrB0L,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,KAAK;gBACVF,QAAQ,EAAE,MAAM;gBAChBd,OAAO,EAAE,UAAU;gBACnB7K,MAAM,EAAE,MAAM;gBACd8K,YAAY,EAAE,KAAK;gBACnBnL,KAAK,EAAE,OAAO;gBACdiL,MAAM,EAAE,SAAS;gBACjBpK,UAAU,EAAE;cACd,CAAE;cACFwK,YAAY,EAAGrH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,SAAU;cAC3DuL,YAAY,EAAGtH,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,SAAU;cAAAgL,QAAA,gBAE3D7P,OAAA,CAACZ,WAAW;gBAACiR,IAAI,EAAE;cAAG;gBAAA7P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAE3B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNX,OAAA;YAAKmL,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,KAAK;cACVgB,YAAY,EAAE,MAAM;cACpBO,QAAQ,EAAE,MAAM;cAChBrN,YAAY,EAAE,gBAAgB;cAC9BwO,aAAa,EAAE;YACjB,CAAE;YAAA7D,QAAA,EACC,CAAC,KAAK,EAAE,GAAG8D,MAAM,CAACC,IAAI,CAACxP,iBAAiB,CAAC,CAAC,CAAC/D,GAAG,CAACwT,QAAQ,iBACtD7T,OAAA;cAEEkQ,OAAO,EAAEA,CAAA,KAAM3M,mBAAmB,CAACsQ,QAAQ,CAAE;cAC7C1I,KAAK,EAAE;gBACL6E,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpB9K,MAAM,EAAE7B,gBAAgB,KAAKuQ,QAAQ,GACjC,MAAM,GACN,gBAAgB;gBACpBhP,UAAU,EAAEvB,gBAAgB,KAAKuQ,QAAQ,GACrClW,YAAY,CAACiH,YAAY,CAACgB,OAAO,GACjC,aAAa;gBACjBd,KAAK,EAAExB,gBAAgB,KAAKuQ,QAAQ,GAChC,OAAO,GACP,MAAM;gBACV9D,MAAM,EAAE,SAAS;gBACjBe,QAAQ,EAAE,MAAM;gBAChBD,UAAU,EAAEvN,gBAAgB,KAAKuQ,QAAQ,GAAG,GAAG,GAAG,GAAG;gBACrDlO,UAAU,EAAE,eAAe;gBAC3BmO,aAAa,EAAE;cACjB,CAAE;cACF3D,YAAY,EAAGrH,CAAC,IAAK;gBACnB,IAAIxF,gBAAgB,KAAKuQ,QAAQ,EAAE;kBACjC/K,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,SAAS;gBACvC;cACF,CAAE;cACFuL,YAAY,EAAGtH,CAAC,IAAK;gBACnB,IAAIxF,gBAAgB,KAAKuQ,QAAQ,EAAE;kBACjC/K,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAG,aAAa;gBAC3C;cACF,CAAE;cAAAgL,QAAA,EAEDgE,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,GAAGA,QAAQ,KAAK,OAAO,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAGA,QAAQ,KAAK,SAAS,GAAG,IAAI,GAAGA,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,IAAIA,QAAQ;YAAE,GA/BlNA,QAAQ;cAAArT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNX,OAAA;YAAKmL,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,MAAM;cACXgB,YAAY,EAAE,MAAM;cACpBO,QAAQ,EAAE,MAAM;cAChB/B,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,gBAEA7P,OAAA;cAAKmL,KAAK,EAAE;gBAAEkE,QAAQ,EAAE,UAAU;gBAAEiB,IAAI,EAAE,CAAC;gBAAEmD,QAAQ,EAAE;cAAQ,CAAE;cAAA5D,QAAA,gBAC/D7P,OAAA;gBAAKmL,KAAK,EAAE;kBACVkE,QAAQ,EAAE,UAAU;kBACpBuC,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVnM,SAAS,EAAE,kBAAkB;kBAC7BV,KAAK,EAAE;gBACT,CAAE;gBAAA+K,QAAA,eACA7P,OAAA,CAACzB,QAAQ;kBAAC8R,IAAI,EAAE;gBAAG;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACNX,OAAA;gBACEuN,IAAI,EAAC,MAAM;gBACXgG,WAAW,EAAC,qBAAqB;gBACjCpI,KAAK,EAAE;kBACL,GAAGjE,QAAQ,CAAC,YAAY,CAAC;kBACzB6M,WAAW,EAAE,MAAM;kBACnB9C,KAAK,EAAE;gBACT,CAAE;gBACFuC,KAAK,EAAE1R,UAAW;gBAClB2Q,QAAQ,EAAG3J,CAAC,IAAK/G,aAAa,CAAC+G,CAAC,CAACE,MAAM,CAACwK,KAAK;cAAE;gBAAAhT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDmB,UAAU,iBACT9B,OAAA;gBACEmL,KAAK,EAAE;kBACLkE,QAAQ,EAAE,UAAU;kBACpBwC,KAAK,EAAE,MAAM;kBACbF,GAAG,EAAE,KAAK;kBACVnM,SAAS,EAAE,kBAAkB;kBAC7BX,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,MAAM;kBACdL,KAAK,EAAE,MAAM;kBACbiL,MAAM,EAAE;gBACV,CAAE;gBACFG,OAAO,EAAEA,CAAA,KAAMnO,aAAa,CAAC,EAAE,CAAE;gBAAA8N,QAAA,eAEjC7P,OAAA,CAACnC,GAAG;kBAACwS,IAAI,EAAE;gBAAG;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNX,OAAA;cACEwT,KAAK,EAAEhQ,MAAO;cACdiP,QAAQ,EAAG3J,CAAC,IAAKrF,SAAS,CAACqF,CAAC,CAACE,MAAM,CAACwK,KAAK,CAAE;cAC3CrI,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,YAAY,CAAC;gBACzB+J,KAAK,EAAE,MAAM;gBACbwC,QAAQ,EAAE;cACZ,CAAE;cAAA5D,QAAA,gBAEF7P,OAAA;gBAAQwT,KAAK,EAAC,MAAM;gBAAA3D,QAAA,EAAC;cAAe;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7CX,OAAA;gBAAQwT,KAAK,EAAC,WAAW;gBAAA3D,QAAA,EAAC;cAAiB;gBAAArP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLiD,eAAe,CAACoL,MAAM,GAAG,CAAC,iBACzBhP,OAAA;YAAKmL,KAAK,EAAE;cACV6G,YAAY,EAAE,MAAM;cACpBhC,OAAO,EAAE,MAAM;cACfC,YAAY,EAAE,MAAM;cACpBpL,UAAU,EAAE,SAAS;cACrBM,MAAM,EAAE;YACV,CAAE;YAAA0K,QAAA,gBACA7P,OAAA;cAAImL,KAAK,EAAE;gBACToF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBQ,GAAG,EAAE,KAAK;gBACVF,QAAQ,EAAE,MAAM;gBAChBkB,YAAY,EAAE,MAAM;gBACpBlN,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG,MAAM;gBACpC/B,MAAM,EAAE;cACV,CAAE;cAAApC,QAAA,gBACA7P,OAAA,CAACf,OAAO;gBAAC6F,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;cAAO;gBAAAxT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBACnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAKmL,KAAK,EAAE;gBACVoF,OAAO,EAAE,MAAM;gBACfS,GAAG,EAAE,KAAK;gBACVuB,QAAQ,EAAE;cACZ,CAAE;cAAA1C,QAAA,EACCjM,eAAe,CAACvD,GAAG,CAAC2J,OAAO,iBAC1BhK,OAAA;gBAEEkQ,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAACC,OAAO,CAAE;gBAC3CmB,KAAK,EAAE;kBACL6E,OAAO,EAAE,UAAU;kBACnBC,YAAY,EAAE,MAAM;kBACpB9K,MAAM,EAAE,aAAaxH,YAAY,CAACiH,YAAY,CAACgB,OAAO,EAAE;kBACxDf,UAAU,EAAEmP,QAAQ,GAAG,SAAS,GAAG,aAAa;kBAChDlP,KAAK,EAAEnH,YAAY,CAACiH,YAAY,CAACgB,OAAO;kBACxCmK,MAAM,EAAE,SAAS;kBACjBe,QAAQ,EAAE,MAAM;kBAChBnL,UAAU,EAAE;gBACd,CAAE;gBACFwK,YAAY,EAAGrH,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGlH,YAAY,CAACiH,YAAY,CAACgB,OAAO;kBAC7DkD,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACrG,KAAK,GAAG,OAAO;gBAChC,CAAE;gBACFsL,YAAY,EAAGtH,CAAC,IAAK;kBACnBA,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACtG,UAAU,GAAGmP,QAAQ,GAAG,SAAS,GAAG,aAAa;kBAChElL,CAAC,CAACE,MAAM,CAACmC,KAAK,CAACrG,KAAK,GAAGnH,YAAY,CAACiH,YAAY,CAACgB,OAAO;gBAC1D,CAAE;gBAAAiK,QAAA,EAED7F;cAAO,GArBHA,OAAO;gBAAAxJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBN,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDX,OAAA;YAAKmL,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACf0D,mBAAmB,EAAE,uCAAuC;cAC5DjD,GAAG,EAAE,MAAM;cACX8B,SAAS,EAAE;YACb,CAAE;YAAAjD,QAAA,EACCzE,oBAAoB,CAAC,CAAC,CAAC/K,GAAG,CAAC,CAAC2J,OAAO,EAAEsH,KAAK,kBACzCtR,OAAA;cAEEmL,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,aAAa,CAAC;gBAC1BmI,QAAQ,EAAE,UAAU;gBACpB7J,SAAS,EAAE9B,iBAAiB,CAACmH,QAAQ,CAACb,OAAO,CAAC,GAAG,aAAa,GAAG,UAAU;gBAC3E7E,MAAM,EAAEzB,iBAAiB,CAACmH,QAAQ,CAACb,OAAO,CAAC,GACvC,aAAarM,YAAY,CAACiH,YAAY,CAACgB,OAAO,EAAE,GAChD,aAAaoO,QAAQ,GAAG,MAAM,GAAGrW,YAAY,CAACiH,YAAY,CAACO,MAAM,EAAE;gBACvEN,UAAU,EAAEmP,QAAQ,GAAG,SAAS,GAAGrW,YAAY,CAACiH,YAAY,CAACU,OAAO;gBACpER,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAGrW,YAAY,CAACiH,YAAY,CAACG,IAAI;gBAC5DmO,SAAS,EAAE,oBAAoB5B,KAAK,GAAG,GAAG,QAAQ;gBAClDjL,SAAS,EAAE2N,QAAQ,GACf,8BAA8B,GAC9B,aAAarW,YAAY,CAACiH,YAAY,CAAC0B,MAAM;cACnD,CAAE;cACF4J,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAACC,OAAO,CAAE;cAAA6F,QAAA,gBAG3C7P,OAAA;gBACEkQ,OAAO,EAAGpH,CAAC,IAAK6B,cAAc,CAACX,OAAO,EAAElB,CAAC,CAAE;gBAC3CqC,KAAK,EAAE;kBACLkE,QAAQ,EAAE,UAAU;kBACpBsC,GAAG,EAAE,KAAK;kBACVE,KAAK,EAAE,KAAK;kBACZhN,UAAU,EAAE,MAAM;kBAClBM,MAAM,EAAE,MAAM;kBACd4K,MAAM,EAAE,SAAS;kBACjBjL,KAAK,EAAEpB,iBAAiB,CAACmH,QAAQ,CAACb,OAAO,CAAC,GAAG,SAAS,GAAG,MAAM;kBAC/DrE,UAAU,EAAE,eAAe;kBAC3BmL,QAAQ,EAAE;gBACZ,CAAE;gBAAAjB,QAAA,eAEF7P,OAAA,CAAChB,OAAO;kBAACkV,IAAI,EAAExQ,iBAAiB,CAACmH,QAAQ,CAACb,OAAO,CAAC,GAAG,cAAc,GAAG;gBAAO;kBAAAxJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eAGTX,OAAA;gBAAKmL,KAAK,EAAE;kBACV8F,KAAK,EAAE,MAAM;kBACbL,MAAM,EAAE,MAAM;kBACdX,YAAY,EAAE,KAAK;kBACnBpL,UAAU,EAAE,2BAA2BlH,YAAY,CAACiH,YAAY,CAACgB,OAAO,KAAKjI,YAAY,CAACiH,YAAY,CAACuP,WAAW,GAAG;kBACrHrP,KAAK,EAAE,OAAO;kBACdyL,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBU,cAAc,EAAE,QAAQ;kBACxBJ,QAAQ,EAAE,MAAM;kBAChBD,UAAU,EAAE,GAAG;kBACfmB,YAAY,EAAE,MAAM;kBACpB3L,SAAS,EAAE,aAAa1I,YAAY,CAACiH,YAAY,CAAC0B,MAAM;gBAC1D,CAAE;gBAAAuJ,QAAA,EACC7F,OAAO,CAACoK,MAAM,CAAC,CAAC;cAAC;gBAAA5T,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGNX,OAAA;gBAAKmL,KAAK,EAAE;kBACV0F,UAAU,EAAE,GAAG;kBACfmC,SAAS,EAAE,QAAQ;kBACnBlC,QAAQ,EAAE,MAAM;kBAChBkB,YAAY,EAAE;gBAChB,CAAE;gBAAAnC,QAAA,EACC7F;cAAO;gBAAAxJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNX,OAAA;gBAAKmL,KAAK,EAAE;kBACVoF,OAAO,EAAE,MAAM;kBACfW,cAAc,EAAE,eAAe;kBAC/BJ,QAAQ,EAAE,MAAM;kBAChBC,OAAO,EAAE,GAAG;kBACZ+B,SAAS,EAAE;gBACb,CAAE;gBAAAjD,QAAA,gBACA7P,OAAA;kBAAA6P,QAAA,GAAM,eAAG,EAACwE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAC,YAAU;gBAAA;kBAAA/T,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DX,OAAA;kBAAA6P,QAAA,GAAM,SAAE,EAAC,CAACwE,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAhU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EAGLgT,MAAM,CAACc,OAAO,CAACrQ,iBAAiB,CAAC,CAAC/D,GAAG,CAAC,CAAC,CAACwT,QAAQ,EAAExP,SAAS,CAAC,KAAK;gBAChE,IAAIA,SAAS,CAACiH,IAAI,CAAClB,CAAC,IAAIA,CAAC,CAACrJ,WAAW,CAAC,CAAC,KAAKiJ,OAAO,CAACjJ,WAAW,CAAC,CAAC,CAAC,EAAE;kBAClE,oBACEf,OAAA;oBAEEmL,KAAK,EAAE;sBACLkE,QAAQ,EAAE,UAAU;sBACpBsC,GAAG,EAAE,KAAK;sBACVC,IAAI,EAAE,KAAK;sBACX/M,UAAU,EAAElH,YAAY,CAACiH,YAAY,CAACgB,OAAO;sBAC7Cd,KAAK,EAAE,OAAO;sBACdkL,OAAO,EAAE,SAAS;sBAClBC,YAAY,EAAE,KAAK;sBACnBa,QAAQ,EAAE,MAAM;sBAChBD,UAAU,EAAE;oBACd,CAAE;oBAAAhB,QAAA,EAEDgE;kBAAQ,GAbJA,QAAQ;oBAAArT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CAAC;gBAEV;gBACA,OAAO,IAAI;cACb,CAAC,CAAC;YAAA,GAlGG2Q,KAAK;cAAA9Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmGP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLyK,oBAAoB,CAAC,CAAC,CAAC4D,MAAM,KAAK,CAAC,iBAClChP,OAAA;YAAKmL,KAAK,EAAE;cACV6H,SAAS,EAAE,QAAQ;cACnBhD,OAAO,EAAE,MAAM;cACfe,OAAO,EAAE,GAAG;cACZjM,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;YAChC,CAAE;YAAAnE,QAAA,gBACA7P,OAAA;cAAKmL,KAAK,EAAE;gBAAE2F,QAAQ,EAAE,MAAM;gBAAEkB,YAAY,EAAE;cAAO,CAAE;cAAAnC,QAAA,EAAC;YAAE;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEX,OAAA;cAAImL,KAAK,EAAE;gBAAErG,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;cAAO,CAAE;cAAAnE,QAAA,EAAC;YAAkB;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EX,OAAA;cAAGmL,KAAK,EAAE;gBAAErG,KAAK,EAAEkP,QAAQ,GAAG,MAAM,GAAG;cAAO,CAAE;cAAAnE,QAAA,EAAC;YAA4C;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,SAAS,iBACtB5B,OAAA;QAAKmL,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B7P,OAAA;UAAKmL,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3B7P,OAAA;YAAImL,KAAK,EAAE;cAAE2H,SAAS,EAAE;YAAE,CAAE;YAAAjD,QAAA,EAAC;UAAc;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDX,OAAA;YAAGmL,KAAK,EAAE;cAAE4F,OAAO,EAAE,GAAG;cAAEiB,YAAY,EAAE;YAAO,CAAE;YAAAnC,QAAA,EAAC;UAElD;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJX,OAAA;YAAKmL,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACf0D,mBAAmB,EAAE,uCAAuC;cAC5DjD,GAAG,EAAE;YACP,CAAE;YAAAnB,QAAA,EACCvL,WAAW,CAACjE,GAAG,CAAC,CAACqU,IAAI,EAAEpD,KAAK,kBAC3BtR,OAAA;cAEEmL,KAAK,EAAEjE,QAAQ,CAAC,UAAU,CAAE;cAC5BgJ,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC4I,IAAI,CAAClQ,IAAI,CAAE;cAAAqL,QAAA,gBAEvC7P,OAAA;gBAAA6P,QAAA,gBACE7P,OAAA;kBAAImL,KAAK,EAAE;oBAAE8G,MAAM,EAAE;kBAAY,CAAE;kBAAApC,QAAA,EAAE6E,IAAI,CAAC5T;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDX,OAAA;kBAAGmL,KAAK,EAAE;oBACR8G,MAAM,EAAE,CAAC;oBACTnB,QAAQ,EAAE,MAAM;oBAChBC,OAAO,EAAE;kBACX,CAAE;kBAAAlB,QAAA,EACC6E,IAAI,CAACnQ;gBAAW;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNX,OAAA;gBAAKmL,KAAK,EAAE;kBAAErG,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;gBAAU,CAAE;gBAAAnE,QAAA,eACtD7P,OAAA,CAACjB,cAAc;kBAACsR,IAAI,EAAE;gBAAG;kBAAA7P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA,GAhBD2Q,KAAK;cAAA9Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAiB,SAAS,KAAK,QAAQ,iBACrB5B,OAAA;QAAKmL,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B7P,OAAA,CAAC3C,MAAM;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN,EACAiB,SAAS,KAAK,WAAW,iBACxB5B,OAAA;QAAKmL,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B7P,OAAA;UAAKmL,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3B7P,OAAA;YAAImL,KAAK,EAAE;cACT2H,SAAS,EAAE,CAAC;cACZhO,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;YAChC,CAAE;YAAAnE,QAAA,EAAC;UAAS;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBX,OAAA;YAAGmL,KAAK,EAAE;cACR4F,OAAO,EAAE,GAAG;cACZiB,YAAY,EAAE,MAAM;cACpBlN,KAAK,EAAEkP,QAAQ,GAAG,MAAM,GAAG;YAC7B,CAAE;YAAAnE,QAAA,EAAC;UAEH;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJX,OAAA;YAAKmL,KAAK,EAAE;cAAE6G,YAAY,EAAE;YAAO,CAAE;YAAAnC,QAAA,eACnC7P,OAAA;cAAOmL,KAAK,EAAE;gBACZ,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAEmP,QAAQ,GAAG,SAAS,GAAG,SAAS;gBAC5ClP,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG,MAAM;gBACpC7O,MAAM,EAAE,aAAa6O,QAAQ,GAAG,MAAM,GAAG,MAAM,EAAE;gBACjDjE,MAAM,EAAErN,qBAAqB,GAAG,aAAa,GAAG;cAClD,CAAE;cAAAmN,QAAA,gBACA7P,OAAA,CAACxB,QAAQ;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACX+B,qBAAqB,GAAG,cAAc,GAAG,iBAAiB,eAC3D1C,OAAA;gBACEuN,IAAI,EAAC,MAAM;gBACXiF,MAAM,EAAC,sBAAsB;gBAC7BC,QAAQ,EAAE3I,oBAAqB;gBAC/B4I,QAAQ,EAAEhQ,qBAAsB;gBAChCyI,KAAK,EAAE;kBAAEoF,OAAO,EAAE;gBAAO;cAAE;gBAAA/P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENX,OAAA;YAAA6P,QAAA,gBACE7P,OAAA;cAAImL,KAAK,EAAE;gBACT6G,YAAY,EAAE,MAAM;gBACpBlN,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;cAChC,CAAE;cAAAnE,QAAA,EAAC;YAAc;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBiC,aAAa,CAACoM,MAAM,KAAK,CAAC,gBACzBhP,OAAA;cAAGmL,KAAK,EAAE;gBACR4F,OAAO,EAAE,GAAG;gBACZjM,KAAK,EAAEkP,QAAQ,GAAG,MAAM,GAAG;cAC7B,CAAE;cAAAnE,QAAA,EAAC;YAAyB;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAEhCX,OAAA;cAAKmL,KAAK,EAAE;gBACVxG,eAAe,EAAEqP,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjD7O,MAAM,EAAE,aAAa6O,QAAQ,GAAG,MAAM,GAAG,SAAS,EAAE;gBACpD/D,YAAY,EAAE,KAAK;gBACnBD,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EACCjN,aAAa,CAACvC,GAAG,CAAC,CAAC0I,IAAI,EAAEkK,GAAG,KAAK;gBAChC,MAAM;kBAAElL,IAAI,EAAE0B;gBAAQ,CAAC,GAAGtF,QAAQ,CAACkF,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAACI,YAAY,CAAC,GAAGtH,IAAI,CAAC+G,EAAE,IAAIJ,IAAI,CAACK,IAAI,EAAE,CAAC;gBACpG,oBACEpJ,OAAA;kBAAemL,KAAK,EAAE;oBACpB6E,OAAO,EAAE,MAAM;oBACf9K,YAAY,EAAE8O,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;oBAC5DzD,OAAO,EAAE,MAAM;oBACfW,cAAc,EAAE,eAAe;oBAC/BV,UAAU,EAAE;kBACd,CAAE;kBAAAX,QAAA,gBACA7P,OAAA;oBAAMmL,KAAK,EAAE;sBACXrG,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;oBAChC,CAAE;oBAAAnE,QAAA,EAAE9G,IAAI,CAACK;kBAAI;oBAAA5I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrBX,OAAA;oBACEwK,IAAI,EAAEf,OAAO,CAACE,SAAU;oBACxBX,MAAM,EAAC,QAAQ;oBACf2J,GAAG,EAAC,qBAAqB;oBACzBxH,KAAK,EAAE;sBACLrG,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG,SAAS;sBACvCpB,cAAc,EAAE,MAAM;sBACtBrC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBQ,GAAG,EAAE;oBACP,CAAE;oBAAAnB,QAAA,gBAEF7P,OAAA,CAACjB,cAAc;sBAACsR,IAAI,EAAE;oBAAG;sBAAA7P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAE9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA,GAxBIsS,GAAG;kBAAAzS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBR,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EACAiB,SAAS,KAAK,WAAW,iBAAI5B,OAAA,CAAC5C,KAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACtCiB,SAAS,KAAK,KAAK,iBAAI5B,OAAA,CAAC7C,GAAG;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC9BiB,SAAS,KAAK,OAAO,IAAI,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgP,KAAK,MAAKvO,WAAW,iBACnD7C,OAAA;QAAKmL,KAAK,EAAE;UAAE6E,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC9B7P,OAAA;UAAKmL,KAAK,EAAEjE,QAAQ,CAAC,MAAM,CAAE;UAAA2I,QAAA,gBAC3B7P,OAAA;YAAImL,KAAK,EAAE;cACT2H,SAAS,EAAE,CAAC;cACZhO,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;YAChC,CAAE;YAAAnE,QAAA,EAAC;UAAW;YAAArP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBX,OAAA;YAAKmL,KAAK,EAAE;cACVoF,OAAO,EAAE,MAAM;cACfS,GAAG,EAAE,MAAM;cACXgB,YAAY,EAAE;YAChB,CAAE;YAAAnC,QAAA,gBACA7P,OAAA;cACEmL,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE9B,QAAQ,KAAK,OAAO,GAC9BpF,YAAY,CAACiH,YAAY,CAACgB,OAAO,GAAIoO,QAAQ,GAAG,SAAS,GAAG,aAAc;gBAC5ElP,KAAK,EAAE/B,QAAQ,KAAK,OAAO,GACzB,OAAO,GAAIiR,QAAQ,GAAG,SAAS,GAAG,MAAO;gBAC3C7O,MAAM,EAAE,aAAa6O,QAAQ,GAAG,MAAM,GAAG,MAAM;cACjD,CAAE;cACF9D,OAAO,EAAEA,CAAA,KAAMlN,WAAW,CAAC,OAAO,CAAE;cAAA6M,QAAA,EACrC;YAED;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTX,OAAA;cACEmL,KAAK,EAAE;gBACL,GAAGjE,QAAQ,CAAC,eAAe,CAAC;gBAC5BrC,UAAU,EAAE9B,QAAQ,KAAK,WAAW,GAClCpF,YAAY,CAACiH,YAAY,CAACgB,OAAO,GAAIoO,QAAQ,GAAG,SAAS,GAAG,aAAc;gBAC5ElP,KAAK,EAAE/B,QAAQ,KAAK,WAAW,GAC7B,OAAO,GAAIiR,QAAQ,GAAG,SAAS,GAAG,MAAO;gBAC3C7O,MAAM,EAAE,aAAa6O,QAAQ,GAAG,MAAM,GAAG,MAAM;cACjD,CAAE;cACF9D,OAAO,EAAEA,CAAA,KAAMlN,WAAW,CAAC,WAAW,CAAE;cAAA6M,QAAA,EACzC;YAED;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELoC,QAAQ,KAAK,OAAO,iBACnB/C,OAAA;YAAA6P,QAAA,gBACE7P,OAAA;cAAImL,KAAK,EAAE;gBACT6G,YAAY,EAAE,MAAM;gBACpBlN,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;cAChC,CAAE;cAAAnE,QAAA,EAAC;YAAS;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBX,OAAA;cAAKmL,KAAK,EAAE;gBACVxG,eAAe,EAAEqP,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjD7O,MAAM,EAAE,aAAa6O,QAAQ,GAAG,MAAM,GAAG,SAAS,EAAE;gBACpD/D,YAAY,EAAE,KAAK;gBACnBD,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EACC/M,QAAQ,CAACzC,GAAG,CAAC,CAAC+B,IAAI,EAAE6Q,GAAG,kBACtBjT,OAAA;gBAAemL,KAAK,EAAE;kBACpB6E,OAAO,EAAE,MAAM;kBACf9K,YAAY,EAAE8O,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;kBAC5DlP,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;gBAChC,CAAE;gBAAAnE,QAAA,EACCzN,IAAI,CAACgP;cAAK,GALH6B,GAAG;gBAAAzS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAoC,QAAQ,KAAK,WAAW,iBACvB/C,OAAA;YAAA6P,QAAA,gBACE7P,OAAA;cAAImL,KAAK,EAAE;gBACT6G,YAAY,EAAE,MAAM;gBACpBlN,KAAK,EAAEkP,QAAQ,GAAG,SAAS,GAAG;cAChC,CAAE;cAAAnE,QAAA,EAAC;YAAa;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBX,OAAA;cAAGmL,KAAK,EAAE;gBACR4F,OAAO,EAAE,GAAG;gBACZjM,KAAK,EAAEkP,QAAQ,GAAG,MAAM,GAAG;cAC7B,CAAE;cAAAnE,QAAA,EAAC;YAA+B;cAAArP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNsC,YAAY,iBACXjD,OAAA;MAAKmL,KAAK,EAAE;QACV,GAAGjE,QAAQ,CAAC,cAAc,CAAC;QAC3BvC,eAAe,EAAE1B,YAAY,CAACsK,IAAI,KAAK,OAAO,GAAG,SAAS,GAC3CtK,YAAY,CAACsK,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;QACtEzI,KAAK,EAAE,OAAO;QACdK,MAAM,EAAE6O,QAAQ,GAAG,aAAa/Q,YAAY,CAACsK,IAAI,KAAK,OAAO,GAAG,SAAS,GAAGtK,YAAY,CAACsK,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE,GAAG;MAC1I,CAAE;MAAAsC,QAAA,EACC5M,YAAY,CAACqK;IAAG;MAAA9M,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGDX,OAAA;MAAA6P,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAArP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACM,EAAA,CAxkDID,YAAY;AAAA2T,EAAA,GAAZ3T,YAAY;AAykDlB,eAAeA,YAAY;AAAC,IAAA2T,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}