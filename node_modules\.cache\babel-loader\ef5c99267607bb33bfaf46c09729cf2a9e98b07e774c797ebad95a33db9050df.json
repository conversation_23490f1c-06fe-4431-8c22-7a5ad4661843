{"ast": null, "code": "import { VOID, PRIMITIVE, ARRAY, OBJECT, DATE, REGEXP, MAP, SET, ERROR, BIGINT } from './types.js';\nconst env = typeof self === 'object' ? self : globalThis;\nconst deserializer = ($, _) => {\n  const as = (out, index) => {\n    $.set(index, out);\n    return out;\n  };\n  const unpair = index => {\n    if ($.has(index)) return $.get(index);\n    const [type, value] = _[index];\n    switch (type) {\n      case PRIMITIVE:\n      case VOID:\n        return as(value, index);\n      case ARRAY:\n        {\n          const arr = as([], index);\n          for (const index of value) arr.push(unpair(index));\n          return arr;\n        }\n      case OBJECT:\n        {\n          const object = as({}, index);\n          for (const [key, index] of value) object[unpair(key)] = unpair(index);\n          return object;\n        }\n      case DATE:\n        return as(new Date(value), index);\n      case REGEXP:\n        {\n          const {\n            source,\n            flags\n          } = value;\n          return as(new RegExp(source, flags), index);\n        }\n      case MAP:\n        {\n          const map = as(new Map(), index);\n          for (const [key, index] of value) map.set(unpair(key), unpair(index));\n          return map;\n        }\n      case SET:\n        {\n          const set = as(new Set(), index);\n          for (const index of value) set.add(unpair(index));\n          return set;\n        }\n      case ERROR:\n        {\n          const {\n            name,\n            message\n          } = value;\n          return as(new env[name](message), index);\n        }\n      case BIGINT:\n        return as(BigInt(value), index);\n      case 'BigInt':\n        return as(Object(BigInt(value)), index);\n      case 'ArrayBuffer':\n        return as(new Uint8Array(value).buffer, value);\n      case 'DataView':\n        {\n          const {\n            buffer\n          } = new Uint8Array(value);\n          return as(new DataView(buffer), value);\n        }\n    }\n    return as(new env[type](value), index);\n  };\n  return unpair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */\nexport const deserialize = serialized => deserializer(new Map(), serialized)(0);", "map": {"version": 3, "names": ["VOID", "PRIMITIVE", "ARRAY", "OBJECT", "DATE", "REGEXP", "MAP", "SET", "ERROR", "BIGINT", "env", "self", "globalThis", "deserializer", "$", "_", "as", "out", "index", "set", "unpair", "has", "get", "type", "value", "arr", "push", "object", "key", "Date", "source", "flags", "RegExp", "map", "Map", "Set", "add", "name", "message", "BigInt", "Object", "Uint8Array", "buffer", "DataView", "deserialize", "serialized"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich/node_modules/@ungap/structured-clone/esm/deserialize.js"], "sourcesContent": ["import {\n  VOID, PRIMITIVE,\n  ARRAY, OBJECT,\n  DATE, REGEXP, MAP, SET,\n  ERROR, BIGINT\n} from './types.js';\n\nconst env = typeof self === 'object' ? self : globalThis;\n\nconst deserializer = ($, _) => {\n  const as = (out, index) => {\n    $.set(index, out);\n    return out;\n  };\n\n  const unpair = index => {\n    if ($.has(index))\n      return $.get(index);\n\n    const [type, value] = _[index];\n    switch (type) {\n      case PRIMITIVE:\n      case VOID:\n        return as(value, index);\n      case ARRAY: {\n        const arr = as([], index);\n        for (const index of value)\n          arr.push(unpair(index));\n        return arr;\n      }\n      case OBJECT: {\n        const object = as({}, index);\n        for (const [key, index] of value)\n          object[unpair(key)] = unpair(index);\n        return object;\n      }\n      case DATE:\n        return as(new Date(value), index);\n      case REGEXP: {\n        const {source, flags} = value;\n        return as(new RegExp(source, flags), index);\n      }\n      case MAP: {\n        const map = as(new Map, index);\n        for (const [key, index] of value)\n          map.set(unpair(key), unpair(index));\n        return map;\n      }\n      case SET: {\n        const set = as(new Set, index);\n        for (const index of value)\n          set.add(unpair(index));\n        return set;\n      }\n      case ERROR: {\n        const {name, message} = value;\n        return as(new env[name](message), index);\n      }\n      case BIGINT:\n        return as(BigInt(value), index);\n      case 'BigInt':\n        return as(Object(BigInt(value)), index);\n      case 'ArrayBuffer':\n        return as(new Uint8Array(value).buffer, value);\n      case 'DataView': {\n        const { buffer } = new Uint8Array(value);\n        return as(new DataView(buffer), value);\n      }\n    }\n    return as(new env[type](value), index);\n  };\n\n  return unpair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */\nexport const deserialize = serialized => deserializer(new Map, serialized)(0);\n"], "mappings": "AAAA,SACEA,IAAI,EAAEC,SAAS,EACfC,KAAK,EAAEC,MAAM,EACbC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EACtBC,KAAK,EAAEC,MAAM,QACR,YAAY;AAEnB,MAAMC,GAAG,GAAG,OAAOC,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGC,UAAU;AAExD,MAAMC,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC7B,MAAMC,EAAE,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACzBJ,CAAC,CAACK,GAAG,CAACD,KAAK,EAAED,GAAG,CAAC;IACjB,OAAOA,GAAG;EACZ,CAAC;EAED,MAAMG,MAAM,GAAGF,KAAK,IAAI;IACtB,IAAIJ,CAAC,CAACO,GAAG,CAACH,KAAK,CAAC,EACd,OAAOJ,CAAC,CAACQ,GAAG,CAACJ,KAAK,CAAC;IAErB,MAAM,CAACK,IAAI,EAAEC,KAAK,CAAC,GAAGT,CAAC,CAACG,KAAK,CAAC;IAC9B,QAAQK,IAAI;MACV,KAAKtB,SAAS;MACd,KAAKD,IAAI;QACP,OAAOgB,EAAE,CAACQ,KAAK,EAAEN,KAAK,CAAC;MACzB,KAAKhB,KAAK;QAAE;UACV,MAAMuB,GAAG,GAAGT,EAAE,CAAC,EAAE,EAAEE,KAAK,CAAC;UACzB,KAAK,MAAMA,KAAK,IAAIM,KAAK,EACvBC,GAAG,CAACC,IAAI,CAACN,MAAM,CAACF,KAAK,CAAC,CAAC;UACzB,OAAOO,GAAG;QACZ;MACA,KAAKtB,MAAM;QAAE;UACX,MAAMwB,MAAM,GAAGX,EAAE,CAAC,CAAC,CAAC,EAAEE,KAAK,CAAC;UAC5B,KAAK,MAAM,CAACU,GAAG,EAAEV,KAAK,CAAC,IAAIM,KAAK,EAC9BG,MAAM,CAACP,MAAM,CAACQ,GAAG,CAAC,CAAC,GAAGR,MAAM,CAACF,KAAK,CAAC;UACrC,OAAOS,MAAM;QACf;MACA,KAAKvB,IAAI;QACP,OAAOY,EAAE,CAAC,IAAIa,IAAI,CAACL,KAAK,CAAC,EAAEN,KAAK,CAAC;MACnC,KAAKb,MAAM;QAAE;UACX,MAAM;YAACyB,MAAM;YAAEC;UAAK,CAAC,GAAGP,KAAK;UAC7B,OAAOR,EAAE,CAAC,IAAIgB,MAAM,CAACF,MAAM,EAAEC,KAAK,CAAC,EAAEb,KAAK,CAAC;QAC7C;MACA,KAAKZ,GAAG;QAAE;UACR,MAAM2B,GAAG,GAAGjB,EAAE,CAAC,IAAIkB,GAAG,CAAD,CAAC,EAAEhB,KAAK,CAAC;UAC9B,KAAK,MAAM,CAACU,GAAG,EAAEV,KAAK,CAAC,IAAIM,KAAK,EAC9BS,GAAG,CAACd,GAAG,CAACC,MAAM,CAACQ,GAAG,CAAC,EAAER,MAAM,CAACF,KAAK,CAAC,CAAC;UACrC,OAAOe,GAAG;QACZ;MACA,KAAK1B,GAAG;QAAE;UACR,MAAMY,GAAG,GAAGH,EAAE,CAAC,IAAImB,GAAG,CAAD,CAAC,EAAEjB,KAAK,CAAC;UAC9B,KAAK,MAAMA,KAAK,IAAIM,KAAK,EACvBL,GAAG,CAACiB,GAAG,CAAChB,MAAM,CAACF,KAAK,CAAC,CAAC;UACxB,OAAOC,GAAG;QACZ;MACA,KAAKX,KAAK;QAAE;UACV,MAAM;YAAC6B,IAAI;YAAEC;UAAO,CAAC,GAAGd,KAAK;UAC7B,OAAOR,EAAE,CAAC,IAAIN,GAAG,CAAC2B,IAAI,CAAC,CAACC,OAAO,CAAC,EAAEpB,KAAK,CAAC;QAC1C;MACA,KAAKT,MAAM;QACT,OAAOO,EAAE,CAACuB,MAAM,CAACf,KAAK,CAAC,EAAEN,KAAK,CAAC;MACjC,KAAK,QAAQ;QACX,OAAOF,EAAE,CAACwB,MAAM,CAACD,MAAM,CAACf,KAAK,CAAC,CAAC,EAAEN,KAAK,CAAC;MACzC,KAAK,aAAa;QAChB,OAAOF,EAAE,CAAC,IAAIyB,UAAU,CAACjB,KAAK,CAAC,CAACkB,MAAM,EAAElB,KAAK,CAAC;MAChD,KAAK,UAAU;QAAE;UACf,MAAM;YAAEkB;UAAO,CAAC,GAAG,IAAID,UAAU,CAACjB,KAAK,CAAC;UACxC,OAAOR,EAAE,CAAC,IAAI2B,QAAQ,CAACD,MAAM,CAAC,EAAElB,KAAK,CAAC;QACxC;IACF;IACA,OAAOR,EAAE,CAAC,IAAIN,GAAG,CAACa,IAAI,CAAC,CAACC,KAAK,CAAC,EAAEN,KAAK,CAAC;EACxC,CAAC;EAED,OAAOE,MAAM;AACf,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMwB,WAAW,GAAGC,UAAU,IAAIhC,YAAY,CAAC,IAAIqB,GAAG,CAAD,CAAC,EAAEW,UAAU,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}