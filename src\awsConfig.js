// AWS Configuration
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/api';
import { getCurrentUser, signUp, signIn, signOut, confirmSignUp, resendSignUpCode } from 'aws-amplify/auth';
import { uploadData, getUrl, remove } from 'aws-amplify/storage';

// AWS Amplify Configuration
const awsConfig = {
  Auth: {
    Cognito: {
      userPoolId: process.env.REACT_APP_USER_POOL_ID || 'us-east-1_XXXXXXXXX',
      userPoolClientId: process.env.REACT_APP_USER_POOL_CLIENT_ID || 'XXXXXXXXXXXXXXXXXXXXXXXXXX',
      region: process.env.REACT_APP_AWS_REGION || 'us-east-1',
      signUpVerificationMethod: 'code',
      loginWith: {
        email: true,
        username: false,
        phone: false
      }
    }
  },
  API: {
    GraphQL: {
      endpoint: process.env.REACT_APP_GRAPHQL_ENDPOINT || 'https://XXXXXXXXXXXXXXXXXXXXXXXXXX.appsync-api.us-east-1.amazonaws.com/graphql',
      region: process.env.REACT_APP_AWS_REGION || 'us-east-1',
      defaultAuthMode: 'userPool'
    },
    REST: {
      'api': {
        endpoint: process.env.REACT_APP_API_ENDPOINT || 'https://XXXXXXXXXX.execute-api.us-east-1.amazonaws.com/prod',
        region: process.env.REACT_APP_AWS_REGION || 'us-east-1'
      }
    }
  },
  Storage: {
    S3: {
      bucket: process.env.REACT_APP_S3_BUCKET || 'your-app-storage-bucket',
      region: process.env.REACT_APP_AWS_REGION || 'us-east-1'
    }
  }
};

// Initialize Amplify
Amplify.configure(awsConfig);

// Create API client
const client = generateClient();

// AWS Service Classes
class AWSAuth {
  static async signUp(email, password, profilePic = null) {
    try {
      const { user } = await signUp({
        username: email,
        password,
        attributes: {
          email,
          'custom:profile_pic': profilePic || ''
        }
      });
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async confirmSignUp(email, code) {
    try {
      await confirmSignUp({
        username: email,
        confirmationCode: code
      });
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async signIn(email, password) {
    try {
      const { user } = await signIn({
        username: email,
        password
      });
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async signOut() {
    try {
      await signOut();
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async getCurrentUser() {
    try {
      const user = await getCurrentUser();
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async resendConfirmationCode(email) {
    try {
      await resendSignUpCode({ username: email });
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

class AWSStorage {
  static async uploadFile(file, key) {
    try {
      const result = await uploadData({
        key,
        data: file,
        options: {
          accessLevel: 'guest'
        }
      }).result;
      return { success: true, key: result.key };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async getFileUrl(key) {
    try {
      const url = await getUrl({
        key,
        options: {
          accessLevel: 'guest'
        }
      });
      return { success: true, url: url.url };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async deleteFile(key) {
    try {
      await remove({ key });
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

class AWSAPI {
  static async post(endpoint, data) {
    try {
      const response = await client.graphql({
        query: endpoint,
        variables: data
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async get(endpoint, params = {}) {
    try {
      const response = await client.graphql({
        query: endpoint,
        variables: params
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

// Email service using AWS SES via Lambda
class AWSEmail {
  static async sendOTP(email, otp) {
    try {
      const response = await fetch(`${awsConfig.API.REST.api.endpoint}/send-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp })
      });
      
      if (response.ok) {
        return { success: true };
      } else {
        throw new Error('Failed to send email');
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async sendWelcomeEmail(email, name) {
    try {
      const response = await fetch(`${awsConfig.API.REST.api.endpoint}/send-welcome`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, name })
      });
      
      if (response.ok) {
        return { success: true };
      } else {
        throw new Error('Failed to send welcome email');
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

// Database operations using DynamoDB via API Gateway
class AWSDatabase {
  static async createUser(userData) {
    try {
      const response = await fetch(`${awsConfig.API.REST.api.endpoint}/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
      });
      
      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        throw new Error('Failed to create user');
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async getUser(userId) {
    try {
      const response = await fetch(`${awsConfig.API.REST.api.endpoint}/users/${userId}`);
      
      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        throw new Error('Failed to get user');
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async updateUser(userId, userData) {
    try {
      const response = await fetch(`${awsConfig.API.REST.api.endpoint}/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
      });
      
      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        throw new Error('Failed to update user');
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

export { 
  AWSAuth, 
  AWSStorage, 
  AWSAPI, 
  AWSEmail, 
  AWSDatabase,
  awsConfig 
};
