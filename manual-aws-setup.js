// Manual AWS Setup Script for Windows
// Run this with: node manual-aws-setup.js

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting AWS Setup for EduNova...\n');

// Configuration
const PROJECT_NAME = 'edunova';
const REGION = 'us-east-1';
const TIMESTAMP = Date.now();
const BUCKET_NAME = `${PROJECT_NAME}-storage-${TIMESTAMP}`;
const USER_POOL_NAME = `${PROJECT_NAME}-user-pool`;
const TABLE_NAME = `${PROJECT_NAME}-users`;

function runCommand(command, description) {
    try {
        console.log(`📋 ${description}...`);
        const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
        console.log(`✅ ${description} completed`);
        return result.trim();
    } catch (error) {
        console.error(`❌ ${description} failed:`, error.message);
        throw error;
    }
}

function createEnvFile(config) {
    const envContent = `# AWS Configuration
REACT_APP_AWS_REGION=${config.region}
REACT_APP_USER_POOL_ID=${config.userPoolId}
REACT_APP_USER_POOL_CLIENT_ID=${config.userPoolClientId}
REACT_APP_API_ENDPOINT=${config.apiEndpoint}
REACT_APP_S3_BUCKET=${config.bucketName}
`;

    fs.writeFileSync('.env', envContent);
    console.log('✅ Environment file created: .env');
}

async function setupAWS() {
    try {
        // Check AWS CLI
        runCommand('aws --version', 'Checking AWS CLI');
        runCommand('aws sts get-caller-identity', 'Verifying AWS credentials');

        console.log('\n📋 Configuration:');
        console.log(`  Project: ${PROJECT_NAME}`);
        console.log(`  Region: ${REGION}`);
        console.log(`  S3 Bucket: ${BUCKET_NAME}`);
        console.log(`  User Pool: ${USER_POOL_NAME}`);
        console.log(`  DynamoDB Table: ${TABLE_NAME}\n`);

        // 1. Create S3 Bucket
        runCommand(`aws s3 mb s3://${BUCKET_NAME} --region ${REGION}`, 'Creating S3 bucket');
        
        // Configure CORS for S3
        const corsConfig = {
            CORSRules: [{
                AllowedHeaders: ["*"],
                AllowedMethods: ["GET", "PUT", "POST", "DELETE"],
                AllowedOrigins: ["*"],
                ExposeHeaders: []
            }]
        };
        
        fs.writeFileSync('cors.json', JSON.stringify(corsConfig, null, 2));
        runCommand(`aws s3api put-bucket-cors --bucket ${BUCKET_NAME} --cors-configuration file://cors.json`, 'Configuring S3 CORS');
        fs.unlinkSync('cors.json');

        // 2. Create Cognito User Pool
        const userPoolCommand = `aws cognito-idp create-user-pool --pool-name ${USER_POOL_NAME} --policies "{\\"PasswordPolicy\\":{\\"MinimumLength\\":8,\\"RequireUppercase\\":false,\\"RequireLowercase\\":false,\\"RequireNumbers\\":false,\\"RequireSymbols\\":false}}" --auto-verified-attributes email --verification-message-template "{\\"DefaultEmailOption\\":\\"CONFIRM_WITH_CODE\\",\\"EmailSubject\\":\\"EduNova - Verify your email\\",\\"EmailMessage\\":\\"Your verification code is {####}\\"}" --query "UserPool.Id" --output text`;
        
        const userPoolId = runCommand(userPoolCommand, 'Creating Cognito User Pool');

        // 3. Create User Pool Client
        const clientCommand = `aws cognito-idp create-user-pool-client --user-pool-id ${userPoolId} --client-name "${PROJECT_NAME}-client" --no-generate-secret --explicit-auth-flows ADMIN_NO_SRP_AUTH ALLOW_USER_PASSWORD_AUTH ALLOW_REFRESH_TOKEN_AUTH --query "UserPoolClient.ClientId" --output text`;
        
        const userPoolClientId = runCommand(clientCommand, 'Creating User Pool Client');

        // 4. Create DynamoDB Table
        const dynamoCommand = `aws dynamodb create-table --table-name ${TABLE_NAME} --attribute-definitions AttributeName=userId,AttributeType=S --key-schema AttributeName=userId,KeyType=HASH --billing-mode PAY_PER_REQUEST --region ${REGION}`;
        
        runCommand(dynamoCommand, 'Creating DynamoDB table');

        // 5. Create IAM Role for Lambda
        const trustPolicy = {
            Version: "2012-10-17",
            Statement: [{
                Effect: "Allow",
                Principal: { Service: "lambda.amazonaws.com" },
                Action: "sts:AssumeRole"
            }]
        };

        fs.writeFileSync('trust-policy.json', JSON.stringify(trustPolicy, null, 2));
        
        const lambdaRoleName = `${PROJECT_NAME}-lambda-role`;
        runCommand(`aws iam create-role --role-name ${lambdaRoleName} --assume-role-policy-document file://trust-policy.json`, 'Creating IAM role for Lambda');

        // Attach policies
        runCommand(`aws iam attach-role-policy --role-name ${lambdaRoleName} --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole`, 'Attaching Lambda execution policy');
        runCommand(`aws iam attach-role-policy --role-name ${lambdaRoleName} --policy-arn arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess`, 'Attaching DynamoDB policy');
        runCommand(`aws iam attach-role-policy --role-name ${lambdaRoleName} --policy-arn arn:aws:iam::aws:policy/AmazonSESFullAccess`, 'Attaching SES policy');

        const lambdaRoleArn = runCommand(`aws iam get-role --role-name ${lambdaRoleName} --query "Role.Arn" --output text`, 'Getting Lambda role ARN');

        console.log('⏳ Waiting for IAM role to be ready...');
        await new Promise(resolve => setTimeout(resolve, 10000));

        // 6. Create Lambda Functions
        console.log('⚡ Creating Lambda functions...');

        // Create ZIP files for Lambda functions
        const sendOtpCode = `
const AWS = require('aws-sdk');
const ses = new AWS.SES({ region: 'us-east-1' });

exports.handler = async (event) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    };

    if (event.httpMethod === 'OPTIONS') {
        return { statusCode: 200, headers, body: '' };
    }

    try {
        const { email, otp } = JSON.parse(event.body);
        
        const params = {
            Destination: { ToAddresses: [email] },
            Message: {
                Body: {
                    Text: {
                        Data: \`Your EduNova verification code is: \${otp}\`
                    }
                },
                Subject: { Data: \`EduNova - Verification Code: \${otp}\` }
            },
            Source: '<EMAIL>'
        };

        await ses.sendEmail(params).promise();
        
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ success: true })
        };
    } catch (error) {
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: error.message })
        };
    }
};`;

        // Write Lambda function code
        if (!fs.existsSync('aws-lambda')) fs.mkdirSync('aws-lambda');
        if (!fs.existsSync('aws-lambda/send-otp')) fs.mkdirSync('aws-lambda/send-otp');
        
        fs.writeFileSync('aws-lambda/send-otp/index.js', sendOtpCode);

        // Create ZIP (simplified for Windows)
        console.log('📦 Creating deployment packages...');
        
        // For now, we'll create the Lambda functions without ZIP files
        // You can manually upload the code later through AWS Console

        // 7. Create API Gateway
        const apiCommand = `aws apigatewayv2 create-api --name "${PROJECT_NAME}-api" --protocol-type HTTP --cors-configuration AllowCredentials=false,AllowHeaders="*",AllowMethods="*",AllowOrigins="*" --query "ApiId" --output text`;
        
        const apiId = runCommand(apiCommand, 'Creating API Gateway');
        const apiEndpoint = `https://${apiId}.execute-api.${REGION}.amazonaws.com/prod`;

        // Create stage
        runCommand(`aws apigatewayv2 create-stage --api-id ${apiId} --stage-name prod --auto-deploy`, 'Creating API stage');

        // 8. Generate environment file
        createEnvFile({
            region: REGION,
            userPoolId,
            userPoolClientId,
            apiEndpoint,
            bucketName: BUCKET_NAME
        });

        // Cleanup
        if (fs.existsSync('trust-policy.json')) fs.unlinkSync('trust-policy.json');

        console.log('\n🎉 AWS setup complete!\n');
        console.log('📋 Summary:');
        console.log(`  ✅ S3 Bucket: ${BUCKET_NAME}`);
        console.log(`  ✅ Cognito User Pool: ${userPoolId}`);
        console.log(`  ✅ User Pool Client: ${userPoolClientId}`);
        console.log(`  ✅ DynamoDB Table: ${TABLE_NAME}`);
        console.log(`  ✅ API Gateway: ${apiEndpoint}`);
        console.log(`  ✅ Environment file: .env\n`);
        
        console.log('🚀 Next steps:');
        console.log('  1. Run "npm start" to start your application');
        console.log('  2. Your app will now use AWS services!');
        console.log('  3. Check AWS Console to see your resources\n');

    } catch (error) {
        console.error('\n❌ Setup failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('  1. Ensure AWS CLI is installed and configured');
        console.log('  2. Check your AWS credentials and permissions');
        console.log('  3. Verify you have the required IAM policies');
        process.exit(1);
    }
}

// Run setup
setupAWS();
