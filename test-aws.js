// Test AWS CLI and Configuration
const { execSync } = require('child_process');

console.log('🔍 Testing AWS Configuration...\n');

function testCommand(command, description) {
    try {
        console.log(`📋 ${description}...`);
        const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
        console.log(`✅ ${description} - SUCCESS`);
        console.log(`   Result: ${result.trim()}\n`);
        return true;
    } catch (error) {
        console.log(`❌ ${description} - FAILED`);
        console.log(`   Error: ${error.message}\n`);
        return false;
    }
}

async function runTests() {
    console.log('🚀 AWS Setup Readiness Check\n');
    
    let allPassed = true;
    
    // Test 1: AWS CLI Installation
    if (!testCommand('aws --version', 'AWS CLI Installation')) {
        allPassed = false;
        console.log('💡 Fix: Install AWS CLI from https://awscli.amazonaws.com/AWSCLIV2.msi\n');
    }
    
    // Test 2: AWS Credentials
    if (!testCommand('aws sts get-caller-identity', 'AWS Credentials')) {
        allPassed = false;
        console.log('💡 Fix: Run "aws configure" and enter your credentials\n');
    }
    
    // Test 3: AWS Region
    if (!testCommand('aws configure get region', 'AWS Region Configuration')) {
        allPassed = false;
        console.log('💡 Fix: Set region with "aws configure set region us-east-1"\n');
    }
    
    // Test 4: Node.js Dependencies
    try {
        require('aws-amplify');
        console.log('✅ AWS Amplify dependency - SUCCESS\n');
    } catch (error) {
        console.log('❌ AWS Amplify dependency - FAILED');
        console.log('💡 Fix: Run "npm install aws-amplify aws-sdk"\n');
        allPassed = false;
    }
    
    if (allPassed) {
        console.log('🎉 All tests passed! You\'re ready to set up AWS services.\n');
        console.log('🚀 Next step: Run "node manual-aws-setup.js" to create AWS resources\n');
    } else {
        console.log('⚠️  Some tests failed. Please fix the issues above before proceeding.\n');
        console.log('📚 Setup Guide: Check SETUP_INSTRUCTIONS.md for detailed steps\n');
    }
}

runTests();
