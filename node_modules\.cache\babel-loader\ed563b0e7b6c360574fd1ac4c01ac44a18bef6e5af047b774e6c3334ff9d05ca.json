{"ast": null, "code": "import { deserialize } from './deserialize.js';\nimport { serialize } from './serialize.js';\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */\nexport default typeof structuredClone === \"function\" ? /* c8 ignore start */\n(any, options) => options && ('json' in options || 'lossy' in options) ? deserialize(serialize(any, options)) : structuredClone(any) : (any, options) => deserialize(serialize(any, options));\n/* c8 ignore stop */\n\nexport { deserialize, serialize };", "map": {"version": 3, "names": ["deserialize", "serialize", "structuredClone", "any", "options"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/node_modules/@ungap/structured-clone/esm/index.js"], "sourcesContent": ["import {deserialize} from './deserialize.js';\nimport {serialize} from './serialize.js';\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */\nexport default typeof structuredClone === \"function\" ?\n  /* c8 ignore start */\n  (any, options) => (\n    options && ('json' in options || 'lossy' in options) ?\n      deserialize(serialize(any, options)) : structuredClone(any)\n  ) :\n  (any, options) => deserialize(serialize(any, options));\n  /* c8 ignore stop */\n\nexport {deserialize, serialize};\n"], "mappings": "AAAA,SAAQA,WAAW,QAAO,kBAAkB;AAC5C,SAAQC,SAAS,QAAO,gBAAgB;;AAExC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,OAAOC,eAAe,KAAK,UAAU,GAClD;AACA,CAACC,GAAG,EAAEC,OAAO,KACXA,OAAO,KAAK,MAAM,IAAIA,OAAO,IAAI,OAAO,IAAIA,OAAO,CAAC,GAClDJ,WAAW,CAACC,SAAS,CAACE,GAAG,EAAEC,OAAO,CAAC,CAAC,GAAGF,eAAe,CAACC,GAAG,CAC7D,GACD,CAACA,GAAG,EAAEC,OAAO,KAAKJ,WAAW,CAACC,SAAS,CAACE,GAAG,EAAEC,OAAO,CAAC,CAAC;AACtD;;AAEF,SAAQJ,WAAW,EAAEC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}