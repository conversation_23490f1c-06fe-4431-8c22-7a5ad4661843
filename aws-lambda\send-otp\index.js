// AWS Lambda function for sending OTP emails via SES
const AWS = require('aws-sdk');
const ses = new AWS.SES({ region: 'us-east-1' });

exports.handler = async (event) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        const { email, otp } = JSON.parse(event.body);

        if (!email || !otp) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Email and OTP are required' })
            };
        }

        const params = {
            Destination: {
                ToAddresses: [email]
            },
            Message: {
                Body: {
                    Html: {
                        Charset: 'UTF-8',
                        Data: `
                            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                                <div style="text-align: center; margin-bottom: 30px;">
                                    <h1 style="color: #4A90E2; margin: 0;">EduNova</h1>
                                    <p style="color: #666; margin: 5px 0;">AI Powered Learning System</p>
                                </div>
                                
                                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px; text-align: center; color: white;">
                                    <h2 style="margin: 0 0 20px 0;">Email Verification</h2>
                                    <p style="margin: 0 0 20px 0; font-size: 16px;">Your verification code is:</p>
                                    <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; font-size: 32px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
                                        ${otp}
                                    </div>
                                    <p style="margin: 20px 0 0 0; font-size: 14px; opacity: 0.9;">This code will expire in 10 minutes</p>
                                </div>
                                
                                <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                    <h3 style="color: #333; margin: 0 0 15px 0;">🚀 Welcome to EduNova!</h3>
                                    <ul style="color: #666; margin: 0; padding-left: 20px;">
                                        <li>🎯 Personalized Learning Experience</li>
                                        <li>🤖 AI-Powered Study Assistant</li>
                                        <li>📊 Real-time Progress Tracking</li>
                                        <li>🎓 Expert-Curated Content</li>
                                    </ul>
                                </div>
                                
                                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                                    <p style="color: #999; font-size: 12px; margin: 0;">
                                        If you didn't request this verification, please ignore this email.
                                    </p>
                                </div>
                            </div>
                        `
                    },
                    Text: {
                        Charset: 'UTF-8',
                        Data: `
EduNova - Email Verification

Your verification code is: ${otp}

This code will expire in 10 minutes.

Welcome to EduNova! 🚀
- Personalized Learning Experience
- AI-Powered Study Assistant  
- Real-time Progress Tracking
- Expert-Curated Content

If you didn't request this verification, please ignore this email.
                        `
                    }
                },
                Subject: {
                    Charset: 'UTF-8',
                    Data: `🔐 EduNova - Your Verification Code: ${otp}`
                }
            },
            Source: process.env.FROM_EMAIL || '<EMAIL>'
        };

        const result = await ses.sendEmail(params).promise();
        
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ 
                success: true, 
                messageId: result.MessageId 
            })
        };

    } catch (error) {
        console.error('Error sending email:', error);
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Failed to send email',
                details: error.message 
            })
        };
    }
};
