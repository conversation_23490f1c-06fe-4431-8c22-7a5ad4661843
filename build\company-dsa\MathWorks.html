
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MathWorks DSA Questions</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #818cf8;
            --secondary: #f1f5f9;
            --easy: #22c55e;
            --medium: #f59e0b;
            --hard: #ef4444;
            --text: #0f172a;
            --text-light: #64748b;
            --card-bg: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--secondary);
            color: var(--text);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        h1 {
            color: var(--primary-dark);
            text-align: center;
            margin: 40px 0;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
            }
            to {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
            }
        }

        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 20px;
            border: 2px solid var(--primary-light);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: var(--card-bg);
            color: var(--text);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .difficulty-filter {
            display: flex;
            gap: 12px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            opacity: 0.8;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            opacity: 1;
        }

        .filter-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: var(--shadow);
        }

        .filter-btn.easy {
            background-color: var(--easy);
            color: white;
        }

        .filter-btn.medium {
            background-color: var(--medium);
            color: white;
        }

        .filter-btn.hard {
            background-color: var(--hard);
            color: white;
        }

        .topics-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 20px 0 30px;
        }

        .topic-tag {
            padding: 8px 16px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.8;
            transition: all 0.3s ease;
            user-select: none;
        }

        .topic-tag:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        .topic-tag.active {
            opacity: 1;
            background-color: var(--primary-dark);
            box-shadow: var(--shadow);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 16px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .difficulty {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .difficulty-easy { background-color: var(--easy); color: white; }
        .difficulty-medium { background-color: var(--medium); color: white; }
        .difficulty-hard { background-color: var(--hard); color: white; }

        .question-title {
            font-size: 1.2em;
            margin: 10px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        .question-link {
            color: var(--primary-dark);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .question-link:hover {
            color: var(--primary);
            text-decoration: underline;
        }

        .question-stats {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            font-size: 0.95em;
            color: var(--text-light);
            padding-top: 15px;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
        }

        .topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .topic-badge:hover {
            transform: translateY(-2px);
            background-color: var(--primary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            h1 {
                font-size: 2rem;
                margin: 30px 0;
            }

            .filters {
                flex-direction: column;
                gap: 15px;
            }

            .difficulty-filter {
                justify-content: center;
                width: 100%;
            }

            .search-box {
                width: 100%;
            }

            .stat-card {
                padding: 20px;
            }

            .question-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 2em;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 1.75rem;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .topic-tag {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .question-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling and selection styles */
        html {
            scroll-behavior: smooth;
        }

        ::selection {
            background-color: var(--primary-light);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MathWorks DSA Questions</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">58</div>
                <div>Total Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">11</div>
                <div>Easy Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">24</div>
                <div>Medium Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">23</div>
                <div>Hard Questions</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" placeholder="Search questions..." id="searchInput">
            <div class="difficulty-filter">
                <button class="filter-btn easy active" data-difficulty="EASY">Easy</button>
                <button class="filter-btn medium active" data-difficulty="MEDIUM">Medium</button>
                <button class="filter-btn hard active" data-difficulty="HARD">Hard</button>
            </div>
        </div>

        <div class="topics-filter">
            <div class="topic-tag" data-topic="Array">Array</div><div class="topic-tag" data-topic="Backtracking">Backtracking</div><div class="topic-tag" data-topic="Binary Search">Binary Search</div><div class="topic-tag" data-topic="Bit Manipulation">Bit Manipulation</div><div class="topic-tag" data-topic="Bitmask">Bitmask</div><div class="topic-tag" data-topic="Breadth-First Search">Breadth-First Search</div><div class="topic-tag" data-topic="Combinatorics">Combinatorics</div><div class="topic-tag" data-topic="Counting">Counting</div><div class="topic-tag" data-topic="Depth-First Search">Depth-First Search</div><div class="topic-tag" data-topic="Dynamic Programming">Dynamic Programming</div><div class="topic-tag" data-topic="Game Theory">Game Theory</div><div class="topic-tag" data-topic="Graph">Graph</div><div class="topic-tag" data-topic="Greedy">Greedy</div><div class="topic-tag" data-topic="Hash Table">Hash Table</div><div class="topic-tag" data-topic="Heap (Priority Queue)">Heap (Priority Queue)</div><div class="topic-tag" data-topic="Linked List">Linked List</div><div class="topic-tag" data-topic="Math">Math</div><div class="topic-tag" data-topic="Matrix">Matrix</div><div class="topic-tag" data-topic="Memoization">Memoization</div><div class="topic-tag" data-topic="Monotonic Queue">Monotonic Queue</div><div class="topic-tag" data-topic="Monotonic Stack">Monotonic Stack</div><div class="topic-tag" data-topic="Prefix Sum">Prefix Sum</div><div class="topic-tag" data-topic="Queue">Queue</div><div class="topic-tag" data-topic="Recursion">Recursion</div><div class="topic-tag" data-topic="Simulation">Simulation</div><div class="topic-tag" data-topic="Sliding Window">Sliding Window</div><div class="topic-tag" data-topic="Sorting">Sorting</div><div class="topic-tag" data-topic="Stack">Stack</div><div class="topic-tag" data-topic="String">String</div><div class="topic-tag" data-topic="String Matching">String Matching</div><div class="topic-tag" data-topic="Tree">Tree</div><div class="topic-tag" data-topic="Two Pointers">Two Pointers</div>
        </div>

        <div class="question-grid">

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Queue", "Sliding Window", "Monotonic Queue"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-subarrays-with-fixed-bounds" class="question-link" target="_blank">Count Subarrays With Fixed Bounds</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Monotonic Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 67.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-alloys" class="question-link" target="_blank">Maximum Number of Alloys</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 39.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-divisible-triplet-sums" class="question-link" target="_blank">Number of Divisible Triplet Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 66.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/pass-the-pillow" class="question-link" target="_blank">Pass the Pillow</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Dynamic Programming", "Depth-First Search", "Breadth-First Search", "Graph"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-edge-reversals-so-every-node-is-reachable" class="question-link" target="_blank">Minimum Edge Reversals So Every Node Is Reachable</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 57.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Matrix", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reshape-the-matrix" class="question-link" target="_blank">Reshape the Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Matrix</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 92.7%</span>
                    <span>Acceptance: 63.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Heap (Priority Queue)", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/total-cost-to-hire-k-workers" class="question-link" target="_blank">Total Cost to Hire K Workers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 92.7%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "String", "Dynamic Programming", "String Matching"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/string-transformation" class="question-link" target="_blank">String Transformation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">String Matching</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 92.5%</span>
                    <span>Acceptance: 30.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Queue", "Sliding Window", "Monotonic Queue"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-subarrays-with-fixed-bounds" class="question-link" target="_blank">Count Subarrays With Fixed Bounds</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Monotonic Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 92.5%</span>
                    <span>Acceptance: 67.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "Math", "String", "Combinatorics", "Counting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-anagrams" class="question-link" target="_blank">Count Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Combinatorics</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.4%</span>
                    <span>Acceptance: 34.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/earliest-second-to-mark-indices-i" class="question-link" target="_blank">Earliest Second to Mark Indices I</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.4%</span>
                    <span>Acceptance: 36.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "String", "Greedy", "Game Theory"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-colored-pieces-if-both-neighbors-are-the-same-color" class="question-link" target="_blank">Remove Colored Pieces if Both Neighbors are the Same Color</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Game Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.4%</span>
                    <span>Acceptance: 62.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Greedy", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/earliest-second-to-mark-indices-ii" class="question-link" target="_blank">Earliest Second to Mark Indices II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.4%</span>
                    <span>Acceptance: 21.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Stack", "Greedy", "Monotonic Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-cost-tree-from-leaf-values" class="question-link" target="_blank">Minimum Cost Tree From Leaf Values</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Greedy</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.4%</span>
                    <span>Acceptance: 67.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/keyboard-row" class="question-link" target="_blank">Keyboard Row</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.4%</span>
                    <span>Acceptance: 72.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Greedy", "Sorting", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-palindromes-after-operations" class="question-link" target="_blank">Maximum Palindromes After Operations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.4%</span>
                    <span>Acceptance: 43.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/last-substring-in-lexicographical-order" class="question-link" target="_blank">Last Substring in Lexicographical Order</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.4%</span>
                    <span>Acceptance: 34.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-alloys" class="question-link" target="_blank">Maximum Number of Alloys</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 84.1%</span>
                    <span>Acceptance: 39.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "String", "Dynamic Programming", "String Matching"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/string-transformation" class="question-link" target="_blank">String Transformation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">String Matching</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 82.0%</span>
                    <span>Acceptance: 30.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-divisible-triplet-sums" class="question-link" target="_blank">Number of Divisible Triplet Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 82.0%</span>
                    <span>Acceptance: 66.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/pass-the-pillow" class="question-link" target="_blank">Pass the Pillow</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.3%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "String", "Dynamic Programming", "String Matching"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/string-transformation" class="question-link" target="_blank">String Transformation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">String Matching</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.8%</span>
                    <span>Acceptance: 30.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Dynamic Programming", "Depth-First Search", "Breadth-First Search", "Graph"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-edge-reversals-so-every-node-is-reachable" class="question-link" target="_blank">Minimum Edge Reversals So Every Node Is Reachable</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.0%</span>
                    <span>Acceptance: 57.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Matrix", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reshape-the-matrix" class="question-link" target="_blank">Reshape the Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Matrix</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.3%</span>
                    <span>Acceptance: 63.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "String", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-string-chain" class="question-link" target="_blank">Longest String Chain</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.3%</span>
                    <span>Acceptance: 61.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Dynamic Programming", "Tree", "Depth-First Search", "Graph"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sum-of-distances-in-tree" class="question-link" target="_blank">Sum of Distances in Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.3%</span>
                    <span>Acceptance: 65.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-divisible-triplet-sums" class="question-link" target="_blank">Number of Divisible Triplet Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 66.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "String", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-string-chain" class="question-link" target="_blank">Longest String Chain</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 61.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Greedy", "Prefix Sum"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/split-array-largest-sum" class="question-link" target="_blank">Split Array Largest Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 57.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/break-a-palindrome" class="question-link" target="_blank">Break a Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 51.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Dynamic Programming", "Tree", "Depth-First Search", "Graph"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sum-of-distances-in-tree" class="question-link" target="_blank">Sum of Distances in Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.0%</span>
                    <span>Acceptance: 65.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Math", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/fraction-to-recurring-decimal" class="question-link" target="_blank">Fraction to Recurring Decimal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 25.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/last-substring-in-lexicographical-order" class="question-link" target="_blank">Last Substring in Lexicographical Order</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 34.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Greedy", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/earliest-second-to-mark-indices-ii" class="question-link" target="_blank">Earliest Second to Mark Indices II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 21.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "Math", "String", "Combinatorics", "Counting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-anagrams" class="question-link" target="_blank">Count Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Combinatorics</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 34.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "String", "Greedy", "Game Theory"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-colored-pieces-if-both-neighbors-are-the-same-color" class="question-link" target="_blank">Remove Colored Pieces if Both Neighbors are the Same Color</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Game Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 62.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-nodes-in-k-group" class="question-link" target="_blank">Reverse Nodes in k-Group</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 62.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Heap (Priority Queue)", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/total-cost-to-hire-k-workers" class="question-link" target="_blank">Total Cost to Hire K Workers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Backtracking", "Bit Manipulation", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/beautiful-arrangement" class="question-link" target="_blank">Beautiful Arrangement</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 64.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming", "Memoization"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-boxes" class="question-link" target="_blank">Remove Boxes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Memoization</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/break-a-palindrome" class="question-link" target="_blank">Break a Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 51.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/keyboard-row" class="question-link" target="_blank">Keyboard Row</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 72.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Greedy", "Prefix Sum"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/split-array-largest-sum" class="question-link" target="_blank">Split Array Largest Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 57.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/pass-the-pillow" class="question-link" target="_blank">Pass the Pillow</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Stack", "Greedy", "Monotonic Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-cost-tree-from-leaf-values" class="question-link" target="_blank">Minimum Cost Tree From Leaf Values</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Greedy</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 67.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/cherry-pickup" class="question-link" target="_blank">Cherry Pickup</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 37.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Greedy", "Sorting", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-palindromes-after-operations" class="question-link" target="_blank">Maximum Palindromes After Operations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 43.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/earliest-second-to-mark-indices-i" class="question-link" target="_blank">Earliest Second to Mark Indices I</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.0%</span>
                    <span>Acceptance: 36.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/cherry-pickup" class="question-link" target="_blank">Cherry Pickup</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 37.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-repeating-character-replacement" class="question-link" target="_blank">Longest Repeating Character Replacement</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 56.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Math", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/fraction-to-recurring-decimal" class="question-link" target="_blank">Fraction to Recurring Decimal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 25.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-difference-between-increasing-elements" class="question-link" target="_blank">Maximum Difference Between Increasing Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 58.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Backtracking", "Bit Manipulation", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/beautiful-arrangement" class="question-link" target="_blank">Beautiful Arrangement</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 64.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Math"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/convert-binary-number-in-a-linked-list-to-integer" class="question-link" target="_blank">Convert Binary Number in a Linked List to Integer</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Math</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 81.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming", "Memoization"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-boxes" class="question-link" target="_blank">Remove Boxes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Memoization</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-nodes-in-k-group" class="question-link" target="_blank">Reverse Nodes in k-Group</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.4%</span>
                    <span>Acceptance: 62.0%</span>
                </div>
            </div>

        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const questionCards = document.querySelectorAll('.question-card');
        const difficultyButtons = document.querySelectorAll('.filter-btn');
        const topicTags = document.querySelectorAll('.topic-tag');
        
        function filterQuestions() {
            const searchTerm = searchInput.value.toLowerCase();
            const activeDifficulties = Array.from(difficultyButtons)
                .filter(btn => btn.classList.contains('active'))
                .map(btn => btn.dataset.difficulty);
            const activeTopics = Array.from(topicTags)
                .filter(tag => tag.classList.contains('active'))
                .map(tag => tag.dataset.topic);
            
            questionCards.forEach(card => {
                const title = card.querySelector('.question-title').textContent.toLowerCase();
                const difficulty = card.dataset.difficulty;
                const topics = JSON.parse(card.dataset.topics);
                
                const matchesSearch = title.includes(searchTerm);
                const matchesDifficulty = activeDifficulties.includes(difficulty);
                const matchesTopics = activeTopics.length === 0 || 
                    topics.some(topic => activeTopics.includes(topic));
                
                if (matchesSearch && matchesDifficulty && matchesTopics) {
                    card.style.display = 'flex';
                    card.style.opacity = '1';
                } else {
                    card.style.display = 'none';
                    card.style.opacity = '0';
                }
            });
        }
        
        // Add smooth transitions for filtering
        questionCards.forEach(card => {
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        });
        
        searchInput.addEventListener('input', filterQuestions);
        
        difficultyButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                btn.classList.toggle('active');
                filterQuestions();
            });
        });
        
        topicTags.forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('active');
                filterQuestions();
            });
        });

        // Add smooth scrolling for all links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
