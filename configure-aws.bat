@echo off
echo 🔧 AWS CLI Configuration Helper
echo.
echo This script will help you configure AWS CLI with your credentials.
echo.
echo Before running this, make sure you have:
echo   1. AWS Account created
echo   2. IAM User created with AdministratorAccess policy
echo   3. Access Key ID and Secret Access Key ready
echo.
echo Press any key to continue...
pause >nul
echo.

REM Add AWS CLI to PATH
set PATH=%PATH%;C:\Program Files\Amazon\AWSCLIV2

echo 🚀 Starting AWS CLI configuration...
echo.
echo Please enter your AWS credentials when prompted:
echo   - AWS Access Key ID: [Your Access Key]
echo   - AWS Secret Access Key: [Your Secret Key]  
echo   - Default region name: us-east-1
echo   - Default output format: json
echo.

aws configure

echo.
echo 🧪 Testing configuration...
aws sts get-caller-identity

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ AWS CLI configured successfully!
    echo 🚀 Ready to create AWS resources!
    echo.
    echo Next step: Run "npm run aws-setup" or "node manual-aws-setup.js"
) else (
    echo.
    echo ❌ Configuration failed. Please check your credentials and try again.
    echo.
    echo Troubleshooting:
    echo   1. Verify your Access Key ID and Secret Access Key
    echo   2. Ensure the IAM user has proper permissions
    echo   3. Check if the AWS account is active
)

echo.
echo Press any key to exit...
pause >nul
