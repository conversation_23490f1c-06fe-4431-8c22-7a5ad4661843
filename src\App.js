// src/App.js

import React, { useState } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import AuthPage from './AuthPage';
import EduAIChatBot from './EduAIChatBot';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(
    () => localStorage.getItem('isAuthenticated') === 'true'
  );

  const handleAuthSuccess = () => {
    setIsAuthenticated(true);
    localStorage.setItem('isAuthenticated', 'true');
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem('isAuthenticated');
  };

  return (
    <BrowserRouter>
      <Routes>
        <Route
          path="/"
          element={
            !isAuthenticated ? (
              <AuthPage onAuthSuccess={handleAuthSuccess} />
            ) : (
              <EduAIChatBot onLogout={handleLogout} />
            )
          }
        />
        <Route
          path="/eduai-chatbot"
          element={<EduAIChatBot onLogout={handleLogout} />}
        />
      </Routes>
    </BrowserRouter>
  );
}

export default App;