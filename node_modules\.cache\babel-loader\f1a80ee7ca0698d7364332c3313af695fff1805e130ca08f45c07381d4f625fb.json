{"ast": null, "code": "// styles.js - Centralized Theme System\n\n// Color Themes Configuration\nconst themes = {\n  mustardGreen: {\n    primary: '#8B7355',\n    primaryDark: '#6B5B47',\n    primaryLight: '#A0956B',\n    secondary: '#F5F3F0',\n    accent: '#9A8B73',\n    background: '#f0f4f8',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(139, 115, 85, 0.1)',\n    shadowDark: 'rgba(139, 115, 85, 0.3)',\n    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',\n    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',\n    gradientLight: 'linear-gradient(135deg, #F5F3F0 0%, #E8E2D5 100%)'\n  },\n  redTheme: {\n    primary: '#DC2626',\n    primaryDark: '#B91C1C',\n    primaryLight: '#EF4444',\n    secondary: '#FEE2E2',\n    accent: '#F87171',\n    background: '#FEF2F2',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(220, 38, 38, 0.1)',\n    shadowDark: 'rgba(220, 38, 38, 0.3)',\n    gradient: 'linear-gradient(135deg, #DC2626 0%, #EF4444 100%)',\n    gradientReverse: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',\n    gradientLight: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)'\n  },\n  softRedGradient: {\n    primary: '#FFB6B6',\n    primaryDark: '#FF99CC',\n    primaryLight: '#FFC5C5',\n    secondary: '#FFF0F0',\n    accent: '#FFE0E0',\n    background: '#FFF5F5',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(255, 182, 182, 0.1)',\n    shadowDark: 'rgba(255, 182, 182, 0.3)',\n    gradient: 'linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)',\n    gradientReverse: 'linear-gradient(135deg, #FFB6B6 0%, #FF99CC 100%)',\n    gradientLight: 'linear-gradient(135deg, #FFF0F0 0%, #FFE0E0 100%)'\n  },\n  darkMode: {\n    primary: '#8B7355',\n    primaryDark: '#6B5B47',\n    primaryLight: '#A0956B',\n    secondary: '#252525',\n    accent: '#333333',\n    background: '#121212',\n    surface: '#1e1e1e',\n    text: '#e0e0e0',\n    textLight: '#a0a0a0',\n    textDark: '#ffffff',\n    border: '#333333',\n    shadow: 'rgba(0, 0, 0, 0.3)',\n    shadowDark: 'rgba(0, 0, 0, 0.5)',\n    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',\n    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',\n    gradientLight: 'linear-gradient(135deg, #252525 0%, #333333 100%)'\n  }\n};\n\n// Current theme selector - can be changed to switch themes\nconst currentTheme = themes.mustardGreen; // Change this to switch themes globally\n\n// Helper function to get theme colors\nconst getTheme = (themeName = 'mustardGreen') => {\n  return themes[themeName] || themes.mustardGreen;\n};\nconst styles = {\n  // Theme configuration\n  themes,\n  currentTheme,\n  getTheme,\n  // Base styles\n  background: {\n    minHeight: \"100vh\",\n    backgroundColor: currentTheme.background,\n    display: \"flex\",\n    flexDirection: \"column\",\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\",\n    position: \"relative\",\n    overflowX: \"hidden\"\n  },\n  animatedBackground: {\n    position: \"fixed\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: \"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)\",\n    zIndex: 0,\n    \"::before\": {\n      content: '\"\"',\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundImage: `\n          radial-gradient(circle at 10% 20%, rgba(255, 94, 94, 0.1) 0%, transparent 20%),\n          radial-gradient(circle at 90% 30%, rgba(94, 163, 255, 0.1) 0%, transparent 25%),\n          radial-gradient(circle at 50% 80%, rgba(255, 215, 94, 0.1) 0%, transparent 20%)\n        `,\n      animation: \"moveBackground 20s infinite alternate\"\n    }\n  },\n  navbar: {\n    width: \"100%\",\n    background: currentTheme.gradient,\n    boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n    padding: \"0.75rem 2rem\",\n    position: \"sticky\",\n    top: 0,\n    zIndex: 1000,\n    backdropFilter: \"blur(8px)\",\n    borderBottom: `1px solid ${currentTheme.border}`\n  },\n  navContainer: {\n    width: \"100%\",\n    maxWidth: \"1400px\",\n    margin: \"0 auto\",\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\"\n  },\n  menuButton: {\n    background: \"transparent\",\n    border: \"none\",\n    cursor: \"pointer\",\n    color: \"#4a5568\",\n    padding: \"0.5rem\",\n    borderRadius: \"8px\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    transition: \"all 0.2s ease\",\n    \":hover\": {\n      background: \"rgba(0, 0, 0, 0.05)\",\n      color: \"#3182ce\"\n    },\n    \"@media (min-width: 768px)\": {\n      display: \"none\"\n    }\n  },\n  centerTitleContainer: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    flex: 1,\n    textAlign: \"center\"\n  },\n  mainTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"bold\",\n    background: currentTheme.gradient,\n    WebkitBackgroundClip: \"text\",\n    WebkitTextFillColor: \"transparent\",\n    letterSpacing: \"0.5px\",\n    textTransform: \"uppercase\"\n  },\n  subTitle: {\n    fontSize: \"0.75rem\",\n    color: currentTheme.textLight,\n    marginTop: \"0.25rem\",\n    fontWeight: 500,\n    letterSpacing: \"0.5px\"\n  },\n  rightLogoContainer: {\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  logoImage: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    objectFit: \"cover\",\n    border: `2px solid ${currentTheme.primary}`,\n    boxShadow: `0 2px 4px ${currentTheme.shadow}`\n  },\n  sidebar: {\n    position: \"fixed\",\n    top: 0,\n    left: 0,\n    width: \"280px\",\n    height: \"100vh\",\n    backgroundColor: currentTheme.surface,\n    boxShadow: `4px 0 15px ${currentTheme.shadow}`,\n    zIndex: 1100,\n    transition: \"transform 0.3s ease-in-out\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    overflowY: \"auto\"\n  },\n  sidebarHeader: {\n    padding: \"1.5rem 1.5rem 1rem\",\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    borderBottom: `1px solid ${currentTheme.border}`\n  },\n  sidebarTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"600\",\n    color: currentTheme.text\n  },\n  closeSidebarButton: {\n    background: \"transparent\",\n    border: \"none\",\n    cursor: \"pointer\",\n    color: \"#4a5568\",\n    padding: \"0.25rem\",\n    borderRadius: \"4px\",\n    transition: \"all 0.2s ease\",\n    \":hover\": {\n      background: \"rgba(0, 0, 0, 0.05)\",\n      color: \"#3182ce\"\n    }\n  },\n  sidebarContent: {\n    flex: 1,\n    padding: \"1rem 0\",\n    overflowY: \"auto\"\n  },\n  sidebarItemGroup: {\n    marginBottom: \"0.5rem\"\n  },\n  sidebarItem: {\n    display: \"flex\",\n    alignItems: \"center\",\n    padding: \"0.75rem 1.5rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    position: \"relative\",\n    \":hover\": {\n      background: `${currentTheme.shadow}`,\n      color: currentTheme.primary\n    }\n  },\n  sidebarItemActive: {\n    background: `${currentTheme.shadow}`,\n    color: currentTheme.primary,\n    fontWeight: \"500\"\n  },\n  sidebarIcon: {\n    marginRight: \"1rem\",\n    fontSize: \"1.1rem\",\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  sidebarItemText: {\n    flex: 1\n  },\n  sidebarExpandIcon: {\n    transition: \"transform 0.2s ease\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    padding: \"0.25rem\",\n    borderRadius: \"4px\",\n    \":hover\": {\n      background: \"rgba(0, 0, 0, 0.05)\"\n    }\n  },\n  subItemsContainer: {\n    paddingLeft: \"2.5rem\",\n    overflow: \"hidden\",\n    transition: \"all 0.3s ease\"\n  },\n  subItem: {\n    padding: \"0.65rem 1.5rem\",\n    fontSize: \"0.9rem\",\n    color: \"#4a5568\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    \":hover\": {\n      background: \"rgba(139, 115, 85, 0.05)\",\n      color: \"#8B7355\"\n    }\n  },\n  sidebarFooter: {\n    padding: \"1.5rem\",\n    borderTop: \"1px solid rgba(0, 0, 0, 0.05)\",\n    textAlign: \"center\"\n  },\n  sidebarFooterText: {\n    fontSize: \"0.8rem\",\n    color: \"#718096\"\n  },\n  overlay: {\n    position: \"fixed\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n    zIndex: 1090,\n    \"@media (min-width: 768px)\": {\n      display: \"none\"\n    }\n  },\n  mainContainer: {\n    width: \"100%\",\n    maxWidth: \"1400px\",\n    margin: \"2rem auto\",\n    padding: \"0 2rem\",\n    flex: 1,\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"flex-start\",\n    position: \"relative\",\n    zIndex: 1,\n    \"@media (max-width: 768px)\": {\n      margin: \"1rem auto\",\n      padding: \"0 1rem\"\n    }\n  },\n  resumeContainer: {\n    width: \"100%\",\n    maxWidth: \"800px\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    height: \"calc(100vh - 120px)\",\n    \"@media (max-width: 768px)\": {\n      height: \"calc(100vh - 100px)\",\n      borderRadius: \"12px\"\n    },\n    \"@media (max-width: 480px)\": {\n      height: \"calc(100vh - 80px)\",\n      borderRadius: \"8px\"\n    }\n  },\n  gridOverlay: {\n    position: \"absolute\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundImage: `\n        linear-gradient(rgba(139, 115, 85, 0.03) 1px, transparent 1px),\n        linear-gradient(90deg, rgba(139, 115, 85, 0.03) 1px, transparent 1px)\n      `,\n    backgroundSize: \"20px 20px\",\n    pointerEvents: \"none\"\n  },\n  header: {\n    background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n    color: \"white\",\n    padding: \"1.75rem 2rem\",\n    textAlign: \"center\",\n    position: \"relative\",\n    overflow: \"hidden\",\n    \"@media (max-width: 768px)\": {\n      padding: \"1.5rem 1.5rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      padding: \"1.25rem 1rem\"\n    },\n    \"::before\": {\n      content: '\"\"',\n      position: \"absolute\",\n      top: \"-50%\",\n      left: \"-50%\",\n      right: \"-50%\",\n      bottom: \"-50%\",\n      background: `\n              radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)\n          `,\n      transform: \"rotate(30deg)\",\n      animation: \"shine 3s infinite\"\n    }\n  },\n  title: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    position: \"relative\",\n    textShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n  },\n  subtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    opacity: 0.9,\n    position: \"relative\",\n    fontWeight: 400\n  },\n  chatContainer: {\n    height: \"calc(100vh - 200px)\",\n    maxHeight: \"800px\",\n    minHeight: \"400px\",\n    overflowY: \"auto\",\n    padding: \"1.5rem\",\n    background: \"white\",\n    position: \"relative\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    width: \"100%\",\n    boxSizing: \"border-box\",\n    \"@media (max-width: 1024px)\": {\n      height: \"calc(100vh - 160px)\",\n      padding: \"1rem\"\n    },\n    \"@media (max-width: 768px)\": {\n      height: \"calc(100vh - 140px)\",\n      padding: \"0.75rem\",\n      minHeight: \"300px\"\n    },\n    \"@media (max-width: 480px)\": {\n      height: \"calc(100vh - 120px)\",\n      padding: \"0.5rem\",\n      minHeight: \"180px\"\n    }\n  },\n  chatMessages: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"1rem\",\n    position: \"relative\",\n    flex: 1,\n    overflowY: \"auto\",\n    paddingRight: \"0.5rem\",\n    scrollBehavior: \"smooth\",\n    width: \"100%\",\n    boxSizing: \"border-box\",\n    \"@media (max-width: 768px)\": {\n      gap: \"0.5rem\",\n      paddingRight: \"0.25rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      gap: \"0.25rem\",\n      paddingRight: 0\n    }\n  },\n  message: {\n    padding: \"1rem 1.25rem\",\n    borderRadius: \"12px\",\n    maxWidth: \"85%\",\n    width: \"fit-content\",\n    boxShadow: \"0 1px 3px rgba(0,0,0,0.1)\",\n    lineHeight: 1.6,\n    position: \"relative\",\n    transition: \"all 0.3s ease\",\n    fontSize: \"1rem\",\n    animation: \"fadeIn 0.3s ease-out\",\n    wordWrap: \"break-word\",\n    \"@media (max-width: 768px)\": {\n      maxWidth: \"90%\",\n      padding: \"0.875rem 1rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      maxWidth: \"95%\",\n      padding: \"0.75rem 0.875rem\",\n      fontSize: \"0.95rem\"\n    }\n  },\n  welcomeMessage: {\n    background: \"rgba(235, 248, 255, 0.5)\",\n    padding: \"1.5rem\",\n    borderRadius: \"12px\",\n    textAlign: \"center\",\n    margin: \"0.5rem 0 1.5rem\",\n    border: \"1px dashed #bee3f8\",\n    backdropFilter: \"blur(5px)\",\n    animation: \"fadeIn 0.8s ease-out\"\n  },\n  welcomeTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: 600,\n    color: \"#8B7355\",\n    marginBottom: \"0.75rem\"\n  },\n  welcomeText: {\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    lineHeight: 1.6\n  },\n  userMessage: {\n    alignSelf: \"flex-end\",\n    background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n    color: \"white\",\n    borderBottomRightRadius: \"4px\"\n  },\n  botMessage: {\n    alignSelf: \"flex-start\",\n    background: \"rgba(247, 250, 252, 0.8)\",\n    color: \"#2d3748\",\n    borderBottomLeftRadius: \"4px\",\n    border: \"1px solid rgba(226, 232, 240, 0.5)\",\n    backdropFilter: \"blur(5px)\"\n  },\n  messageRole: {\n    fontSize: \"0.75rem\",\n    fontWeight: 600,\n    marginBottom: \"0.5rem\",\n    opacity: 0.8,\n    textTransform: \"uppercase\",\n    letterSpacing: \"0.5px\"\n  },\n  messageContent: {\n    fontSize: \"1rem\",\n    whiteSpace: \"pre-wrap\"\n  },\n  inputContainer: {\n    display: \"flex\",\n    padding: \"1.25rem\",\n    background: \"#f7fafc\",\n    borderTop: \"1px solid rgba(226, 232, 240, 0.5)\",\n    position: \"relative\",\n    backdropFilter: \"blur(5px)\",\n    transition: \"all 0.3s ease\",\n    transform: \"translateY(0)\",\n    maxHeight: \"120px\",\n    width: \"100%\",\n    boxSizing: \"border-box\",\n    \"&.focused\": {\n      background: \"#ffffff\",\n      boxShadow: \"0 -4px 10px rgba(0, 0, 0, 0.05)\"\n    },\n    \"@media (max-width: 1024px)\": {\n      padding: \"1rem\",\n      maxHeight: \"100px\"\n    },\n    \"@media (max-width: 768px)\": {\n      padding: \"0.75rem\",\n      flexDirection: \"column\",\n      gap: \"0.75rem\",\n      maxHeight: \"90px\"\n    },\n    \"@media (max-width: 480px)\": {\n      padding: \"0.5rem\",\n      flexDirection: \"column\",\n      gap: \"0.5rem\",\n      maxHeight: \"70px\"\n    }\n  },\n  input: {\n    flex: 1,\n    padding: \"1rem 1.5rem\",\n    borderRadius: \"8px\",\n    border: \"1px solid rgba(226, 232, 240, 0.8)\",\n    outline: \"none\",\n    fontSize: \"1rem\",\n    transition: \"all 0.3s ease\",\n    background: \"rgba(255,255,255,0.8)\",\n    color: \"#2d3748\",\n    boxShadow: \"0 1px 2px rgba(0, 0, 0, 0.05)\",\n    \"&:focus\": {\n      borderColor: \"rgba(139, 115, 85, 0.5)\",\n      boxShadow: \"0 0 0 3px rgba(139, 115, 85, 0.1)\",\n      background: \"#ffffff\"\n    },\n    \"&::placeholder\": {\n      color: \"#A0AEC0\",\n      transition: \"opacity 0.3s ease\"\n    },\n    \"&:focus::placeholder\": {\n      opacity: 0.7\n    },\n    \"@media (max-width: 768px)\": {\n      padding: \"0.875rem 1.25rem\",\n      fontSize: \"0.95rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      padding: \"0.75rem 1rem\",\n      fontSize: \"0.9rem\",\n      width: \"100%\"\n    }\n  },\n  sendButton: {\n    background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n    color: \"white\",\n    border: \"none\",\n    borderRadius: \"8px\",\n    padding: \"0 1.75rem\",\n    marginLeft: \"1rem\",\n    cursor: \"pointer\",\n    fontWeight: 600,\n    fontSize: \"1rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    transition: \"all 0.3s ease\",\n    position: \"relative\",\n    overflow: \"hidden\",\n    boxShadow: \"0 2px 5px rgba(139, 115, 85, 0.3)\",\n    minWidth: \"80px\",\n    height: \"42px\",\n    \"&:hover:not(:disabled)\": {\n      transform: \"translateY(-2px)\",\n      boxShadow: \"0 4px 8px rgba(139, 115, 85, 0.4)\"\n    },\n    \"&:disabled\": {\n      background: \"#a0aec0\",\n      cursor: \"not-allowed\",\n      transform: \"none\",\n      boxShadow: \"none\",\n      opacity: 0.7\n    },\n    \"@media (max-width: 768px)\": {\n      padding: \"0 1.5rem\",\n      fontSize: \"0.95rem\",\n      height: \"40px\",\n      marginLeft: \"0.75rem\"\n    },\n    \"@media (max-width: 480px)\": {\n      marginLeft: 0,\n      width: \"100%\",\n      height: \"38px\",\n      transform: \"translateY(0) !important\",\n      opacity: \"1 !important\"\n    }\n  },\n  sendButtonLoading: {\n    width: \"24px\",\n    height: \"24px\",\n    border: \"3px solid rgba(255,255,255,0.3)\",\n    borderTopColor: \"white\",\n    borderRadius: \"50%\",\n    animation: \"spin 1s linear infinite\"\n  },\n  loadingContainer: {\n    display: \"flex\",\n    justifyContent: \"center\",\n    padding: \"1.5rem\"\n  },\n  loadingDots: {\n    display: \"flex\",\n    gap: \"0.75rem\",\n    alignItems: \"center\"\n  },\n  loadingDot: {\n    width: \"12px\",\n    height: \"12px\",\n    borderRadius: \"50%\",\n    background: \"#8B7355\",\n    animation: \"bounce 1.4s infinite ease-in-out\",\n    \":nth-child(1)\": {\n      animationDelay: \"0s\"\n    },\n    \":nth-child(2)\": {\n      animationDelay: \"0.2s\"\n    },\n    \":nth-child(3)\": {\n      animationDelay: \"0.4s\"\n    }\n  },\n  dsaContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\"\n  },\n  dsaHeader: {\n    padding: \"1.75rem 2rem\",\n    background: \"linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)\",\n    borderBottom: \"1px solid rgba(0, 0, 0, 0.05)\",\n    textAlign: \"center\"\n  },\n  dsaTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  dsaSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400\n  },\n  searchBox: {\n    position: \"relative\",\n    maxWidth: \"600px\",\n    margin: \"1.5rem auto 0\",\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  searchIcon: {\n    position: \"absolute\",\n    left: \"1rem\",\n    top: \"50%\",\n    transform: \"translateY(-50%)\",\n    color: \"#718096\"\n  },\n  searchInput: {\n    width: \"100%\",\n    padding: \"1rem 1rem 1rem 3rem\",\n    borderRadius: \"8px\",\n    border: \"1px solid #e2e8f0\",\n    outline: \"none\",\n    fontSize: \"1rem\",\n    transition: \"all 0.2s ease\",\n    background: \"white\",\n    color: \"#2d3748\",\n    boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n    \":focus\": {\n      borderColor: \"#8B7355\",\n      boxShadow: \"0 0 0 3px rgba(139, 115, 85, 0.1)\"\n    }\n  },\n  clearSearchButton: {\n    position: \"absolute\",\n    right: \"1rem\",\n    top: \"50%\",\n    transform: \"translateY(-50%)\",\n    background: \"transparent\",\n    border: \"none\",\n    cursor: \"pointer\",\n    color: \"#718096\",\n    padding: \"0.25rem\",\n    borderRadius: \"50%\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    transition: \"all 0.2s ease\",\n    \":hover\": {\n      background: \"rgba(0, 0, 0, 0.05)\"\n    }\n  },\n  companiesGrid: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fill, minmax(200px, 1fr))\",\n    gap: \"1rem\",\n    padding: \"2rem\",\n    background: \"white\"\n  },\n  companyCard: {\n    background: \"linear-gradient(135deg, #8B7355 0%, #A0956B 100%)\",\n    // mustard green gradient\n    border: \"1px solid #e2e8f0\",\n    borderRadius: \"12px\",\n    padding: \"1.25rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.3s ease\",\n    position: \"relative\",\n    overflow: \"hidden\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.03)\",\n    \":hover\": {\n      transform: \"translateY(-5px)\",\n      boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n      borderColor: \"#8B7355\"\n    }\n  },\n  companyInitial: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    background: \"linear-gradient(135deg, #6B5B47 0%, #5A4A37 100%)\",\n    color: \"white\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    fontWeight: \"bold\",\n    fontSize: \"1.2rem\",\n    marginBottom: \"1rem\"\n  },\n  companyName: {\n    fontSize: \"1rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    transition: \"all 0.3s ease\"\n  },\n  companyHoverEffect: {\n    position: \"absolute\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: \"linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)\",\n    opacity: 0,\n    transition: \"opacity 0.3s ease\",\n    \":hover\": {\n      opacity: 1\n    }\n  },\n  noResults: {\n    textAlign: \"center\",\n    padding: \"3rem\",\n    gridColumn: \"1 / -1\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  },\n  noResultsIcon: {\n    marginBottom: \"1rem\",\n    color: \"#a0aec0\"\n  },\n  noResultsText: {\n    fontSize: \"1.1rem\",\n    color: \"#4a5568\",\n    marginBottom: \"1.5rem\"\n  },\n  clearSearchButtonLarge: {\n    background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n    color: \"white\",\n    border: \"none\",\n    borderRadius: \"8px\",\n    padding: \"0.75rem 1.5rem\",\n    cursor: \"pointer\",\n    fontWeight: 600,\n    fontSize: \"1rem\",\n    transition: \"all 0.2s ease\",\n    boxShadow: \"0 2px 5px rgba(139, 115, 85, 0.3)\",\n    \":hover\": {\n      transform: \"translateY(-2px)\",\n      boxShadow: \"0 4px 8px rgba(139, 115, 85, 0.4)\"\n    }\n  },\n  quizzesContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\"\n  },\n  quizzesHeader: {\n    padding: \"1.75rem 2rem\",\n    background: \"linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)\",\n    borderBottom: \"1px solid rgba(0, 0, 0, 0.05)\",\n    textAlign: \"center\"\n  },\n  quizzesTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  quizzesSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400\n  },\n  quizCards: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fill, minmax(280px, 1fr))\",\n    gap: \"1.5rem\",\n    padding: \"2rem\",\n    background: \"white\"\n  },\n  quizCard: {\n    background: \"linear-gradient(135deg, #8B7355 0%, #A0956B 100%)\",\n    // mustard green gradient\n    border: \"1px solid #e2e8f0\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.3s ease\",\n    position: \"relative\",\n    overflow: \"hidden\",\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.03)\",\n    \":hover\": {\n      transform: \"translateY(-5px)\",\n      boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n      borderColor: \"#8B7355\"\n    }\n  },\n  quizCardContent: {\n    flex: 1\n  },\n  quizCardTitle: {\n    fontSize: \"1.1rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    marginBottom: \"0.5rem\"\n  },\n  quizCardDescription: {\n    fontSize: \"0.9rem\",\n    color: \"#718096\",\n    lineHeight: 1.5\n  },\n  quizCardArrow: {\n    color: \"#8B7355\",\n    marginLeft: \"1rem\"\n  },\n  quizCardHover: {\n    position: \"absolute\",\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    background: \"linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)\",\n    opacity: 0,\n    transition: \"opacity 0.3s ease\",\n    \":hover\": {\n      opacity: 1\n    }\n  },\n  examsContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    padding: \"2rem\",\n    textAlign: \"center\"\n  },\n  examsTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  examsSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400,\n    marginBottom: \"2rem\"\n  },\n  aptitudeContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    padding: \"2rem\",\n    textAlign: \"center\"\n  },\n  aptitudeTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  aptitudeSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400,\n    marginBottom: \"2rem\"\n  },\n  faqContainer: {\n    width: \"100%\",\n    backgroundColor: \"white\",\n    borderRadius: \"16px\",\n    overflow: \"hidden\",\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n    position: \"relative\",\n    border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    padding: \"2rem\",\n    textAlign: \"center\"\n  },\n  faqTitle: {\n    margin: 0,\n    fontSize: \"1.75rem\",\n    fontWeight: 700,\n    color: \"#2d3748\"\n  },\n  faqSubtitle: {\n    marginTop: \"0.75rem\",\n    fontSize: \"1rem\",\n    color: \"#4a5568\",\n    fontWeight: 400,\n    marginBottom: \"2rem\"\n  },\n  comingSoon: {\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    minHeight: \"300px\"\n  },\n  comingSoonContent: {\n    maxWidth: \"400px\",\n    textAlign: \"center\"\n  },\n  comingSoonIcon: {\n    fontSize: \"3rem\",\n    marginBottom: \"1rem\",\n    color: \"#FFB6B6\"\n  },\n  comingSoonText: {\n    fontSize: \"1.5rem\",\n    fontWeight: 600,\n    color: \"#2d3748\",\n    marginBottom: \"0.5rem\"\n  },\n  comingSoonDescription: {\n    fontSize: \"1rem\",\n    color: \"#718096\",\n    lineHeight: 1.6\n  },\n  \"@keyframes fadeIn\": {\n    from: {\n      opacity: 0,\n      transform: \"translateY(10px)\"\n    },\n    to: {\n      opacity: 1,\n      transform: \"translateY(0)\"\n    }\n  },\n  \"@keyframes spin\": {\n    to: {\n      transform: \"rotate(360deg)\"\n    }\n  },\n  \"@keyframes bounce\": {\n    \"0%, 80%, 100%\": {\n      transform: \"scale(0.6)\"\n    },\n    \"40%\": {\n      transform: \"scale(1)\"\n    }\n  },\n  \"@keyframes moveBackground\": {\n    \"0%\": {\n      transform: \"translate(0, 0)\"\n    },\n    \"100%\": {\n      transform: \"translate(50px, 50px)\"\n    }\n  },\n  \"@keyframes shine\": {\n    \"0%\": {\n      transform: \"rotate(30deg) translate(-30%, -30%)\"\n    },\n    \"100%\": {\n      transform: \"rotate(30deg) translate(30%, 30%)\"\n    }\n  },\n  examButtonsGrid: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n    gap: \"1.5rem\",\n    padding: \"1.5rem 0\",\n    \"@media (max-width: 768px)\": {\n      gridTemplateColumns: \"1fr\",\n      padding: \"1rem 0\"\n    }\n  },\n  examButton: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    border: \"1px solid #e2e8f0\",\n    transition: \"all 0.3s ease\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"1rem\",\n    cursor: \"pointer\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    \"&:hover\": {\n      transform: \"translateY(-4px)\",\n      boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n      borderColor: \"#FFB6B6\"\n    }\n  },\n  examIconContainer: {\n    width: \"48px\",\n    height: \"48px\",\n    borderRadius: \"12px\",\n    background: \"linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    marginBottom: \"0.5rem\"\n  },\n  examIcon: {\n    fontSize: \"24px\"\n  },\n  examContent: {\n    flex: 1\n  },\n  examTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    marginBottom: \"0.5rem\"\n  },\n  examDescription: {\n    fontSize: \"0.9rem\",\n    color: \"#718096\",\n    lineHeight: \"1.5\"\n  },\n  resourcesList: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"0.5rem\",\n    marginTop: \"1rem\"\n  },\n  resourceLink: {\n    color: \"#FFB6B6\",\n    textDecoration: \"none\",\n    fontSize: \"0.9rem\",\n    padding: \"0.5rem\",\n    borderRadius: \"6px\",\n    background: \"#FFF0F0\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFE0E0\",\n      color: \"#FF99CC\"\n    }\n  },\n  comingSoonBadge: {\n    display: \"inline-block\",\n    padding: \"0.5rem 1rem\",\n    borderRadius: \"20px\",\n    background: \"#edf2f7\",\n    color: \"#718096\",\n    fontSize: \"0.85rem\",\n    fontWeight: \"500\",\n    marginTop: \"0.5rem\"\n  },\n  selectedExamContainer: {\n    width: \"100%\",\n    padding: \"1.5rem\"\n  },\n  examHeader: {\n    marginBottom: \"2rem\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"1rem\"\n  },\n  backButton: {\n    background: \"none\",\n    border: \"none\",\n    color: \"#FFB6B6\",\n    cursor: \"pointer\",\n    fontSize: \"1rem\",\n    padding: \"0.75rem 0\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5rem\",\n    transition: \"all 0.2s ease\",\n    marginBottom: \"1rem\",\n    \"&:hover\": {\n      color: \"#FF99CC\",\n      transform: \"translateX(-4px)\"\n    }\n  },\n  selectedExamTitle: {\n    fontSize: \"2rem\",\n    fontWeight: \"700\",\n    color: \"#2d3748\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.75rem\"\n  },\n  resourceTypeFilter: {\n    display: \"flex\",\n    gap: \"0.75rem\",\n    flexWrap: \"wrap\",\n    marginBottom: \"2rem\",\n    padding: \"1rem\",\n    background: \"#f7fafc\",\n    borderRadius: \"12px\"\n  },\n  filterButton: {\n    background: \"white\",\n    border: \"1px solid #e2e8f0\",\n    borderRadius: \"8px\",\n    padding: \"0.5rem 1rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    fontSize: \"0.9rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5rem\",\n    \"&:hover\": {\n      background: \"#FFF0F0\",\n      borderColor: \"#FFB6B6\"\n    }\n  },\n  filterButtonActive: {\n    background: \"#FFB6B6\",\n    color: \"white\",\n    borderColor: \"#FFB6B6\",\n    \"&:hover\": {\n      background: \"#FF99CC\"\n    }\n  },\n  resourcesGrid: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n    gap: \"2rem\"\n  },\n  resourceCategory: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    border: \"1px solid #e2e8f0\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\"\n  },\n  categoryTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    marginBottom: \"1rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5rem\"\n  },\n  resourceCard: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"1rem\",\n    background: \"#f7fafc\",\n    borderRadius: \"8px\",\n    marginBottom: \"0.75rem\",\n    textDecoration: \"none\",\n    color: \"inherit\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFF0F0\",\n      transform: \"translateY(-2px)\"\n    }\n  },\n  resourceInfo: {\n    flex: 1\n  },\n  resourceTitle: {\n    fontSize: \"1rem\",\n    fontWeight: \"500\",\n    color: \"#2d3748\",\n    marginBottom: \"0.25rem\"\n  },\n  resourceDescription: {\n    fontSize: \"0.875rem\",\n    color: \"#718096\",\n    marginBottom: \"0.5rem\"\n  },\n  resourceYear: {\n    fontSize: \"0.75rem\",\n    color: \"#4a5568\",\n    background: \"#edf2f7\",\n    padding: \"0.25rem 0.5rem\",\n    borderRadius: \"4px\"\n  },\n  downloadIcon: {\n    fontSize: \"1.25rem\",\n    color: \"#FFB6B6\",\n    marginLeft: \"1rem\"\n  },\n  categoryIcon: {\n    fontSize: \"1.25rem\",\n    marginRight: \"0.5rem\"\n  },\n  categoryPreview: {\n    display: \"flex\",\n    gap: \"0.5rem\",\n    marginTop: \"1rem\",\n    color: \"#718096\"\n  },\n  gateContainer: {\n    width: \"100%\",\n    padding: \"1.5rem\"\n  },\n  gateHeader: {\n    marginBottom: \"2rem\"\n  },\n  gateStats: {\n    display: \"flex\",\n    gap: \"1.5rem\",\n    marginBottom: \"2rem\",\n    \"@media (max-width: 768px)\": {\n      flexDirection: \"column\",\n      gap: \"1rem\"\n    }\n  },\n  statCard: {\n    flex: 1,\n    background: \"white\",\n    padding: \"1.5rem\",\n    borderRadius: \"12px\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    gap: \"0.5rem\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    transition: \"transform 0.2s ease\",\n    \"&:hover\": {\n      transform: \"translateY(-4px)\"\n    }\n  },\n  statIcon: {\n    fontSize: \"2rem\"\n  },\n  statValue: {\n    fontSize: \"1.5rem\",\n    fontWeight: \"bold\",\n    color: \"#2d3748\"\n  },\n  statLabel: {\n    fontSize: \"0.9rem\",\n    color: \"#718096\"\n  },\n  gateFilters: {\n    display: \"flex\",\n    gap: \"1rem\",\n    marginBottom: \"2rem\",\n    \"@media (max-width: 768px)\": {\n      flexDirection: \"column\"\n    }\n  },\n  gateSelect: {\n    padding: \"0.75rem 1rem\",\n    borderRadius: \"8px\",\n    border: \"1px solid #e2e8f0\",\n    background: \"white\",\n    fontSize: \"0.95rem\",\n    color: \"#2d3748\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    \"&:focus\": {\n      outline: \"none\",\n      borderColor: \"#FFB6B6\",\n      boxShadow: \"0 0 0 3px rgba(255, 182, 182, 0.1)\"\n    }\n  },\n  gateContent: {\n    display: \"flex\",\n    gap: \"2rem\",\n    \"@media (max-width: 1024px)\": {\n      flexDirection: \"column\"\n    }\n  },\n  gateSidebar: {\n    width: \"300px\",\n    flexShrink: 0,\n    \"@media (max-width: 1024px)\": {\n      width: \"100%\"\n    }\n  },\n  sidebarSection: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    marginBottom: \"1.5rem\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\"\n  },\n  sidebarTitle: {\n    fontSize: \"1.1rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    marginBottom: \"1rem\"\n  },\n  quickLinks: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"0.75rem\"\n  },\n  quickLink: {\n    color: \"#FFB6B6\",\n    textDecoration: \"none\",\n    fontSize: \"0.95rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5rem\",\n    padding: \"0.5rem\",\n    borderRadius: \"6px\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFF0F0\"\n    }\n  },\n  datesList: {\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"1rem\"\n  },\n  dateItem: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"0.75rem\",\n    background: \"#f7fafc\",\n    borderRadius: \"8px\"\n  },\n  dateLabel: {\n    fontSize: \"0.9rem\",\n    color: \"#4a5568\"\n  },\n  dateValue: {\n    fontSize: \"0.9rem\",\n    fontWeight: \"500\",\n    color: \"#2d3748\"\n  },\n  gateMainContent: {\n    flex: 1\n  },\n  subjectsGrid: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fill, minmax(250px, 1fr))\",\n    gap: \"1.5rem\"\n  },\n  subjectCard: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"1.5rem\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    gap: \"1rem\",\n    cursor: \"pointer\",\n    transition: \"all 0.3s ease\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    \"&:hover\": {\n      transform: \"translateY(-4px)\",\n      boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\"\n    }\n  },\n  subjectIcon: {\n    fontSize: \"2.5rem\"\n  },\n  subjectName: {\n    fontSize: \"1.1rem\",\n    fontWeight: \"600\",\n    color: \"#2d3748\",\n    textAlign: \"center\"\n  },\n  subjectTopics: {\n    fontSize: \"0.9rem\",\n    color: \"#718096\"\n  },\n  subjectContent: {\n    background: \"white\",\n    borderRadius: \"12px\",\n    padding: \"2rem\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\"\n  },\n  subjectTitle: {\n    fontSize: \"1.5rem\",\n    fontWeight: \"700\",\n    color: \"#2d3748\",\n    marginBottom: \"2rem\"\n  },\n  topicsList: {\n    display: \"grid\",\n    gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\",\n    gap: \"1.5rem\"\n  },\n  topicCard: {\n    background: \"#f7fafc\",\n    borderRadius: \"10px\",\n    padding: \"1.25rem\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFF0F0\"\n    }\n  },\n  topicTitle: {\n    fontSize: \"1rem\",\n    fontWeight: \"500\",\n    color: \"#2d3748\",\n    marginBottom: \"1rem\"\n  },\n  topicResources: {\n    display: \"flex\",\n    gap: \"0.75rem\"\n  },\n  resourceButton: {\n    background: \"white\",\n    border: \"1px solid #e2e8f0\",\n    borderRadius: \"6px\",\n    padding: \"0.5rem 0.75rem\",\n    fontSize: \"0.9rem\",\n    color: \"#4a5568\",\n    cursor: \"pointer\",\n    transition: \"all 0.2s ease\",\n    \"&:hover\": {\n      background: \"#FFB6B6\",\n      color: \"white\",\n      borderColor: \"#FFB6B6\"\n    }\n  },\n  leftLogoContainer: {\n    display: \"flex\",\n    alignItems: \"center\",\n    marginRight: \"1rem\"\n  },\n  eduaiLogoImage: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    objectFit: \"cover\",\n    border: \"2px solid #FF99CC\",\n    boxShadow: \"0 2px 4px rgba(255, 153, 204, 0.2)\"\n  },\n  userAvatar: {\n    width: '40px',\n    height: '40px',\n    borderRadius: '50%',\n    background: '#f0f4f8',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontWeight: 700,\n    fontSize: '1.1rem',\n    marginRight: '0.75rem',\n    border: '2px solid #FF99CC',\n    boxShadow: '0 2px 4px rgba(255,153,204,0.1)'\n  },\n  userAvatarLarge: {\n    width: '56px',\n    height: '56px',\n    borderRadius: '50%',\n    background: '#f0f4f8',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontWeight: 700,\n    fontSize: '2rem',\n    border: '2px solid #FF99CC',\n    boxShadow: '0 2px 4px rgba(255,153,204,0.1)'\n  }\n};\nexport default styles;", "map": {"version": 3, "names": ["themes", "mustardGreen", "primary", "primaryDark", "primaryLight", "secondary", "accent", "background", "surface", "text", "textLight", "textDark", "border", "shadow", "shadowDark", "gradient", "gradientReverse", "gradientLight", "redTheme", "softRedGradient", "darkMode", "currentTheme", "getTheme", "themeName", "styles", "minHeight", "backgroundColor", "display", "flexDirection", "fontFamily", "position", "overflowX", "animatedBackground", "top", "left", "right", "bottom", "zIndex", "content", "backgroundImage", "animation", "navbar", "width", "boxShadow", "padding", "<PERSON><PERSON>ilter", "borderBottom", "navContainer", "max<PERSON><PERSON><PERSON>", "margin", "justifyContent", "alignItems", "menuButton", "cursor", "color", "borderRadius", "transition", "centerTitleContainer", "flex", "textAlign", "mainTitle", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "letterSpacing", "textTransform", "subTitle", "marginTop", "rightLogoContainer", "logoImage", "height", "objectFit", "sidebar", "overflowY", "sidebarHeader", "sidebarTitle", "closeSidebarButton", "sidebarContent", "sidebarItemGroup", "marginBottom", "sidebarItem", "sidebarItemActive", "sidebarIcon", "marginRight", "sidebarItemText", "sidebarExpandIcon", "subItemsContainer", "paddingLeft", "overflow", "subItem", "sidebarFooter", "borderTop", "sidebarFooterText", "overlay", "mainContainer", "<PERSON><PERSON><PERSON><PERSON>", "gridOverlay", "backgroundSize", "pointerEvents", "header", "transform", "title", "textShadow", "subtitle", "opacity", "chatContainer", "maxHeight", "boxSizing", "chatMessages", "gap", "paddingRight", "scroll<PERSON>eh<PERSON>or", "message", "lineHeight", "wordWrap", "welcomeMessage", "welcomeTitle", "welcomeText", "userMessage", "alignSelf", "borderBottomRightRadius", "botMessage", "borderBottomLeftRadius", "messageRole", "messageContent", "whiteSpace", "inputContainer", "input", "outline", "borderColor", "sendButton", "marginLeft", "min<PERSON><PERSON><PERSON>", "sendButtonLoading", "borderTopColor", "loadingContainer", "loadingDots", "loadingDot", "animationDelay", "dsa<PERSON><PERSON><PERSON>", "d<PERSON><PERSON><PERSON><PERSON>", "dsa<PERSON>itle", "dsaSubtitle", "searchBox", "searchIcon", "searchInput", "clearSearchButton", "companiesGrid", "gridTemplateColumns", "companyCard", "companyInitial", "companyName", "companyHoverEffect", "noResults", "gridColumn", "noResultsIcon", "noResultsText", "clearSearchButtonLarge", "quizzes<PERSON><PERSON><PERSON>", "quizzes<PERSON><PERSON><PERSON>", "quizzesTitle", "quizzesSubtitle", "quizCards", "quizCard", "quiz<PERSON><PERSON><PERSON><PERSON><PERSON>", "quizCardTitle", "quizCardDescription", "quizCardArrow", "quizCardHover", "<PERSON><PERSON><PERSON><PERSON>", "examsTitle", "examsSubtitle", "aptitudeC<PERSON>r", "aptitudeTitle", "aptitudeSubtitle", "faq<PERSON><PERSON><PERSON>", "faqTitle", "faqSubtitle", "comingSoon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comingSoonIcon", "comingSoonText", "comingSoonDescription", "from", "to", "examButtonsGrid", "examButton", "examIconContainer", "examIcon", "examContent", "examTitle", "examDescription", "resourcesList", "resourceLink", "textDecoration", "comingSoonBadge", "selected<PERSON><PERSON>mContainer", "<PERSON><PERSON><PERSON><PERSON>", "backButton", "selectedExamTitle", "resourceTypeFilter", "flexWrap", "filterButton", "filterButtonActive", "resourcesGrid", "resourceCategory", "categoryTitle", "resourceCard", "resourceInfo", "resourceTitle", "resourceDescription", "resourceYear", "downloadIcon", "categoryIcon", "categoryPreview", "gateContainer", "gateHeader", "gateStats", "statCard", "statIcon", "statValue", "statLabel", "gateFilters", "gateSelect", "gateContent", "gateSidebar", "flexShrink", "sidebarSection", "quickLinks", "quickLink", "datesList", "dateItem", "<PERSON><PERSON><PERSON><PERSON>", "dateValue", "gateMain<PERSON><PERSON>nt", "subjectsGrid", "subjectCard", "subjectIcon", "subjectName", "subjectTopics", "subjectContent", "subjectTitle", "topicsList", "topicCard", "topicTitle", "topicResources", "resourceButton", "leftLogoContainer", "eduaiLogoImage", "userAvatar", "userAvatarLarge"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/src/styles.js"], "sourcesContent": ["// styles.js - Centralized Theme System\n\n// Color Themes Configuration\nconst themes = {\n  mustardGreen: {\n    primary: '#8B7355',\n    primaryDark: '#6B5B47',\n    primaryLight: '#A0956B',\n    secondary: '#F5F3F0',\n    accent: '#9A8B73',\n    background: '#f0f4f8',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(139, 115, 85, 0.1)',\n    shadowDark: 'rgba(139, 115, 85, 0.3)',\n    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',\n    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',\n    gradientLight: 'linear-gradient(135deg, #F5F3F0 0%, #E8E2D5 100%)',\n  },\n  redTheme: {\n    primary: '#DC2626',\n    primaryDark: '#B91C1C',\n    primaryLight: '#EF4444',\n    secondary: '#FEE2E2',\n    accent: '#F87171',\n    background: '#FEF2F2',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(220, 38, 38, 0.1)',\n    shadowDark: 'rgba(220, 38, 38, 0.3)',\n    gradient: 'linear-gradient(135deg, #DC2626 0%, #EF4444 100%)',\n    gradientReverse: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',\n    gradientLight: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n  },\n  softRedGradient: {\n    primary: '#FFB6B6',\n    primaryDark: '#FF99CC',\n    primaryLight: '#FFC5C5',\n    secondary: '#FFF0F0',\n    accent: '#FFE0E0',\n    background: '#FFF5F5',\n    surface: '#ffffff',\n    text: '#2d3748',\n    textLight: '#718096',\n    textDark: '#1a202c',\n    border: '#e2e8f0',\n    shadow: 'rgba(255, 182, 182, 0.1)',\n    shadowDark: 'rgba(255, 182, 182, 0.3)',\n    gradient: 'linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)',\n    gradientReverse: 'linear-gradient(135deg, #FFB6B6 0%, #FF99CC 100%)',\n    gradientLight: 'linear-gradient(135deg, #FFF0F0 0%, #FFE0E0 100%)',\n  },\n  darkMode: {\n    primary: '#8B7355',\n    primaryDark: '#6B5B47',\n    primaryLight: '#A0956B',\n    secondary: '#252525',\n    accent: '#333333',\n    background: '#121212',\n    surface: '#1e1e1e',\n    text: '#e0e0e0',\n    textLight: '#a0a0a0',\n    textDark: '#ffffff',\n    border: '#333333',\n    shadow: 'rgba(0, 0, 0, 0.3)',\n    shadowDark: 'rgba(0, 0, 0, 0.5)',\n    gradient: 'linear-gradient(135deg, #8B7355 0%, #A0956B 100%)',\n    gradientReverse: 'linear-gradient(135deg, #A0956B 0%, #8B7355 100%)',\n    gradientLight: 'linear-gradient(135deg, #252525 0%, #333333 100%)',\n  }\n};\n\n// Current theme selector - can be changed to switch themes\nconst currentTheme = themes.mustardGreen; // Change this to switch themes globally\n\n// Helper function to get theme colors\nconst getTheme = (themeName = 'mustardGreen') => {\n  return themes[themeName] || themes.mustardGreen;\n};\n\nconst styles = {\n  // Theme configuration\n  themes,\n  currentTheme,\n  getTheme,\n\n  // Base styles\n  background: {\n    minHeight: \"100vh\",\n    backgroundColor: currentTheme.background,\n    display: \"flex\",\n    flexDirection: \"column\",\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, sans-serif\",\n    position: \"relative\",\n    overflowX: \"hidden\",\n  },\n    animatedBackground: {\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: \"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)\",\n      zIndex: 0,\n      \"::before\": {\n        content: '\"\"',\n        position: \"absolute\",\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 10% 20%, rgba(255, 94, 94, 0.1) 0%, transparent 20%),\n          radial-gradient(circle at 90% 30%, rgba(94, 163, 255, 0.1) 0%, transparent 25%),\n          radial-gradient(circle at 50% 80%, rgba(255, 215, 94, 0.1) 0%, transparent 20%)\n        `,\n        animation: \"moveBackground 20s infinite alternate\",\n      },\n    },\n    navbar: {\n      width: \"100%\",\n      background: currentTheme.gradient,\n      boxShadow: `0 4px 6px ${currentTheme.shadow}`,\n      padding: \"0.75rem 2rem\",\n      position: \"sticky\",\n      top: 0,\n      zIndex: 1000,\n      backdropFilter: \"blur(8px)\",\n      borderBottom: `1px solid ${currentTheme.border}`,\n    },\n    navContainer: {\n      width: \"100%\",\n      maxWidth: \"1400px\",\n      margin: \"0 auto\",\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n    },\n    menuButton: {\n      background: \"transparent\",\n      border: \"none\",\n      cursor: \"pointer\",\n      color: \"#4a5568\",\n      padding: \"0.5rem\",\n      borderRadius: \"8px\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      transition: \"all 0.2s ease\",\n      \":hover\": {\n        background: \"rgba(0, 0, 0, 0.05)\",\n        color: \"#3182ce\",\n      },\n      \"@media (min-width: 768px)\": {\n        display: \"none\",\n      },\n    },\n    centerTitleContainer: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      flex: 1,\n      textAlign: \"center\",\n    },\n    mainTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: \"bold\",\n      background: currentTheme.gradient,\n      WebkitBackgroundClip: \"text\",\n      WebkitTextFillColor: \"transparent\",\n      letterSpacing: \"0.5px\",\n      textTransform: \"uppercase\",\n    },\n    subTitle: {\n      fontSize: \"0.75rem\",\n      color: currentTheme.textLight,\n      marginTop: \"0.25rem\",\n      fontWeight: 500,\n      letterSpacing: \"0.5px\",\n    },\n    rightLogoContainer: {\n      display: \"flex\",\n      alignItems: \"center\",\n    },\n    logoImage: {\n      width: \"40px\",\n      height: \"40px\",\n      borderRadius: \"50%\",\n      objectFit: \"cover\",\n      border: `2px solid ${currentTheme.primary}`,\n      boxShadow: `0 2px 4px ${currentTheme.shadow}`,\n    },\n    sidebar: {\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      width: \"280px\",\n      height: \"100vh\",\n      backgroundColor: currentTheme.surface,\n      boxShadow: `4px 0 15px ${currentTheme.shadow}`,\n      zIndex: 1100,\n      transition: \"transform 0.3s ease-in-out\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      overflowY: \"auto\",\n    },\n    sidebarHeader: {\n      padding: \"1.5rem 1.5rem 1rem\",\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      borderBottom: `1px solid ${currentTheme.border}`,\n    },\n    sidebarTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: \"600\",\n      color: currentTheme.text,\n    },\n    closeSidebarButton: {\n      background: \"transparent\",\n      border: \"none\",\n      cursor: \"pointer\",\n      color: \"#4a5568\",\n      padding: \"0.25rem\",\n      borderRadius: \"4px\",\n      transition: \"all 0.2s ease\",\n      \":hover\": {\n        background: \"rgba(0, 0, 0, 0.05)\",\n        color: \"#3182ce\",\n      },\n    },\n    sidebarContent: {\n      flex: 1,\n      padding: \"1rem 0\",\n      overflowY: \"auto\",\n    },\n    sidebarItemGroup: {\n      marginBottom: \"0.5rem\",\n    },\n    sidebarItem: {\n      display: \"flex\",\n      alignItems: \"center\",\n      padding: \"0.75rem 1.5rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      position: \"relative\",\n      \":hover\": {\n        background: `${currentTheme.shadow}`,\n        color: currentTheme.primary,\n      },\n    },\n    sidebarItemActive: {\n      background: `${currentTheme.shadow}`,\n      color: currentTheme.primary,\n      fontWeight: \"500\",\n    },\n    sidebarIcon: {\n      marginRight: \"1rem\",\n      fontSize: \"1.1rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n    },\n    sidebarItemText: {\n      flex: 1,\n    },\n    sidebarExpandIcon: {\n      transition: \"transform 0.2s ease\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      padding: \"0.25rem\",\n      borderRadius: \"4px\",\n      \":hover\": {\n        background: \"rgba(0, 0, 0, 0.05)\",\n      },\n    },\n    subItemsContainer: {\n      paddingLeft: \"2.5rem\",\n      overflow: \"hidden\",\n      transition: \"all 0.3s ease\",\n    },\n    subItem: {\n      padding: \"0.65rem 1.5rem\",\n      fontSize: \"0.9rem\",\n      color: \"#4a5568\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      \":hover\": {\n        background: \"rgba(139, 115, 85, 0.05)\",\n        color: \"#8B7355\",\n      },\n    },\n    sidebarFooter: {\n      padding: \"1.5rem\",\n      borderTop: \"1px solid rgba(0, 0, 0, 0.05)\",\n      textAlign: \"center\",\n    },\n    sidebarFooterText: {\n      fontSize: \"0.8rem\",\n      color: \"#718096\",\n    },\n    overlay: {\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n      zIndex: 1090,\n      \"@media (min-width: 768px)\": {\n        display: \"none\",\n      },\n    },\n    mainContainer: {\n      width: \"100%\",\n      maxWidth: \"1400px\",\n      margin: \"2rem auto\",\n      padding: \"0 2rem\",\n      flex: 1,\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"flex-start\",\n      position: \"relative\",\n      zIndex: 1,\n      \"@media (max-width: 768px)\": {\n        margin: \"1rem auto\",\n        padding: \"0 1rem\",\n      },\n    },\n    resumeContainer: {\n      width: \"100%\",\n      maxWidth: \"800px\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      height: \"calc(100vh - 120px)\",\n      \"@media (max-width: 768px)\": {\n          height: \"calc(100vh - 100px)\",\n          borderRadius: \"12px\",\n      },\n      \"@media (max-width: 480px)\": {\n          height: \"calc(100vh - 80px)\",\n          borderRadius: \"8px\",\n      }\n  },\n  gridOverlay: {\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundImage: `\n        linear-gradient(rgba(139, 115, 85, 0.03) 1px, transparent 1px),\n        linear-gradient(90deg, rgba(139, 115, 85, 0.03) 1px, transparent 1px)\n      `,\n      backgroundSize: \"20px 20px\",\n      pointerEvents: \"none\",\n    },\n    header: {\n      background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n      color: \"white\",\n      padding: \"1.75rem 2rem\",\n      textAlign: \"center\",\n      position: \"relative\",\n      overflow: \"hidden\",\n      \"@media (max-width: 768px)\": {\n          padding: \"1.5rem 1.5rem\",\n      },\n      \"@media (max-width: 480px)\": {\n          padding: \"1.25rem 1rem\",\n      },\n      \"::before\": {\n          content: '\"\"',\n          position: \"absolute\",\n          top: \"-50%\",\n          left: \"-50%\",\n          right: \"-50%\",\n          bottom: \"-50%\",\n          background: `\n              radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)\n          `,\n          transform: \"rotate(30deg)\",\n          animation: \"shine 3s infinite\",\n      }\n  },\n  title: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      position: \"relative\",\n      textShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n    },\n    subtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      opacity: 0.9,\n      position: \"relative\",\n      fontWeight: 400,\n    },\n    chatContainer: {\n      height: \"calc(100vh - 200px)\",\n      maxHeight: \"800px\",\n      minHeight: \"400px\",\n      overflowY: \"auto\",\n      padding: \"1.5rem\",\n      background: \"white\",\n      position: \"relative\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      width: \"100%\",\n      boxSizing: \"border-box\",\n      \"@media (max-width: 1024px)\": {\n        height: \"calc(100vh - 160px)\",\n        padding: \"1rem\",\n      },\n      \"@media (max-width: 768px)\": {\n        height: \"calc(100vh - 140px)\",\n        padding: \"0.75rem\",\n        minHeight: \"300px\",\n      },\n      \"@media (max-width: 480px)\": {\n        height: \"calc(100vh - 120px)\",\n        padding: \"0.5rem\",\n        minHeight: \"180px\",\n      }\n    },\n    chatMessages: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"1rem\",\n      position: \"relative\",\n      flex: 1,\n      overflowY: \"auto\",\n      paddingRight: \"0.5rem\",\n      scrollBehavior: \"smooth\",\n      width: \"100%\",\n      boxSizing: \"border-box\",\n      \"@media (max-width: 768px)\": {\n        gap: \"0.5rem\",\n        paddingRight: \"0.25rem\",\n      },\n      \"@media (max-width: 480px)\": {\n        gap: \"0.25rem\",\n        paddingRight: 0,\n      }\n    },\n    message: {\n      padding: \"1rem 1.25rem\",\n      borderRadius: \"12px\",\n      maxWidth: \"85%\",\n      width: \"fit-content\",\n      boxShadow: \"0 1px 3px rgba(0,0,0,0.1)\",\n      lineHeight: 1.6,\n      position: \"relative\",\n      transition: \"all 0.3s ease\",\n      fontSize: \"1rem\",\n      animation: \"fadeIn 0.3s ease-out\",\n      wordWrap: \"break-word\",\n      \"@media (max-width: 768px)\": {\n          maxWidth: \"90%\",\n          padding: \"0.875rem 1rem\",\n      },\n      \"@media (max-width: 480px)\": {\n          maxWidth: \"95%\",\n          padding: \"0.75rem 0.875rem\",\n          fontSize: \"0.95rem\",\n      }\n    },\n    welcomeMessage: {\n      background: \"rgba(235, 248, 255, 0.5)\",\n      padding: \"1.5rem\",\n      borderRadius: \"12px\",\n      textAlign: \"center\",\n      margin: \"0.5rem 0 1.5rem\",\n      border: \"1px dashed #bee3f8\",\n      backdropFilter: \"blur(5px)\",\n      animation: \"fadeIn 0.8s ease-out\",\n    },\n    welcomeTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: 600,\n      color: \"#8B7355\",\n      marginBottom: \"0.75rem\",\n    },\n    welcomeText: {\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      lineHeight: 1.6,\n    },\n    userMessage: {\n      alignSelf: \"flex-end\",\n      background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n      color: \"white\",\n      borderBottomRightRadius: \"4px\",\n    },\n    botMessage: {\n      alignSelf: \"flex-start\",\n      background: \"rgba(247, 250, 252, 0.8)\",\n      color: \"#2d3748\",\n      borderBottomLeftRadius: \"4px\",\n      border: \"1px solid rgba(226, 232, 240, 0.5)\",\n      backdropFilter: \"blur(5px)\",\n    },\n    messageRole: {\n      fontSize: \"0.75rem\",\n      fontWeight: 600,\n      marginBottom: \"0.5rem\",\n      opacity: 0.8,\n      textTransform: \"uppercase\",\n      letterSpacing: \"0.5px\",\n    },\n    messageContent: {\n      fontSize: \"1rem\",\n      whiteSpace: \"pre-wrap\",\n    },\n    inputContainer: {\n      display: \"flex\",\n      padding: \"1.25rem\",\n      background: \"#f7fafc\",\n      borderTop: \"1px solid rgba(226, 232, 240, 0.5)\",\n      position: \"relative\",\n      backdropFilter: \"blur(5px)\",\n      transition: \"all 0.3s ease\",\n      transform: \"translateY(0)\",\n      maxHeight: \"120px\",\n      width: \"100%\",\n      boxSizing: \"border-box\",\n      \"&.focused\": {\n        background: \"#ffffff\",\n        boxShadow: \"0 -4px 10px rgba(0, 0, 0, 0.05)\",\n      },\n      \"@media (max-width: 1024px)\": {\n        padding: \"1rem\",\n        maxHeight: \"100px\",\n      },\n      \"@media (max-width: 768px)\": {\n        padding: \"0.75rem\",\n        flexDirection: \"column\",\n        gap: \"0.75rem\",\n        maxHeight: \"90px\",\n      },\n      \"@media (max-width: 480px)\": {\n        padding: \"0.5rem\",\n        flexDirection: \"column\",\n        gap: \"0.5rem\",\n        maxHeight: \"70px\",\n      }\n    },\n    input: {\n      flex: 1,\n      padding: \"1rem 1.5rem\",\n      borderRadius: \"8px\",\n      border: \"1px solid rgba(226, 232, 240, 0.8)\",\n      outline: \"none\",\n      fontSize: \"1rem\",\n      transition: \"all 0.3s ease\",\n      background: \"rgba(255,255,255,0.8)\",\n      color: \"#2d3748\",\n      boxShadow: \"0 1px 2px rgba(0, 0, 0, 0.05)\",\n      \"&:focus\": {\n        borderColor: \"rgba(139, 115, 85, 0.5)\",\n        boxShadow: \"0 0 0 3px rgba(139, 115, 85, 0.1)\",\n        background: \"#ffffff\",\n      },\n      \"&::placeholder\": {\n        color: \"#A0AEC0\",\n        transition: \"opacity 0.3s ease\",\n      },\n      \"&:focus::placeholder\": {\n        opacity: 0.7,\n      },\n      \"@media (max-width: 768px)\": {\n        padding: \"0.875rem 1.25rem\",\n        fontSize: \"0.95rem\",\n      },\n      \"@media (max-width: 480px)\": {\n        padding: \"0.75rem 1rem\",\n        fontSize: \"0.9rem\",\n        width: \"100%\",\n      }\n    },\n    sendButton: {\n      background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n      color: \"white\",\n      border: \"none\",\n      borderRadius: \"8px\",\n      padding: \"0 1.75rem\",\n      marginLeft: \"1rem\",\n      cursor: \"pointer\",\n      fontWeight: 600,\n      fontSize: \"1rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      transition: \"all 0.3s ease\",\n      position: \"relative\",\n      overflow: \"hidden\",\n      boxShadow: \"0 2px 5px rgba(139, 115, 85, 0.3)\",\n      minWidth: \"80px\",\n      height: \"42px\",\n      \"&:hover:not(:disabled)\": {\n        transform: \"translateY(-2px)\",\n        boxShadow: \"0 4px 8px rgba(139, 115, 85, 0.4)\",\n      },\n      \"&:disabled\": {\n        background: \"#a0aec0\",\n        cursor: \"not-allowed\",\n        transform: \"none\",\n        boxShadow: \"none\",\n        opacity: 0.7,\n      },\n      \"@media (max-width: 768px)\": {\n        padding: \"0 1.5rem\",\n        fontSize: \"0.95rem\",\n        height: \"40px\",\n        marginLeft: \"0.75rem\",\n      },\n      \"@media (max-width: 480px)\": {\n        marginLeft: 0,\n        width: \"100%\",\n        height: \"38px\",\n        transform: \"translateY(0) !important\",\n        opacity: \"1 !important\",\n      }\n    },\n    sendButtonLoading: {\n      width: \"24px\",\n      height: \"24px\",\n      border: \"3px solid rgba(255,255,255,0.3)\",\n      borderTopColor: \"white\",\n      borderRadius: \"50%\",\n      animation: \"spin 1s linear infinite\",\n    },\n    loadingContainer: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      padding: \"1.5rem\",\n    },\n    loadingDots: {\n      display: \"flex\",\n      gap: \"0.75rem\",\n      alignItems: \"center\",\n    },\n    loadingDot: {\n      width: \"12px\",\n      height: \"12px\",\n      borderRadius: \"50%\",\n      background: \"#8B7355\",\n      animation: \"bounce 1.4s infinite ease-in-out\",\n      \":nth-child(1)\": {\n        animationDelay: \"0s\",\n      },\n      \":nth-child(2)\": {\n        animationDelay: \"0.2s\",\n      },\n      \":nth-child(3)\": {\n        animationDelay: \"0.4s\",\n      },\n    },\n    dsaContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    },\n    dsaHeader: {\n      padding: \"1.75rem 2rem\",\n      background: \"linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)\",\n      borderBottom: \"1px solid rgba(0, 0, 0, 0.05)\",\n      textAlign: \"center\",\n    },\n    dsaTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    dsaSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n    },\n    searchBox: {\n      position: \"relative\",\n      maxWidth: \"600px\",\n      margin: \"1.5rem auto 0\",\n      display: \"flex\",\n      alignItems: \"center\",\n    },\n    searchIcon: {\n      position: \"absolute\",\n      left: \"1rem\",\n      top: \"50%\",\n      transform: \"translateY(-50%)\",\n      color: \"#718096\",\n    },\n    searchInput: {\n      width: \"100%\",\n      padding: \"1rem 1rem 1rem 3rem\",\n      borderRadius: \"8px\",\n      border: \"1px solid #e2e8f0\",\n      outline: \"none\",\n      fontSize: \"1rem\",\n      transition: \"all 0.2s ease\",\n      background: \"white\",\n      color: \"#2d3748\",\n      boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n      \":focus\": {\n        borderColor: \"#8B7355\",\n        boxShadow: \"0 0 0 3px rgba(139, 115, 85, 0.1)\",\n      },\n    },\n    clearSearchButton: {\n      position: \"absolute\",\n      right: \"1rem\",\n      top: \"50%\",\n      transform: \"translateY(-50%)\",\n      background: \"transparent\",\n      border: \"none\",\n      cursor: \"pointer\",\n      color: \"#718096\",\n      padding: \"0.25rem\",\n      borderRadius: \"50%\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      transition: \"all 0.2s ease\",\n      \":hover\": {\n        background: \"rgba(0, 0, 0, 0.05)\",\n      },\n    },\n    companiesGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fill, minmax(200px, 1fr))\",\n      gap: \"1rem\",\n      padding: \"2rem\",\n      background: \"white\",\n    },\n    companyCard: {\n      background: \"linear-gradient(135deg, #8B7355 0%, #A0956B 100%)\", // mustard green gradient\n      border: \"1px solid #e2e8f0\",\n      borderRadius: \"12px\",\n      padding: \"1.25rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.3s ease\",\n      position: \"relative\",\n      overflow: \"hidden\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.03)\",\n      \":hover\": {\n        transform: \"translateY(-5px)\",\n        boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n        borderColor: \"#8B7355\",\n      },\n    },\n    companyInitial: {\n      width: \"40px\",\n      height: \"40px\",\n      borderRadius: \"50%\",\n      background: \"linear-gradient(135deg, #6B5B47 0%, #5A4A37 100%)\",\n      color: \"white\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      fontWeight: \"bold\",\n      fontSize: \"1.2rem\",\n      marginBottom: \"1rem\",\n    },\n    companyName: {\n      fontSize: \"1rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      transition: \"all 0.3s ease\",\n    },\n    companyHoverEffect: {\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: \"linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)\",\n      opacity: 0,\n      transition: \"opacity 0.3s ease\",\n      \":hover\": {\n        opacity: 1,\n      },\n    },\n    noResults: {\n      textAlign: \"center\",\n      padding: \"3rem\",\n      gridColumn: \"1 / -1\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n    },\n    noResultsIcon: {\n      marginBottom: \"1rem\",\n      color: \"#a0aec0\",\n    },\n    noResultsText: {\n      fontSize: \"1.1rem\",\n      color: \"#4a5568\",\n      marginBottom: \"1.5rem\",\n    },\n    clearSearchButtonLarge: {\n      background: \"linear-gradient(135deg, #8B7355 0%, #6B5B47 100%)\",\n      color: \"white\",\n      border: \"none\",\n      borderRadius: \"8px\",\n      padding: \"0.75rem 1.5rem\",\n      cursor: \"pointer\",\n      fontWeight: 600,\n      fontSize: \"1rem\",\n      transition: \"all 0.2s ease\",\n      boxShadow: \"0 2px 5px rgba(139, 115, 85, 0.3)\",\n      \":hover\": {\n        transform: \"translateY(-2px)\",\n        boxShadow: \"0 4px 8px rgba(139, 115, 85, 0.4)\",\n      },\n    },\n    quizzesContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n    },\n    quizzesHeader: {\n      padding: \"1.75rem 2rem\",\n      background: \"linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)\",\n      borderBottom: \"1px solid rgba(0, 0, 0, 0.05)\",\n      textAlign: \"center\",\n    },\n    quizzesTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    quizzesSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n    },\n    quizCards: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fill, minmax(280px, 1fr))\",\n      gap: \"1.5rem\",\n      padding: \"2rem\",\n      background: \"white\",\n    },\n    quizCard: {\n      background: \"linear-gradient(135deg, #8B7355 0%, #A0956B 100%)\", // mustard green gradient\n      border: \"1px solid #e2e8f0\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.3s ease\",\n      position: \"relative\",\n      overflow: \"hidden\",\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.03)\",\n      \":hover\": {\n        transform: \"translateY(-5px)\",\n        boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n        borderColor: \"#8B7355\",\n      },\n    },\n    quizCardContent: {\n      flex: 1,\n    },\n    quizCardTitle: {\n      fontSize: \"1.1rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      marginBottom: \"0.5rem\",\n    },\n    quizCardDescription: {\n      fontSize: \"0.9rem\",\n      color: \"#718096\",\n      lineHeight: 1.5,\n    },\n    quizCardArrow: {\n      color: \"#8B7355\",\n      marginLeft: \"1rem\",\n    },\n    quizCardHover: {\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: \"linear-gradient(135deg, rgba(139, 115, 85, 0.1) 0%, rgba(160, 149, 107, 0.1) 100%)\",\n      opacity: 0,\n      transition: \"opacity 0.3s ease\",\n      \":hover\": {\n        opacity: 1,\n      },\n    },\n    examsContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n      padding: \"2rem\",\n      textAlign: \"center\",\n    },\n    examsTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    examsSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n      marginBottom: \"2rem\",\n    },\n    aptitudeContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n      padding: \"2rem\",\n      textAlign: \"center\",\n    },\n    aptitudeTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    aptitudeSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n      marginBottom: \"2rem\",\n    },\n    faqContainer: {\n      width: \"100%\",\n      backgroundColor: \"white\",\n      borderRadius: \"16px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.08)\",\n      position: \"relative\",\n      border: \"1px solid rgba(0, 0, 0, 0.05)\",\n      padding: \"2rem\",\n      textAlign: \"center\",\n    },\n    faqTitle: {\n      margin: 0,\n      fontSize: \"1.75rem\",\n      fontWeight: 700,\n      color: \"#2d3748\",\n    },\n    faqSubtitle: {\n      marginTop: \"0.75rem\",\n      fontSize: \"1rem\",\n      color: \"#4a5568\",\n      fontWeight: 400,\n      marginBottom: \"2rem\",\n    },\n    comingSoon: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"300px\",\n    },\n    comingSoonContent: {\n      maxWidth: \"400px\",\n      textAlign: \"center\",\n    },\n    comingSoonIcon: {\n      fontSize: \"3rem\",\n      marginBottom: \"1rem\",\n      color: \"#FFB6B6\",\n    },\n    comingSoonText: {\n      fontSize: \"1.5rem\",\n      fontWeight: 600,\n      color: \"#2d3748\",\n      marginBottom: \"0.5rem\",\n    },\n    comingSoonDescription: {\n      fontSize: \"1rem\",\n      color: \"#718096\",\n      lineHeight: 1.6,\n    },\n    \"@keyframes fadeIn\": {\n      from: { opacity: 0, transform: \"translateY(10px)\" },\n      to: { opacity: 1, transform: \"translateY(0)\" },\n    },\n    \"@keyframes spin\": {\n      to: { transform: \"rotate(360deg)\" },\n    },\n    \"@keyframes bounce\": {\n      \"0%, 80%, 100%\": { transform: \"scale(0.6)\" },\n      \"40%\": { transform: \"scale(1)\" },\n    },\n    \"@keyframes moveBackground\": {\n      \"0%\": { transform: \"translate(0, 0)\" },\n      \"100%\": { transform: \"translate(50px, 50px)\" },\n    },\n    \"@keyframes shine\": {\n      \"0%\": { transform: \"rotate(30deg) translate(-30%, -30%)\" },\n      \"100%\": { transform: \"rotate(30deg) translate(30%, 30%)\" },\n    },\n    examButtonsGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n      gap: \"1.5rem\",\n      padding: \"1.5rem 0\",\n      \"@media (max-width: 768px)\": {\n        gridTemplateColumns: \"1fr\",\n        padding: \"1rem 0\",\n      }\n    },\n    examButton: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      border: \"1px solid #e2e8f0\",\n      transition: \"all 0.3s ease\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"1rem\",\n      cursor: \"pointer\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n      \"&:hover\": {\n        transform: \"translateY(-4px)\",\n        boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n        borderColor: \"#FFB6B6\",\n      }\n    },\n    examIconContainer: {\n      width: \"48px\",\n      height: \"48px\",\n      borderRadius: \"12px\",\n      background: \"linear-gradient(135deg, #FFC5C5 0%, #FFB6B6 100%)\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      marginBottom: \"0.5rem\",\n    },\n    examIcon: {\n      fontSize: \"24px\",\n    },\n    examContent: {\n      flex: 1,\n    },\n    examTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      marginBottom: \"0.5rem\",\n    },\n    examDescription: {\n      fontSize: \"0.9rem\",\n      color: \"#718096\",\n      lineHeight: \"1.5\",\n    },\n    resourcesList: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"0.5rem\",\n      marginTop: \"1rem\",\n    },\n    resourceLink: {\n      color: \"#FFB6B6\",\n      textDecoration: \"none\",\n      fontSize: \"0.9rem\",\n      padding: \"0.5rem\",\n      borderRadius: \"6px\",\n      background: \"#FFF0F0\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFE0E0\",\n        color: \"#FF99CC\",\n      }\n    },\n    comingSoonBadge: {\n      display: \"inline-block\",\n      padding: \"0.5rem 1rem\",\n      borderRadius: \"20px\",\n      background: \"#edf2f7\",\n      color: \"#718096\",\n      fontSize: \"0.85rem\",\n      fontWeight: \"500\",\n      marginTop: \"0.5rem\",\n    },\n    selectedExamContainer: {\n      width: \"100%\",\n      padding: \"1.5rem\",\n    },\n    examHeader: {\n      marginBottom: \"2rem\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"1rem\",\n    },\n    backButton: {\n      background: \"none\",\n      border: \"none\",\n      color: \"#FFB6B6\",\n      cursor: \"pointer\",\n      fontSize: \"1rem\",\n      padding: \"0.75rem 0\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n      transition: \"all 0.2s ease\",\n      marginBottom: \"1rem\",\n      \"&:hover\": {\n        color: \"#FF99CC\",\n        transform: \"translateX(-4px)\",\n      }\n    },\n    selectedExamTitle: {\n      fontSize: \"2rem\",\n      fontWeight: \"700\",\n      color: \"#2d3748\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.75rem\",\n    },\n    resourceTypeFilter: {\n      display: \"flex\",\n      gap: \"0.75rem\",\n      flexWrap: \"wrap\",\n      marginBottom: \"2rem\",\n      padding: \"1rem\",\n      background: \"#f7fafc\",\n      borderRadius: \"12px\",\n    },\n    filterButton: {\n      background: \"white\",\n      border: \"1px solid #e2e8f0\",\n      borderRadius: \"8px\",\n      padding: \"0.5rem 1rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      fontSize: \"0.9rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n      \"&:hover\": {\n        background: \"#FFF0F0\",\n        borderColor: \"#FFB6B6\",\n      }\n    },\n    filterButtonActive: {\n      background: \"#FFB6B6\",\n      color: \"white\",\n      borderColor: \"#FFB6B6\",\n      \"&:hover\": {\n        background: \"#FF99CC\",\n      }\n    },\n    resourcesGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n      gap: \"2rem\",\n    },\n    resourceCategory: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      border: \"1px solid #e2e8f0\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    },\n    categoryTitle: {\n      fontSize: \"1.25rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      marginBottom: \"1rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n    },\n    resourceCard: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      padding: \"1rem\",\n      background: \"#f7fafc\",\n      borderRadius: \"8px\",\n      marginBottom: \"0.75rem\",\n      textDecoration: \"none\",\n      color: \"inherit\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFF0F0\",\n        transform: \"translateY(-2px)\",\n      }\n    },\n    resourceInfo: {\n      flex: 1,\n    },\n    resourceTitle: {\n      fontSize: \"1rem\",\n      fontWeight: \"500\",\n      color: \"#2d3748\",\n      marginBottom: \"0.25rem\",\n    },\n    resourceDescription: {\n      fontSize: \"0.875rem\",\n      color: \"#718096\",\n      marginBottom: \"0.5rem\",\n    },\n    resourceYear: {\n      fontSize: \"0.75rem\",\n      color: \"#4a5568\",\n      background: \"#edf2f7\",\n      padding: \"0.25rem 0.5rem\",\n      borderRadius: \"4px\",\n    },\n    downloadIcon: {\n      fontSize: \"1.25rem\",\n      color: \"#FFB6B6\",\n      marginLeft: \"1rem\",\n    },\n    categoryIcon: {\n      fontSize: \"1.25rem\",\n      marginRight: \"0.5rem\",\n    },\n    categoryPreview: {\n      display: \"flex\",\n      gap: \"0.5rem\",\n      marginTop: \"1rem\",\n      color: \"#718096\",\n    },\n    gateContainer: {\n      width: \"100%\",\n      padding: \"1.5rem\",\n    },\n    gateHeader: {\n      marginBottom: \"2rem\",\n    },\n    gateStats: {\n      display: \"flex\",\n      gap: \"1.5rem\",\n      marginBottom: \"2rem\",\n      \"@media (max-width: 768px)\": {\n        flexDirection: \"column\",\n        gap: \"1rem\",\n      }\n    },\n    statCard: {\n      flex: 1,\n      background: \"white\",\n      padding: \"1.5rem\",\n      borderRadius: \"12px\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n      transition: \"transform 0.2s ease\",\n      \"&:hover\": {\n        transform: \"translateY(-4px)\",\n      }\n    },\n    statIcon: {\n      fontSize: \"2rem\",\n    },\n    statValue: {\n      fontSize: \"1.5rem\",\n      fontWeight: \"bold\",\n      color: \"#2d3748\",\n    },\n    statLabel: {\n      fontSize: \"0.9rem\",\n      color: \"#718096\",\n    },\n    gateFilters: {\n      display: \"flex\",\n      gap: \"1rem\",\n      marginBottom: \"2rem\",\n      \"@media (max-width: 768px)\": {\n        flexDirection: \"column\",\n      }\n    },\n    gateSelect: {\n      padding: \"0.75rem 1rem\",\n      borderRadius: \"8px\",\n      border: \"1px solid #e2e8f0\",\n      background: \"white\",\n      fontSize: \"0.95rem\",\n      color: \"#2d3748\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      \"&:focus\": {\n        outline: \"none\",\n        borderColor: \"#FFB6B6\",\n        boxShadow: \"0 0 0 3px rgba(255, 182, 182, 0.1)\",\n      }\n    },\n    gateContent: {\n      display: \"flex\",\n      gap: \"2rem\",\n      \"@media (max-width: 1024px)\": {\n        flexDirection: \"column\",\n      }\n    },\n    gateSidebar: {\n      width: \"300px\",\n      flexShrink: 0,\n      \"@media (max-width: 1024px)\": {\n        width: \"100%\",\n      }\n    },\n    sidebarSection: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      marginBottom: \"1.5rem\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    },\n    sidebarTitle: {\n      fontSize: \"1.1rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      marginBottom: \"1rem\",\n    },\n    quickLinks: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"0.75rem\",\n    },\n    quickLink: {\n      color: \"#FFB6B6\",\n      textDecoration: \"none\",\n      fontSize: \"0.95rem\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"0.5rem\",\n      padding: \"0.5rem\",\n      borderRadius: \"6px\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFF0F0\",\n      }\n    },\n    datesList: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      gap: \"1rem\",\n    },\n    dateItem: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      padding: \"0.75rem\",\n      background: \"#f7fafc\",\n      borderRadius: \"8px\",\n    },\n    dateLabel: {\n      fontSize: \"0.9rem\",\n      color: \"#4a5568\",\n    },\n    dateValue: {\n      fontSize: \"0.9rem\",\n      fontWeight: \"500\",\n      color: \"#2d3748\",\n    },\n    gateMainContent: {\n      flex: 1,\n    },\n    subjectsGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fill, minmax(250px, 1fr))\",\n      gap: \"1.5rem\",\n    },\n    subjectCard: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"1.5rem\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      gap: \"1rem\",\n      cursor: \"pointer\",\n      transition: \"all 0.3s ease\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n      \"&:hover\": {\n        transform: \"translateY(-4px)\",\n        boxShadow: \"0 8px 16px rgba(0, 0, 0, 0.1)\",\n      }\n    },\n    subjectIcon: {\n      fontSize: \"2.5rem\",\n    },\n    subjectName: {\n      fontSize: \"1.1rem\",\n      fontWeight: \"600\",\n      color: \"#2d3748\",\n      textAlign: \"center\",\n    },\n    subjectTopics: {\n      fontSize: \"0.9rem\",\n      color: \"#718096\",\n    },\n    subjectContent: {\n      background: \"white\",\n      borderRadius: \"12px\",\n      padding: \"2rem\",\n      boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.05)\",\n    },\n    subjectTitle: {\n      fontSize: \"1.5rem\",\n      fontWeight: \"700\",\n      color: \"#2d3748\",\n      marginBottom: \"2rem\",\n    },\n    topicsList: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\",\n      gap: \"1.5rem\",\n    },\n    topicCard: {\n      background: \"#f7fafc\",\n      borderRadius: \"10px\",\n      padding: \"1.25rem\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFF0F0\",\n      }\n    },\n    topicTitle: {\n      fontSize: \"1rem\",\n      fontWeight: \"500\",\n      color: \"#2d3748\",\n      marginBottom: \"1rem\",\n    },\n    topicResources: {\n      display: \"flex\",\n      gap: \"0.75rem\",\n    },\n    resourceButton: {\n      background: \"white\",\n      border: \"1px solid #e2e8f0\",\n      borderRadius: \"6px\",\n      padding: \"0.5rem 0.75rem\",\n      fontSize: \"0.9rem\",\n      color: \"#4a5568\",\n      cursor: \"pointer\",\n      transition: \"all 0.2s ease\",\n      \"&:hover\": {\n        background: \"#FFB6B6\",\n        color: \"white\",\n        borderColor: \"#FFB6B6\",\n      }\n    },\n    leftLogoContainer: {\n      display: \"flex\",\n      alignItems: \"center\",\n      marginRight: \"1rem\",\n    },\n    eduaiLogoImage: {\n      width: \"40px\",\n      height: \"40px\",\n      borderRadius: \"50%\",\n      objectFit: \"cover\",\n      border: \"2px solid #FF99CC\",\n      boxShadow: \"0 2px 4px rgba(255, 153, 204, 0.2)\",\n    },\n    userAvatar: {\n      width: '40px', height: '40px', borderRadius: '50%', background: '#f0f4f8', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: '1.1rem', marginRight: '0.75rem', border: '2px solid #FF99CC', boxShadow: '0 2px 4px rgba(255,153,204,0.1)'\n    },\n    userAvatarLarge: {\n      width: '56px', height: '56px', borderRadius: '50%', background: '#f0f4f8', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: '2rem', border: '2px solid #FF99CC', boxShadow: '0 2px 4px rgba(255,153,204,0.1)'\n    },\n\n\n};\n\nexport default styles;"], "mappings": "AAAA;;AAEA;AACA,MAAMA,MAAM,GAAG;EACbC,YAAY,EAAE;IACZC,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,yBAAyB;IACjCC,UAAU,EAAE,yBAAyB;IACrCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB,CAAC;EACDC,QAAQ,EAAE;IACRhB,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,wBAAwB;IAChCC,UAAU,EAAE,wBAAwB;IACpCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB,CAAC;EACDE,eAAe,EAAE;IACfjB,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,0BAA0B;IAClCC,UAAU,EAAE,0BAA0B;IACtCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB,CAAC;EACDG,QAAQ,EAAE;IACRlB,OAAO,EAAE,SAAS;IAClBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,oBAAoB;IAC5BC,UAAU,EAAE,oBAAoB;IAChCC,QAAQ,EAAE,mDAAmD;IAC7DC,eAAe,EAAE,mDAAmD;IACpEC,aAAa,EAAE;EACjB;AACF,CAAC;;AAED;AACA,MAAMI,YAAY,GAAGrB,MAAM,CAACC,YAAY,CAAC,CAAC;;AAE1C;AACA,MAAMqB,QAAQ,GAAGA,CAACC,SAAS,GAAG,cAAc,KAAK;EAC/C,OAAOvB,MAAM,CAACuB,SAAS,CAAC,IAAIvB,MAAM,CAACC,YAAY;AACjD,CAAC;AAED,MAAMuB,MAAM,GAAG;EACb;EACAxB,MAAM;EACNqB,YAAY;EACZC,QAAQ;EAER;EACAf,UAAU,EAAE;IACVkB,SAAS,EAAE,OAAO;IAClBC,eAAe,EAAEL,YAAY,CAACd,UAAU;IACxCoB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,wDAAwD;IACpEC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE;EACb,CAAC;EACCC,kBAAkB,EAAE;IAClBF,QAAQ,EAAE,OAAO;IACjBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACT7B,UAAU,EAAE,mDAAmD;IAC/D8B,MAAM,EAAE,CAAC;IACT,UAAU,EAAE;MACVC,OAAO,EAAE,IAAI;MACbR,QAAQ,EAAE,UAAU;MACpBG,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTG,eAAe,EAAE;AACzB;AACA;AACA;AACA,SAAS;MACDC,SAAS,EAAE;IACb;EACF,CAAC;EACDC,MAAM,EAAE;IACNC,KAAK,EAAE,MAAM;IACbnC,UAAU,EAAEc,YAAY,CAACN,QAAQ;IACjC4B,SAAS,EAAE,aAAatB,YAAY,CAACR,MAAM,EAAE;IAC7C+B,OAAO,EAAE,cAAc;IACvBd,QAAQ,EAAE,QAAQ;IAClBG,GAAG,EAAE,CAAC;IACNI,MAAM,EAAE,IAAI;IACZQ,cAAc,EAAE,WAAW;IAC3BC,YAAY,EAAE,aAAazB,YAAY,CAACT,MAAM;EAChD,CAAC;EACDmC,YAAY,EAAE;IACZL,KAAK,EAAE,MAAM;IACbM,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,QAAQ;IAChBtB,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACDC,UAAU,EAAE;IACV7C,UAAU,EAAE,aAAa;IACzBK,MAAM,EAAE,MAAM;IACdyC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBV,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,KAAK;IACnB5B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBM,UAAU,EAAE,eAAe;IAC3B,QAAQ,EAAE;MACRjD,UAAU,EAAE,qBAAqB;MACjC+C,KAAK,EAAE;IACT,CAAC;IACD,2BAA2B,EAAE;MAC3B3B,OAAO,EAAE;IACX;EACF,CAAC;EACD8B,oBAAoB,EAAE;IACpB9B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBQ,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IACTC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,MAAM;IAClBvD,UAAU,EAAEc,YAAY,CAACN,QAAQ;IACjCgD,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCC,aAAa,EAAE,OAAO;IACtBC,aAAa,EAAE;EACjB,CAAC;EACDC,QAAQ,EAAE;IACRN,QAAQ,EAAE,SAAS;IACnBP,KAAK,EAAEjC,YAAY,CAACX,SAAS;IAC7B0D,SAAS,EAAE,SAAS;IACpBN,UAAU,EAAE,GAAG;IACfG,aAAa,EAAE;EACjB,CAAC;EACDI,kBAAkB,EAAE;IAClB1C,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE;EACd,CAAC;EACDmB,SAAS,EAAE;IACT5B,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,KAAK;IACnBiB,SAAS,EAAE,OAAO;IAClB5D,MAAM,EAAE,aAAaS,YAAY,CAACnB,OAAO,EAAE;IAC3CyC,SAAS,EAAE,aAAatB,YAAY,CAACR,MAAM;EAC7C,CAAC;EACD4D,OAAO,EAAE;IACP3C,QAAQ,EAAE,OAAO;IACjBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPQ,KAAK,EAAE,OAAO;IACd6B,MAAM,EAAE,OAAO;IACf7C,eAAe,EAAEL,YAAY,CAACb,OAAO;IACrCmC,SAAS,EAAE,cAActB,YAAY,CAACR,MAAM,EAAE;IAC9CwB,MAAM,EAAE,IAAI;IACZmB,UAAU,EAAE,4BAA4B;IACxC7B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvB8C,SAAS,EAAE;EACb,CAAC;EACDC,aAAa,EAAE;IACb/B,OAAO,EAAE,oBAAoB;IAC7BjB,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBL,YAAY,EAAE,aAAazB,YAAY,CAACT,MAAM;EAChD,CAAC;EACDgE,YAAY,EAAE;IACZf,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAEjC,YAAY,CAACZ;EACtB,CAAC;EACDoE,kBAAkB,EAAE;IAClBtE,UAAU,EAAE,aAAa;IACzBK,MAAM,EAAE,MAAM;IACdyC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBV,OAAO,EAAE,SAAS;IAClBW,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,eAAe;IAC3B,QAAQ,EAAE;MACRjD,UAAU,EAAE,qBAAqB;MACjC+C,KAAK,EAAE;IACT;EACF,CAAC;EACDwB,cAAc,EAAE;IACdpB,IAAI,EAAE,CAAC;IACPd,OAAO,EAAE,QAAQ;IACjB8B,SAAS,EAAE;EACb,CAAC;EACDK,gBAAgB,EAAE;IAChBC,YAAY,EAAE;EAChB,CAAC;EACDC,WAAW,EAAE;IACXtD,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBP,OAAO,EAAE,gBAAgB;IACzBS,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B1B,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE;MACRvB,UAAU,EAAE,GAAGc,YAAY,CAACR,MAAM,EAAE;MACpCyC,KAAK,EAAEjC,YAAY,CAACnB;IACtB;EACF,CAAC;EACDgF,iBAAiB,EAAE;IACjB3E,UAAU,EAAE,GAAGc,YAAY,CAACR,MAAM,EAAE;IACpCyC,KAAK,EAAEjC,YAAY,CAACnB,OAAO;IAC3B4D,UAAU,EAAE;EACd,CAAC;EACDqB,WAAW,EAAE;IACXC,WAAW,EAAE,MAAM;IACnBvB,QAAQ,EAAE,QAAQ;IAClBlC,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE;EACd,CAAC;EACDkC,eAAe,EAAE;IACf3B,IAAI,EAAE;EACR,CAAC;EACD4B,iBAAiB,EAAE;IACjB9B,UAAU,EAAE,qBAAqB;IACjC7B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBN,OAAO,EAAE,SAAS;IAClBW,YAAY,EAAE,KAAK;IACnB,QAAQ,EAAE;MACRhD,UAAU,EAAE;IACd;EACF,CAAC;EACDgF,iBAAiB,EAAE;IACjBC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,QAAQ;IAClBjC,UAAU,EAAE;EACd,CAAC;EACDkC,OAAO,EAAE;IACP9C,OAAO,EAAE,gBAAgB;IACzBiB,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChBD,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B,QAAQ,EAAE;MACRjD,UAAU,EAAE,0BAA0B;MACtC+C,KAAK,EAAE;IACT;EACF,CAAC;EACDqC,aAAa,EAAE;IACb/C,OAAO,EAAE,QAAQ;IACjBgD,SAAS,EAAE,+BAA+B;IAC1CjC,SAAS,EAAE;EACb,CAAC;EACDkC,iBAAiB,EAAE;IACjBhC,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE;EACT,CAAC;EACDwC,OAAO,EAAE;IACPhE,QAAQ,EAAE,OAAO;IACjBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTV,eAAe,EAAE,oBAAoB;IACrCW,MAAM,EAAE,IAAI;IACZ,2BAA2B,EAAE;MAC3BV,OAAO,EAAE;IACX;EACF,CAAC;EACDoE,aAAa,EAAE;IACbrD,KAAK,EAAE,MAAM;IACbM,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,WAAW;IACnBL,OAAO,EAAE,QAAQ;IACjBc,IAAI,EAAE,CAAC;IACP/B,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,YAAY;IACxBrB,QAAQ,EAAE,UAAU;IACpBO,MAAM,EAAE,CAAC;IACT,2BAA2B,EAAE;MAC3BY,MAAM,EAAE,WAAW;MACnBL,OAAO,EAAE;IACX;EACF,CAAC;EACDoD,eAAe,EAAE;IACftD,KAAK,EAAE,MAAM;IACbM,QAAQ,EAAE,OAAO;IACjBtB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBlB,MAAM,EAAE,+BAA+B;IACvCe,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvB2C,MAAM,EAAE,qBAAqB;IAC7B,2BAA2B,EAAE;MACzBA,MAAM,EAAE,qBAAqB;MAC7BhB,YAAY,EAAE;IAClB,CAAC;IACD,2BAA2B,EAAE;MACzBgB,MAAM,EAAE,oBAAoB;MAC5BhB,YAAY,EAAE;IAClB;EACJ,CAAC;EACD0C,WAAW,EAAE;IACTnE,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTG,eAAe,EAAE;AACvB;AACA;AACA,OAAO;IACD2D,cAAc,EAAE,WAAW;IAC3BC,aAAa,EAAE;EACjB,CAAC;EACDC,MAAM,EAAE;IACN7F,UAAU,EAAE,mDAAmD;IAC/D+C,KAAK,EAAE,OAAO;IACdV,OAAO,EAAE,cAAc;IACvBe,SAAS,EAAE,QAAQ;IACnB7B,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB,2BAA2B,EAAE;MACzB7C,OAAO,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MACzBA,OAAO,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACRN,OAAO,EAAE,IAAI;MACbR,QAAQ,EAAE,UAAU;MACpBG,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACd7B,UAAU,EAAE;AACtB;AACA,WAAW;MACD8F,SAAS,EAAE,eAAe;MAC1B7D,SAAS,EAAE;IACf;EACJ,CAAC;EACD8D,KAAK,EAAE;IACHrD,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfhC,QAAQ,EAAE,UAAU;IACpByE,UAAU,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;IACRpC,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChB4C,OAAO,EAAE,GAAG;IACZ3E,QAAQ,EAAE,UAAU;IACpBgC,UAAU,EAAE;EACd,CAAC;EACD4C,aAAa,EAAE;IACbnC,MAAM,EAAE,qBAAqB;IAC7BoC,SAAS,EAAE,OAAO;IAClBlF,SAAS,EAAE,OAAO;IAClBiD,SAAS,EAAE,MAAM;IACjB9B,OAAO,EAAE,QAAQ;IACjBrC,UAAU,EAAE,OAAO;IACnBuB,QAAQ,EAAE,UAAU;IACpBH,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBc,KAAK,EAAE,MAAM;IACbkE,SAAS,EAAE,YAAY;IACvB,4BAA4B,EAAE;MAC5BrC,MAAM,EAAE,qBAAqB;MAC7B3B,OAAO,EAAE;IACX,CAAC;IACD,2BAA2B,EAAE;MAC3B2B,MAAM,EAAE,qBAAqB;MAC7B3B,OAAO,EAAE,SAAS;MAClBnB,SAAS,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MAC3B8C,MAAM,EAAE,qBAAqB;MAC7B3B,OAAO,EAAE,QAAQ;MACjBnB,SAAS,EAAE;IACb;EACF,CAAC;EACDoF,YAAY,EAAE;IACZlF,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE,MAAM;IACXhF,QAAQ,EAAE,UAAU;IACpB4B,IAAI,EAAE,CAAC;IACPgB,SAAS,EAAE,MAAM;IACjBqC,YAAY,EAAE,QAAQ;IACtBC,cAAc,EAAE,QAAQ;IACxBtE,KAAK,EAAE,MAAM;IACbkE,SAAS,EAAE,YAAY;IACvB,2BAA2B,EAAE;MAC3BE,GAAG,EAAE,QAAQ;MACbC,YAAY,EAAE;IAChB,CAAC;IACD,2BAA2B,EAAE;MAC3BD,GAAG,EAAE,SAAS;MACdC,YAAY,EAAE;IAChB;EACF,CAAC;EACDE,OAAO,EAAE;IACPrE,OAAO,EAAE,cAAc;IACvBW,YAAY,EAAE,MAAM;IACpBP,QAAQ,EAAE,KAAK;IACfN,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,2BAA2B;IACtCuE,UAAU,EAAE,GAAG;IACfpF,QAAQ,EAAE,UAAU;IACpB0B,UAAU,EAAE,eAAe;IAC3BK,QAAQ,EAAE,MAAM;IAChBrB,SAAS,EAAE,sBAAsB;IACjC2E,QAAQ,EAAE,YAAY;IACtB,2BAA2B,EAAE;MACzBnE,QAAQ,EAAE,KAAK;MACfJ,OAAO,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MACzBI,QAAQ,EAAE,KAAK;MACfJ,OAAO,EAAE,kBAAkB;MAC3BiB,QAAQ,EAAE;IACd;EACF,CAAC;EACDuD,cAAc,EAAE;IACd7G,UAAU,EAAE,0BAA0B;IACtCqC,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,MAAM;IACpBI,SAAS,EAAE,QAAQ;IACnBV,MAAM,EAAE,iBAAiB;IACzBrC,MAAM,EAAE,oBAAoB;IAC5BiC,cAAc,EAAE,WAAW;IAC3BL,SAAS,EAAE;EACb,CAAC;EACD6E,YAAY,EAAE;IACZxD,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDsC,WAAW,EAAE;IACXzD,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChB4D,UAAU,EAAE;EACd,CAAC;EACDK,WAAW,EAAE;IACXC,SAAS,EAAE,UAAU;IACrBjH,UAAU,EAAE,mDAAmD;IAC/D+C,KAAK,EAAE,OAAO;IACdmE,uBAAuB,EAAE;EAC3B,CAAC;EACDC,UAAU,EAAE;IACVF,SAAS,EAAE,YAAY;IACvBjH,UAAU,EAAE,0BAA0B;IACtC+C,KAAK,EAAE,SAAS;IAChBqE,sBAAsB,EAAE,KAAK;IAC7B/G,MAAM,EAAE,oCAAoC;IAC5CiC,cAAc,EAAE;EAClB,CAAC;EACD+E,WAAW,EAAE;IACX/D,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfkB,YAAY,EAAE,QAAQ;IACtByB,OAAO,EAAE,GAAG;IACZvC,aAAa,EAAE,WAAW;IAC1BD,aAAa,EAAE;EACjB,CAAC;EACD4D,cAAc,EAAE;IACdhE,QAAQ,EAAE,MAAM;IAChBiE,UAAU,EAAE;EACd,CAAC;EACDC,cAAc,EAAE;IACdpG,OAAO,EAAE,MAAM;IACfiB,OAAO,EAAE,SAAS;IAClBrC,UAAU,EAAE,SAAS;IACrBqF,SAAS,EAAE,oCAAoC;IAC/C9D,QAAQ,EAAE,UAAU;IACpBe,cAAc,EAAE,WAAW;IAC3BW,UAAU,EAAE,eAAe;IAC3B6C,SAAS,EAAE,eAAe;IAC1BM,SAAS,EAAE,OAAO;IAClBjE,KAAK,EAAE,MAAM;IACbkE,SAAS,EAAE,YAAY;IACvB,WAAW,EAAE;MACXrG,UAAU,EAAE,SAAS;MACrBoC,SAAS,EAAE;IACb,CAAC;IACD,4BAA4B,EAAE;MAC5BC,OAAO,EAAE,MAAM;MACf+D,SAAS,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MAC3B/D,OAAO,EAAE,SAAS;MAClBhB,aAAa,EAAE,QAAQ;MACvBkF,GAAG,EAAE,SAAS;MACdH,SAAS,EAAE;IACb,CAAC;IACD,2BAA2B,EAAE;MAC3B/D,OAAO,EAAE,QAAQ;MACjBhB,aAAa,EAAE,QAAQ;MACvBkF,GAAG,EAAE,QAAQ;MACbH,SAAS,EAAE;IACb;EACF,CAAC;EACDqB,KAAK,EAAE;IACLtE,IAAI,EAAE,CAAC;IACPd,OAAO,EAAE,aAAa;IACtBW,YAAY,EAAE,KAAK;IACnB3C,MAAM,EAAE,oCAAoC;IAC5CqH,OAAO,EAAE,MAAM;IACfpE,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE,eAAe;IAC3BjD,UAAU,EAAE,uBAAuB;IACnC+C,KAAK,EAAE,SAAS;IAChBX,SAAS,EAAE,+BAA+B;IAC1C,SAAS,EAAE;MACTuF,WAAW,EAAE,yBAAyB;MACtCvF,SAAS,EAAE,mCAAmC;MAC9CpC,UAAU,EAAE;IACd,CAAC;IACD,gBAAgB,EAAE;MAChB+C,KAAK,EAAE,SAAS;MAChBE,UAAU,EAAE;IACd,CAAC;IACD,sBAAsB,EAAE;MACtBiD,OAAO,EAAE;IACX,CAAC;IACD,2BAA2B,EAAE;MAC3B7D,OAAO,EAAE,kBAAkB;MAC3BiB,QAAQ,EAAE;IACZ,CAAC;IACD,2BAA2B,EAAE;MAC3BjB,OAAO,EAAE,cAAc;MACvBiB,QAAQ,EAAE,QAAQ;MAClBnB,KAAK,EAAE;IACT;EACF,CAAC;EACDyF,UAAU,EAAE;IACV5H,UAAU,EAAE,mDAAmD;IAC/D+C,KAAK,EAAE,OAAO;IACd1C,MAAM,EAAE,MAAM;IACd2C,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,WAAW;IACpBwF,UAAU,EAAE,MAAM;IAClB/E,MAAM,EAAE,SAAS;IACjBS,UAAU,EAAE,GAAG;IACfD,QAAQ,EAAE,MAAM;IAChBlC,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBM,UAAU,EAAE,eAAe;IAC3B1B,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,mCAAmC;IAC9C0F,QAAQ,EAAE,MAAM;IAChB9D,MAAM,EAAE,MAAM;IACd,wBAAwB,EAAE;MACxB8B,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE;IACb,CAAC;IACD,YAAY,EAAE;MACZpC,UAAU,EAAE,SAAS;MACrB8C,MAAM,EAAE,aAAa;MACrBgD,SAAS,EAAE,MAAM;MACjB1D,SAAS,EAAE,MAAM;MACjB8D,OAAO,EAAE;IACX,CAAC;IACD,2BAA2B,EAAE;MAC3B7D,OAAO,EAAE,UAAU;MACnBiB,QAAQ,EAAE,SAAS;MACnBU,MAAM,EAAE,MAAM;MACd6D,UAAU,EAAE;IACd,CAAC;IACD,2BAA2B,EAAE;MAC3BA,UAAU,EAAE,CAAC;MACb1F,KAAK,EAAE,MAAM;MACb6B,MAAM,EAAE,MAAM;MACd8B,SAAS,EAAE,0BAA0B;MACrCI,OAAO,EAAE;IACX;EACF,CAAC;EACD6B,iBAAiB,EAAE;IACjB5F,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACd3D,MAAM,EAAE,iCAAiC;IACzC2H,cAAc,EAAE,OAAO;IACvBhF,YAAY,EAAE,KAAK;IACnBf,SAAS,EAAE;EACb,CAAC;EACDgG,gBAAgB,EAAE;IAChB7G,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,QAAQ;IACxBN,OAAO,EAAE;EACX,CAAC;EACD6F,WAAW,EAAE;IACX9G,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,SAAS;IACd3D,UAAU,EAAE;EACd,CAAC;EACDuF,UAAU,EAAE;IACVhG,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,KAAK;IACnBhD,UAAU,EAAE,SAAS;IACrBiC,SAAS,EAAE,kCAAkC;IAC7C,eAAe,EAAE;MACfmG,cAAc,EAAE;IAClB,CAAC;IACD,eAAe,EAAE;MACfA,cAAc,EAAE;IAClB,CAAC;IACD,eAAe,EAAE;MACfA,cAAc,EAAE;IAClB;EACF,CAAC;EACDC,YAAY,EAAE;IACZlG,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBlB,MAAM,EAAE;EACV,CAAC;EACDiI,SAAS,EAAE;IACTjG,OAAO,EAAE,cAAc;IACvBrC,UAAU,EAAE,mDAAmD;IAC/DuC,YAAY,EAAE,+BAA+B;IAC7Ca,SAAS,EAAE;EACb,CAAC;EACDmF,QAAQ,EAAE;IACR7F,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACDyF,WAAW,EAAE;IACX3E,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE;EACd,CAAC;EACDkF,SAAS,EAAE;IACTlH,QAAQ,EAAE,UAAU;IACpBkB,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,eAAe;IACvBtB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE;EACd,CAAC;EACD8F,UAAU,EAAE;IACVnH,QAAQ,EAAE,UAAU;IACpBI,IAAI,EAAE,MAAM;IACZD,GAAG,EAAE,KAAK;IACVoE,SAAS,EAAE,kBAAkB;IAC7B/C,KAAK,EAAE;EACT,CAAC;EACD4F,WAAW,EAAE;IACXxG,KAAK,EAAE,MAAM;IACbE,OAAO,EAAE,qBAAqB;IAC9BW,YAAY,EAAE,KAAK;IACnB3C,MAAM,EAAE,mBAAmB;IAC3BqH,OAAO,EAAE,MAAM;IACfpE,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE,eAAe;IAC3BjD,UAAU,EAAE,OAAO;IACnB+C,KAAK,EAAE,SAAS;IAChBX,SAAS,EAAE,8BAA8B;IACzC,QAAQ,EAAE;MACRuF,WAAW,EAAE,SAAS;MACtBvF,SAAS,EAAE;IACb;EACF,CAAC;EACDwG,iBAAiB,EAAE;IACjBrH,QAAQ,EAAE,UAAU;IACpBK,KAAK,EAAE,MAAM;IACbF,GAAG,EAAE,KAAK;IACVoE,SAAS,EAAE,kBAAkB;IAC7B9F,UAAU,EAAE,aAAa;IACzBK,MAAM,EAAE,MAAM;IACdyC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,SAAS;IAChBV,OAAO,EAAE,SAAS;IAClBW,YAAY,EAAE,KAAK;IACnB5B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBM,UAAU,EAAE,eAAe;IAC3B,QAAQ,EAAE;MACRjD,UAAU,EAAE;IACd;EACF,CAAC;EACD6I,aAAa,EAAE;IACbzH,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE,MAAM;IACXlE,OAAO,EAAE,MAAM;IACfrC,UAAU,EAAE;EACd,CAAC;EACD+I,WAAW,EAAE;IACX/I,UAAU,EAAE,mDAAmD;IAAE;IACjEK,MAAM,EAAE,mBAAmB;IAC3B2C,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,SAAS;IAClBS,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B1B,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,+BAA+B;IAC1C,QAAQ,EAAE;MACR0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,+BAA+B;MAC1CuF,WAAW,EAAE;IACf;EACF,CAAC;EACDqB,cAAc,EAAE;IACd7G,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,KAAK;IACnBhD,UAAU,EAAE,mDAAmD;IAC/D+C,KAAK,EAAE,OAAO;IACd3B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBY,UAAU,EAAE,MAAM;IAClBD,QAAQ,EAAE,QAAQ;IAClBmB,YAAY,EAAE;EAChB,CAAC;EACDwE,WAAW,EAAE;IACX3F,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChBE,UAAU,EAAE;EACd,CAAC;EACDiG,kBAAkB,EAAE;IAClB3H,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACT7B,UAAU,EAAE,oFAAoF;IAChGkG,OAAO,EAAE,CAAC;IACVjD,UAAU,EAAE,mBAAmB;IAC/B,QAAQ,EAAE;MACRiD,OAAO,EAAE;IACX;EACF,CAAC;EACDiD,SAAS,EAAE;IACT/F,SAAS,EAAE,QAAQ;IACnBf,OAAO,EAAE,MAAM;IACf+G,UAAU,EAAE,QAAQ;IACpBhI,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE;EAClB,CAAC;EACD0G,aAAa,EAAE;IACb5E,YAAY,EAAE,MAAM;IACpB1B,KAAK,EAAE;EACT,CAAC;EACDuG,aAAa,EAAE;IACbhG,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACD8E,sBAAsB,EAAE;IACtBvJ,UAAU,EAAE,mDAAmD;IAC/D+C,KAAK,EAAE,OAAO;IACd1C,MAAM,EAAE,MAAM;IACd2C,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,gBAAgB;IACzBS,MAAM,EAAE,SAAS;IACjBS,UAAU,EAAE,GAAG;IACfD,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE,eAAe;IAC3Bb,SAAS,EAAE,mCAAmC;IAC9C,QAAQ,EAAE;MACR0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE;IACb;EACF,CAAC;EACDoH,gBAAgB,EAAE;IAChBrH,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBlB,MAAM,EAAE;EACV,CAAC;EACDoJ,aAAa,EAAE;IACbpH,OAAO,EAAE,cAAc;IACvBrC,UAAU,EAAE,mDAAmD;IAC/DuC,YAAY,EAAE,+BAA+B;IAC7Ca,SAAS,EAAE;EACb,CAAC;EACDsG,YAAY,EAAE;IACZhH,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACD4G,eAAe,EAAE;IACf9F,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE;EACd,CAAC;EACDqG,SAAS,EAAE;IACTxI,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE,QAAQ;IACblE,OAAO,EAAE,MAAM;IACfrC,UAAU,EAAE;EACd,CAAC;EACD6J,QAAQ,EAAE;IACR7J,UAAU,EAAE,mDAAmD;IAAE;IACjEK,MAAM,EAAE,mBAAmB;IAC3B2C,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBS,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B1B,QAAQ,EAAE,UAAU;IACpB2D,QAAQ,EAAE,QAAQ;IAClB9D,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBR,SAAS,EAAE,+BAA+B;IAC1C,QAAQ,EAAE;MACR0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,+BAA+B;MAC1CuF,WAAW,EAAE;IACf;EACF,CAAC;EACDmC,eAAe,EAAE;IACf3G,IAAI,EAAE;EACR,CAAC;EACD4G,aAAa,EAAE;IACbzG,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDuF,mBAAmB,EAAE;IACnB1G,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChB4D,UAAU,EAAE;EACd,CAAC;EACDsD,aAAa,EAAE;IACblH,KAAK,EAAE,SAAS;IAChB8E,UAAU,EAAE;EACd,CAAC;EACDqC,aAAa,EAAE;IACb3I,QAAQ,EAAE,UAAU;IACpBG,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACT7B,UAAU,EAAE,oFAAoF;IAChGkG,OAAO,EAAE,CAAC;IACVjD,UAAU,EAAE,mBAAmB;IAC/B,QAAQ,EAAE;MACRiD,OAAO,EAAE;IACX;EACF,CAAC;EACDiE,cAAc,EAAE;IACdhI,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBlB,MAAM,EAAE,+BAA+B;IACvCgC,OAAO,EAAE,MAAM;IACfe,SAAS,EAAE;EACb,CAAC;EACDgH,UAAU,EAAE;IACV1H,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACDsH,aAAa,EAAE;IACbxG,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,GAAG;IACfkB,YAAY,EAAE;EAChB,CAAC;EACD6F,iBAAiB,EAAE;IACjBnI,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBlB,MAAM,EAAE,+BAA+B;IACvCgC,OAAO,EAAE,MAAM;IACfe,SAAS,EAAE;EACb,CAAC;EACDmH,aAAa,EAAE;IACb7H,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACDyH,gBAAgB,EAAE;IAChB3G,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,GAAG;IACfkB,YAAY,EAAE;EAChB,CAAC;EACDgG,YAAY,EAAE;IACZtI,KAAK,EAAE,MAAM;IACbhB,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,MAAM;IACpBkC,QAAQ,EAAE,QAAQ;IAClB9C,SAAS,EAAE,iCAAiC;IAC5Cb,QAAQ,EAAE,UAAU;IACpBlB,MAAM,EAAE,+BAA+B;IACvCgC,OAAO,EAAE,MAAM;IACfe,SAAS,EAAE;EACb,CAAC;EACDsH,QAAQ,EAAE;IACRhI,MAAM,EAAE,CAAC;IACTY,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE;EACT,CAAC;EACD4H,WAAW,EAAE;IACX9G,SAAS,EAAE,SAAS;IACpBP,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChBQ,UAAU,EAAE,GAAG;IACfkB,YAAY,EAAE;EAChB,CAAC;EACDmG,UAAU,EAAE;IACVxJ,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpB1B,SAAS,EAAE;EACb,CAAC;EACD2J,iBAAiB,EAAE;IACjBpI,QAAQ,EAAE,OAAO;IACjBW,SAAS,EAAE;EACb,CAAC;EACD0H,cAAc,EAAE;IACdxH,QAAQ,EAAE,MAAM;IAChBmB,YAAY,EAAE,MAAM;IACpB1B,KAAK,EAAE;EACT,CAAC;EACDgI,cAAc,EAAE;IACdzH,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,GAAG;IACfR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDuG,qBAAqB,EAAE;IACrB1H,QAAQ,EAAE,MAAM;IAChBP,KAAK,EAAE,SAAS;IAChB4D,UAAU,EAAE;EACd,CAAC;EACD,mBAAmB,EAAE;IACnBsE,IAAI,EAAE;MAAE/E,OAAO,EAAE,CAAC;MAAEJ,SAAS,EAAE;IAAmB,CAAC;IACnDoF,EAAE,EAAE;MAAEhF,OAAO,EAAE,CAAC;MAAEJ,SAAS,EAAE;IAAgB;EAC/C,CAAC;EACD,iBAAiB,EAAE;IACjBoF,EAAE,EAAE;MAAEpF,SAAS,EAAE;IAAiB;EACpC,CAAC;EACD,mBAAmB,EAAE;IACnB,eAAe,EAAE;MAAEA,SAAS,EAAE;IAAa,CAAC;IAC5C,KAAK,EAAE;MAAEA,SAAS,EAAE;IAAW;EACjC,CAAC;EACD,2BAA2B,EAAE;IAC3B,IAAI,EAAE;MAAEA,SAAS,EAAE;IAAkB,CAAC;IACtC,MAAM,EAAE;MAAEA,SAAS,EAAE;IAAwB;EAC/C,CAAC;EACD,kBAAkB,EAAE;IAClB,IAAI,EAAE;MAAEA,SAAS,EAAE;IAAsC,CAAC;IAC1D,MAAM,EAAE;MAAEA,SAAS,EAAE;IAAoC;EAC3D,CAAC;EACDqF,eAAe,EAAE;IACf/J,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,sCAAsC;IAC3DvC,GAAG,EAAE,QAAQ;IACblE,OAAO,EAAE,UAAU;IACnB,2BAA2B,EAAE;MAC3ByG,mBAAmB,EAAE,KAAK;MAC1BzG,OAAO,EAAE;IACX;EACF,CAAC;EACD+I,UAAU,EAAE;IACVpL,UAAU,EAAE,OAAO;IACnBgD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBhC,MAAM,EAAE,mBAAmB;IAC3B4C,UAAU,EAAE,eAAe;IAC3B7B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE,MAAM;IACXzD,MAAM,EAAE,SAAS;IACjBV,SAAS,EAAE,+BAA+B;IAC1C,SAAS,EAAE;MACT0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE,+BAA+B;MAC1CuF,WAAW,EAAE;IACf;EACF,CAAC;EACD0D,iBAAiB,EAAE;IACjBlJ,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,MAAM;IACpBhD,UAAU,EAAE,mDAAmD;IAC/DoB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxB8B,YAAY,EAAE;EAChB,CAAC;EACD6G,QAAQ,EAAE;IACRhI,QAAQ,EAAE;EACZ,CAAC;EACDiI,WAAW,EAAE;IACXpI,IAAI,EAAE;EACR,CAAC;EACDqI,SAAS,EAAE;IACTlI,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDgH,eAAe,EAAE;IACfnI,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChB4D,UAAU,EAAE;EACd,CAAC;EACD+E,aAAa,EAAE;IACbtK,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE,QAAQ;IACb1C,SAAS,EAAE;EACb,CAAC;EACD8H,YAAY,EAAE;IACZ5I,KAAK,EAAE,SAAS;IAChB6I,cAAc,EAAE,MAAM;IACtBtI,QAAQ,EAAE,QAAQ;IAClBjB,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,KAAK;IACnBhD,UAAU,EAAE,SAAS;IACrBiD,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTjD,UAAU,EAAE,SAAS;MACrB+C,KAAK,EAAE;IACT;EACF,CAAC;EACD8I,eAAe,EAAE;IACfzK,OAAO,EAAE,cAAc;IACvBiB,OAAO,EAAE,aAAa;IACtBW,YAAY,EAAE,MAAM;IACpBhD,UAAU,EAAE,SAAS;IACrB+C,KAAK,EAAE,SAAS;IAChBO,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBM,SAAS,EAAE;EACb,CAAC;EACDiI,qBAAqB,EAAE;IACrB3J,KAAK,EAAE,MAAM;IACbE,OAAO,EAAE;EACX,CAAC;EACD0J,UAAU,EAAE;IACVtH,YAAY,EAAE,MAAM;IACpBrD,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE;EACP,CAAC;EACDyF,UAAU,EAAE;IACVhM,UAAU,EAAE,MAAM;IAClBK,MAAM,EAAE,MAAM;IACd0C,KAAK,EAAE,SAAS;IAChBD,MAAM,EAAE,SAAS;IACjBQ,QAAQ,EAAE,MAAM;IAChBjB,OAAO,EAAE,WAAW;IACpBjB,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACbtD,UAAU,EAAE,eAAe;IAC3BwB,YAAY,EAAE,MAAM;IACpB,SAAS,EAAE;MACT1B,KAAK,EAAE,SAAS;MAChB+C,SAAS,EAAE;IACb;EACF,CAAC;EACDmG,iBAAiB,EAAE;IACjB3I,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB3B,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE;EACP,CAAC;EACD2F,kBAAkB,EAAE;IAClB9K,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,SAAS;IACd4F,QAAQ,EAAE,MAAM;IAChB1H,YAAY,EAAE,MAAM;IACpBpC,OAAO,EAAE,MAAM;IACfrC,UAAU,EAAE,SAAS;IACrBgD,YAAY,EAAE;EAChB,CAAC;EACDoJ,YAAY,EAAE;IACZpM,UAAU,EAAE,OAAO;IACnBK,MAAM,EAAE,mBAAmB;IAC3B2C,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,aAAa;IACtBS,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3BK,QAAQ,EAAE,QAAQ;IAClBlC,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACb,SAAS,EAAE;MACTvG,UAAU,EAAE,SAAS;MACrB2H,WAAW,EAAE;IACf;EACF,CAAC;EACD0E,kBAAkB,EAAE;IAClBrM,UAAU,EAAE,SAAS;IACrB+C,KAAK,EAAE,OAAO;IACd4E,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE;MACT3H,UAAU,EAAE;IACd;EACF,CAAC;EACDsM,aAAa,EAAE;IACblL,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,sCAAsC;IAC3DvC,GAAG,EAAE;EACP,CAAC;EACDgG,gBAAgB,EAAE;IAChBvM,UAAU,EAAE,OAAO;IACnBgD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBhC,MAAM,EAAE,mBAAmB;IAC3B+B,SAAS,EAAE;EACb,CAAC;EACDoK,aAAa,EAAE;IACblJ,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE,MAAM;IACpBrD,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE;EACP,CAAC;EACDkG,YAAY,EAAE;IACZrL,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBP,OAAO,EAAE,MAAM;IACfrC,UAAU,EAAE,SAAS;IACrBgD,YAAY,EAAE,KAAK;IACnByB,YAAY,EAAE,SAAS;IACvBmH,cAAc,EAAE,MAAM;IACtB7I,KAAK,EAAE,SAAS;IAChBE,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTjD,UAAU,EAAE,SAAS;MACrB8F,SAAS,EAAE;IACb;EACF,CAAC;EACD4G,YAAY,EAAE;IACZvJ,IAAI,EAAE;EACR,CAAC;EACDwJ,aAAa,EAAE;IACbrJ,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDmI,mBAAmB,EAAE;IACnBtJ,QAAQ,EAAE,UAAU;IACpBP,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDoI,YAAY,EAAE;IACZvJ,QAAQ,EAAE,SAAS;IACnBP,KAAK,EAAE,SAAS;IAChB/C,UAAU,EAAE,SAAS;IACrBqC,OAAO,EAAE,gBAAgB;IACzBW,YAAY,EAAE;EAChB,CAAC;EACD8J,YAAY,EAAE;IACZxJ,QAAQ,EAAE,SAAS;IACnBP,KAAK,EAAE,SAAS;IAChB8E,UAAU,EAAE;EACd,CAAC;EACDkF,YAAY,EAAE;IACZzJ,QAAQ,EAAE,SAAS;IACnBuB,WAAW,EAAE;EACf,CAAC;EACDmI,eAAe,EAAE;IACf5L,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,QAAQ;IACb1C,SAAS,EAAE,MAAM;IACjBd,KAAK,EAAE;EACT,CAAC;EACDkK,aAAa,EAAE;IACb9K,KAAK,EAAE,MAAM;IACbE,OAAO,EAAE;EACX,CAAC;EACD6K,UAAU,EAAE;IACVzI,YAAY,EAAE;EAChB,CAAC;EACD0I,SAAS,EAAE;IACT/L,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,QAAQ;IACb9B,YAAY,EAAE,MAAM;IACpB,2BAA2B,EAAE;MAC3BpD,aAAa,EAAE,QAAQ;MACvBkF,GAAG,EAAE;IACP;EACF,CAAC;EACD6G,QAAQ,EAAE;IACRjK,IAAI,EAAE,CAAC;IACPnD,UAAU,EAAE,OAAO;IACnBqC,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,MAAM;IACpB5B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACbnE,SAAS,EAAE,+BAA+B;IAC1Ca,UAAU,EAAE,qBAAqB;IACjC,SAAS,EAAE;MACT6C,SAAS,EAAE;IACb;EACF,CAAC;EACDuH,QAAQ,EAAE;IACR/J,QAAQ,EAAE;EACZ,CAAC;EACDgK,SAAS,EAAE;IACThK,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,MAAM;IAClBR,KAAK,EAAE;EACT,CAAC;EACDwK,SAAS,EAAE;IACTjK,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE;EACT,CAAC;EACDyK,WAAW,EAAE;IACXpM,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,MAAM;IACX9B,YAAY,EAAE,MAAM;IACpB,2BAA2B,EAAE;MAC3BpD,aAAa,EAAE;IACjB;EACF,CAAC;EACDoM,UAAU,EAAE;IACVpL,OAAO,EAAE,cAAc;IACvBW,YAAY,EAAE,KAAK;IACnB3C,MAAM,EAAE,mBAAmB;IAC3BL,UAAU,EAAE,OAAO;IACnBsD,QAAQ,EAAE,SAAS;IACnBP,KAAK,EAAE,SAAS;IAChBD,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTyE,OAAO,EAAE,MAAM;MACfC,WAAW,EAAE,SAAS;MACtBvF,SAAS,EAAE;IACb;EACF,CAAC;EACDsL,WAAW,EAAE;IACXtM,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE,MAAM;IACX,4BAA4B,EAAE;MAC5BlF,aAAa,EAAE;IACjB;EACF,CAAC;EACDsM,WAAW,EAAE;IACXxL,KAAK,EAAE,OAAO;IACdyL,UAAU,EAAE,CAAC;IACb,4BAA4B,EAAE;MAC5BzL,KAAK,EAAE;IACT;EACF,CAAC;EACD0L,cAAc,EAAE;IACd7N,UAAU,EAAE,OAAO;IACnBgD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBoC,YAAY,EAAE,QAAQ;IACtBrC,SAAS,EAAE;EACb,CAAC;EACDiC,YAAY,EAAE;IACZf,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDqJ,UAAU,EAAE;IACV1M,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE;EACP,CAAC;EACDwH,SAAS,EAAE;IACThL,KAAK,EAAE,SAAS;IAChB6I,cAAc,EAAE,MAAM;IACtBtI,QAAQ,EAAE,SAAS;IACnBlC,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,QAAQ;IACblE,OAAO,EAAE,QAAQ;IACjBW,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTjD,UAAU,EAAE;IACd;EACF,CAAC;EACDgO,SAAS,EAAE;IACT5M,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBkF,GAAG,EAAE;EACP,CAAC;EACD0H,QAAQ,EAAE;IACR7M,OAAO,EAAE,MAAM;IACfuB,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBP,OAAO,EAAE,SAAS;IAClBrC,UAAU,EAAE,SAAS;IACrBgD,YAAY,EAAE;EAChB,CAAC;EACDkL,SAAS,EAAE;IACT5K,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE;EACT,CAAC;EACDoL,SAAS,EAAE;IACT7K,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE;EACT,CAAC;EACDqL,eAAe,EAAE;IACfjL,IAAI,EAAE;EACR,CAAC;EACDkL,YAAY,EAAE;IACZjN,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE;EACP,CAAC;EACD+H,WAAW,EAAE;IACXtO,UAAU,EAAE,OAAO;IACnBgD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,QAAQ;IACjBjB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBuB,UAAU,EAAE,QAAQ;IACpB2D,GAAG,EAAE,MAAM;IACXzD,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3Bb,SAAS,EAAE,+BAA+B;IAC1C,SAAS,EAAE;MACT0D,SAAS,EAAE,kBAAkB;MAC7B1D,SAAS,EAAE;IACb;EACF,CAAC;EACDmM,WAAW,EAAE;IACXjL,QAAQ,EAAE;EACZ,CAAC;EACDkL,WAAW,EAAE;IACXlL,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChBK,SAAS,EAAE;EACb,CAAC;EACDqL,aAAa,EAAE;IACbnL,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE;EACT,CAAC;EACD2L,cAAc,EAAE;IACd1O,UAAU,EAAE,OAAO;IACnBgD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,MAAM;IACfD,SAAS,EAAE;EACb,CAAC;EACDuM,YAAY,EAAE;IACZrL,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDmK,UAAU,EAAE;IACVxN,OAAO,EAAE,MAAM;IACf0H,mBAAmB,EAAE,uCAAuC;IAC5DvC,GAAG,EAAE;EACP,CAAC;EACDsI,SAAS,EAAE;IACT7O,UAAU,EAAE,SAAS;IACrBgD,YAAY,EAAE,MAAM;IACpBX,OAAO,EAAE,SAAS;IAClBY,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTjD,UAAU,EAAE;IACd;EACF,CAAC;EACD8O,UAAU,EAAE;IACVxL,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBR,KAAK,EAAE,SAAS;IAChB0B,YAAY,EAAE;EAChB,CAAC;EACDsK,cAAc,EAAE;IACd3N,OAAO,EAAE,MAAM;IACfmF,GAAG,EAAE;EACP,CAAC;EACDyI,cAAc,EAAE;IACdhP,UAAU,EAAE,OAAO;IACnBK,MAAM,EAAE,mBAAmB;IAC3B2C,YAAY,EAAE,KAAK;IACnBX,OAAO,EAAE,gBAAgB;IACzBiB,QAAQ,EAAE,QAAQ;IAClBP,KAAK,EAAE,SAAS;IAChBD,MAAM,EAAE,SAAS;IACjBG,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTjD,UAAU,EAAE,SAAS;MACrB+C,KAAK,EAAE,OAAO;MACd4E,WAAW,EAAE;IACf;EACF,CAAC;EACDsH,iBAAiB,EAAE;IACjB7N,OAAO,EAAE,MAAM;IACfwB,UAAU,EAAE,QAAQ;IACpBiC,WAAW,EAAE;EACf,CAAC;EACDqK,cAAc,EAAE;IACd/M,KAAK,EAAE,MAAM;IACb6B,MAAM,EAAE,MAAM;IACdhB,YAAY,EAAE,KAAK;IACnBiB,SAAS,EAAE,OAAO;IAClB5D,MAAM,EAAE,mBAAmB;IAC3B+B,SAAS,EAAE;EACb,CAAC;EACD+M,UAAU,EAAE;IACVhN,KAAK,EAAE,MAAM;IAAE6B,MAAM,EAAE,MAAM;IAAEhB,YAAY,EAAE,KAAK;IAAEhD,UAAU,EAAE,SAAS;IAAEoB,OAAO,EAAE,MAAM;IAAEwB,UAAU,EAAE,QAAQ;IAAED,cAAc,EAAE,QAAQ;IAAEY,UAAU,EAAE,GAAG;IAAED,QAAQ,EAAE,QAAQ;IAAEuB,WAAW,EAAE,SAAS;IAAExE,MAAM,EAAE,mBAAmB;IAAE+B,SAAS,EAAE;EACnP,CAAC;EACDgN,eAAe,EAAE;IACfjN,KAAK,EAAE,MAAM;IAAE6B,MAAM,EAAE,MAAM;IAAEhB,YAAY,EAAE,KAAK;IAAEhD,UAAU,EAAE,SAAS;IAAEoB,OAAO,EAAE,MAAM;IAAEwB,UAAU,EAAE,QAAQ;IAAED,cAAc,EAAE,QAAQ;IAAEY,UAAU,EAAE,GAAG;IAAED,QAAQ,EAAE,MAAM;IAAEjD,MAAM,EAAE,mBAAmB;IAAE+B,SAAS,EAAE;EACzN;AAGJ,CAAC;AAED,eAAenB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}