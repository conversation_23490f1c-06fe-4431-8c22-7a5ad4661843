import React, { useState, useEffect } from 'react';
import { AWSAuth, AWSStorage, AWSDatabase, AWSEmail } from './awsConfig';
import styles from './styles';

const AuthPage = ({ onAuthSuccess }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [otp, setOtp] = useState('');
  const [generatedOtp, setGeneratedOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [profilePic, setProfilePic] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSignupMode, setIsSignupMode] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [particles, setParticles] = useState([]);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [showCelebration, setShowCelebration] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  // Create floating particles effect
  useEffect(() => {
    const createParticles = () => {
      const newParticles = [];
      for (let i = 0; i < 15; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 4 + 2,
          speed: Math.random() * 2 + 1,
          opacity: Math.random() * 0.5 + 0.2,
        });
      }
      setParticles(newParticles);
    };
    createParticles();

    // Mouse tracking for interactive effects
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Resend cooldown timer
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const generateOtp = () => {
    const otp = Math.floor(100000 + Math.random() * 900000); // Generate a 6-digit OTP
    setGeneratedOtp(otp);
    return otp;
  };

  const sendOtpEmail = async (email, otp) => {
    try {
      console.log('🚀 Attempting to send OTP email via AWS SES...', {
        to: email,
        otp: otp
      });

      const result = await AWSEmail.sendOTP(email, otp);

      if (result.success) {
        console.log('✅ OTP email sent successfully via AWS SES!');
        setError(''); // Clear any previous errors
        return true;
      } else {
        console.error('❌ AWS SES email sending failed:', result.error);
        setError('Failed to send verification email. Please try again.');
        return false;
      }
    } catch (err) {
      console.error('❌ OTP email sending failed:', err);
      setError('Failed to send verification email. Please check your internet connection and try again.');
      return false;
    }
  };

  const handleResendOtp = async () => {
    if (resendCooldown > 0) return;

    setResendCooldown(60); // 60 second cooldown
    const otp = generateOtp();
    const emailSent = await sendOtpEmail(email, otp);

    if (emailSent) {
      setError(''); // Clear any errors
      console.log('✅ OTP resent successfully!');
    }
  };

  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfilePic(reader.result); // Store image in base64
      };
      reader.readAsDataURL(file); // Convert file to base64
    }
  };

  const handleSignup = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Upload profile picture to S3 if provided
      let profilePicUrl = null;
      if (profilePic) {
        const file = dataURLtoFile(profilePic, `profile-${Date.now()}.jpg`);
        const uploadResult = await AWSStorage.uploadFile(file, `profiles/${email}-${Date.now()}.jpg`);
        if (uploadResult.success) {
          const urlResult = await AWSStorage.getFileUrl(uploadResult.key);
          if (urlResult.success) {
            profilePicUrl = urlResult.url;
          }
        }
      }

      // Create user account with AWS Cognito
      const signUpResult = await AWSAuth.signUp(email, password, profilePicUrl);

      if (signUpResult.success) {
        setIsOtpSent(true);
        setResendCooldown(60); // Start 60 second cooldown

        // Save additional user data to DynamoDB
        const userData = {
          email: email,
          createdAt: new Date().toISOString(),
          profilePic: profilePicUrl,
          isVerified: false
        };

        await AWSDatabase.createUser(userData);

        // Show success message
        setError(''); // Clear any errors
        console.log('✅ Account created successfully! Please check your email for verification code.');
      } else {
        setError(signUpResult.error);
      }

    } catch (error) {
      console.error('❌ Signup failed:', error);
      setError(error.message || 'Signup failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to convert data URL to File
  const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };

  const handleOtpVerification = async () => {
    if (!otp) {
      setError('Please enter the verification code');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Confirm signup with AWS Cognito
      const confirmResult = await AWSAuth.confirmSignUp(email, otp);

      if (confirmResult.success) {
        setShowCelebration(true);

        // Update user verification status in DynamoDB
        const userData = { isVerified: true };
        await AWSDatabase.updateUser(email, userData);

        setTimeout(() => {
          setIsOtpSent(false);
          setOtp('');
          setIsSignupMode(false);
          setError('');
          setShowCelebration(false);
          alert('🎉 Email Verified! You can now login with your credentials.');
        }, 2000);
      } else {
        setError(confirmResult.error || 'Invalid verification code. Please try again.');
      }
    } catch (error) {
      console.error('❌ OTP verification failed:', error);
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Sign in with AWS Cognito
      const signInResult = await AWSAuth.signIn(email, password);

      if (signInResult.success) {
        console.log('✅ Login successful!');
        onAuthSuccess();
      } else {
        setError(signInResult.error || 'Login failed. Please check your credentials.');
      }
    } catch (error) {
      console.error('❌ Login failed:', error);
      setError(error.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      ...styles.authPageContainer,
      background: `
        linear-gradient(135deg, ${styles.currentTheme.primary}15 0%, ${styles.currentTheme.accent}10 25%, ${styles.currentTheme.primaryLight}20 50%, ${styles.currentTheme.secondary}30 75%, ${styles.currentTheme.primary}10 100%),
        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${styles.currentTheme.primary}20 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, ${styles.currentTheme.accent}20 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, ${styles.currentTheme.primaryLight}15 0%, transparent 50%)
      `,
    }}>
      {/* Animated Background */}
      <div style={{
        ...styles.authBackgroundAnimation,
        transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,
      }}></div>

      {/* Floating Particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          style={{
            position: 'absolute',
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            background: styles.currentTheme.primary,
            borderRadius: '50%',
            opacity: particle.opacity,
            animation: `floatParticle${(particle.id % 2) + 1} ${12 + particle.speed * 3}s infinite linear`,
            zIndex: 1,
            pointerEvents: 'none',
          }}
        />
      ))}

      {/* Celebration Effect */}
      {showCelebration && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: 'none',
          zIndex: 1000,
        }}>
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              style={{
                position: 'absolute',
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                fontSize: '2rem',
                animation: `confetti ${2 + Math.random() * 2}s ease-out forwards`,
                animationDelay: `${Math.random() * 0.5}s`,
              }}
            >
              {['🎉', '✨', '🎊', '🌟', '💫'][Math.floor(Math.random() * 5)]}
            </div>
          ))}
        </div>
      )}

      {/* Branding Section */}
      <div style={styles.authBrandingSection}>
        <img
          src={require('./eduai-logo.jpg')}
          alt="EduNova Logo"
          style={styles.authBrandingLogo}
        />
        <h2 style={styles.authBrandingTitle}>EDU NOVA</h2>
        <p style={styles.authBrandingSubtitle}>AI POWERED LEARNING SYSTEM</p>
        <div style={styles.authBrandingFeatures}>
          <div style={styles.authBrandingFeature}>
            <span>🎯</span>
            <span>Personalized Learning</span>
          </div>
          <div style={styles.authBrandingFeature}>
            <span>🚀</span>
            <span>AI-Powered Assistance</span>
          </div>
          <div style={styles.authBrandingFeature}>
            <span>📊</span>
            <span>Progress Tracking</span>
          </div>
          <div style={styles.authBrandingFeature}>
            <span>🎓</span>
            <span>Expert Content</span>
          </div>
        </div>
      </div>

      {/* Welcome Section */}
      <div style={styles.authWelcomeSection}>
        <h3 style={styles.authWelcomeTitle}>🌟 Why Join EduNova?</h3>
        <p style={styles.authWelcomeText}>
          Transform your learning journey with our cutting-edge AI platform
        </p>
        <div style={styles.authBenefitsList}>
          <div style={styles.authBenefitItem}>
            <span>✨</span>
            <span>Smart Study Plans</span>
          </div>
          <div style={styles.authBenefitItem}>
            <span>💡</span>
            <span>Instant Doubt Resolution</span>
          </div>
          <div style={styles.authBenefitItem}>
            <span>🏆</span>
            <span>Achievement Tracking</span>
          </div>
          <div style={styles.authBenefitItem}>
            <span>🌐</span>
            <span>24/7 Learning Support</span>
          </div>
        </div>
      </div>

      {/* Main Form Container */}
      <div style={styles.authFormContainer}>
        <h1 style={styles.authFormTitle}>
          {isSignupMode ? '✨ Create Account' : '🎯 Welcome Back'}
        </h1>
        <p style={styles.authFormSubtitle}>
          {isSignupMode
            ? '🚀 Join our learning platform and start your amazing journey'
            : '💫 Sign in to continue your learning adventure'
          }
        </p>

        {/* Progress Indicator */}
        {isSignupMode && !isOtpSent && (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '2rem',
            gap: '0.5rem',
          }}>
            <div style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: email && password ? styles.currentTheme.primary : styles.currentTheme.border,
              transition: 'all 0.3s ease',
              animation: email && password ? 'pulse 2s infinite' : 'none',
            }}></div>
            <div style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: profilePic ? styles.currentTheme.primary : styles.currentTheme.border,
              transition: 'all 0.3s ease',
              animation: profilePic ? 'pulse 2s infinite' : 'none',
            }}></div>
            <div style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: styles.currentTheme.border,
              transition: 'all 0.3s ease',
            }}></div>
          </div>
        )}

        {!isOtpSent ? (
          <>
            {/* Email Input */}
            <div style={styles.authInputGroup}>
              <div style={{ position: 'relative' }}>
                <span style={{
                  position: 'absolute',
                  left: '1.25rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '1.2rem',
                  color: styles.currentTheme.textLight,
                  zIndex: 1,
                }}>
                  📧
                </span>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  style={{
                    ...styles.authInput,
                    paddingLeft: '3.5rem',
                  }}
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Password Input */}
            <div style={styles.authInputGroup}>
              <div style={{ position: 'relative' }}>
                <span style={{
                  position: 'absolute',
                  left: '1.25rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '1.2rem',
                  color: styles.currentTheme.textLight,
                  zIndex: 1,
                }}>
                  🔒
                </span>
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  style={{
                    ...styles.authInput,
                    paddingLeft: '3.5rem',
                    paddingRight: '3.5rem',
                  }}
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    right: '1.25rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: 'none',
                    border: 'none',
                    fontSize: '1.2rem',
                    cursor: 'pointer',
                    color: styles.currentTheme.textLight,
                    zIndex: 1,
                  }}
                  disabled={isLoading}
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>
            </div>

            {/* Profile Picture Upload (only for signup) */}
            {isSignupMode && (
              <div style={styles.authInputGroup}>
                <div style={{ textAlign: 'center' }}>
                  <label style={{
                    display: 'block',
                    padding: '1.5rem',
                    border: `2px dashed ${styles.currentTheme.primary}`,
                    borderRadius: '16px',
                    background: 'rgba(255, 255, 255, 0.5)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    ':hover': {
                      background: 'rgba(255, 255, 255, 0.8)',
                      transform: 'translateY(-2px)',
                    }
                  }}>
                    <span style={{ fontSize: '2rem', display: 'block', marginBottom: '0.5rem' }}>
                      📸
                    </span>
                    <span style={{ color: styles.currentTheme.textLight, fontSize: '0.9rem' }}>
                      {profilePic ? 'Change Profile Picture' : 'Upload Profile Picture'}
                    </span>
                    <input
                      type="file"
                      onChange={handleProfilePicChange}
                      accept="image/*"
                      style={{ display: 'none' }}
                      disabled={isLoading}
                    />
                  </label>
                  {profilePic && (
                    <div style={{ marginTop: '1rem', position: 'relative', display: 'inline-block' }}>
                      <img
                        src={profilePic}
                        alt="Profile Preview"
                        style={{
                          ...styles.authProfilePreview,
                          animation: 'fadeIn 0.5s ease',
                        }}
                      />
                      <div style={{
                        position: 'absolute',
                        top: '-5px',
                        right: '-5px',
                        background: styles.currentTheme.primary,
                        color: 'white',
                        borderRadius: '50%',
                        width: '24px',
                        height: '24px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.8rem',
                        animation: 'pulse 2s infinite',
                      }}>
                        ✓
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div style={styles.authButtonGroup}>
              {isSignupMode ? (
                <>
                  <button
                    onClick={handleSignup}
                    disabled={isLoading}
                    style={styles.authButtonPrimary}
                  >
                    {isLoading && <div style={styles.authLoadingSpinner}></div>}
                    {isLoading ? '🚀 Creating Magic...' : '✨ Create Account'}
                  </button>
                  <button
                    onClick={() => setIsSignupMode(false)}
                    disabled={isLoading}
                    style={styles.authButtonSecondary}
                  >
                    🔑 Login Instead
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={handleLogin}
                    disabled={isLoading}
                    style={styles.authButtonPrimary}
                  >
                    {isLoading && <div style={styles.authLoadingSpinner}></div>}
                    {isLoading ? '🎯 Signing In...' : '🚀 Sign In'}
                  </button>
                  <button
                    onClick={() => setIsSignupMode(true)}
                    disabled={isLoading}
                    style={styles.authButtonSecondary}
                  >
                    ✨ Create Account
                  </button>
                </>
              )}
            </div>
          </>
        ) : (
          /* OTP Verification Section */
          <div style={styles.authOtpContainer}>
            <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
              <div style={{
                fontSize: '3rem',
                marginBottom: '1rem',
                animation: 'pulse 2s infinite',
              }}>
                📧
              </div>
              <h3 style={styles.authOtpTitle}>🔐 Verify Your Email</h3>
              <p style={{ color: styles.currentTheme.textLight, marginBottom: '1rem' }}>
                ✨ We've sent a magical 6-digit code to<br />
                <strong style={{ color: styles.currentTheme.primary }}>{email}</strong>
              </p>
              {/* Development Mode Info */}
              {process.env.NODE_ENV === 'development' && (
                <div style={{
                  background: '#FFF3CD',
                  border: '1px solid #FFEAA7',
                  borderRadius: '8px',
                  padding: '0.75rem',
                  marginBottom: '1rem',
                  fontSize: '0.85rem',
                  color: '#856404'
                }}>
                  <strong>🔧 Development Mode:</strong><br />
                  Check browser console for OTP if email fails.<br />
                  Generated OTP: <strong>{generatedOtp}</strong>
                </div>
              )}
            </div>
            <div style={{ position: 'relative' }}>
              <span style={{
                position: 'absolute',
                left: '1rem',
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '1.2rem',
                color: styles.currentTheme.textLight,
                zIndex: 1,
              }}>
                🔢
              </span>
              <input
                type="text"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                placeholder="Enter 6-digit code"
                maxLength="6"
                style={{
                  ...styles.authOtpInput,
                  paddingLeft: '3rem',
                }}
              />
            </div>
            <div style={{ display: 'flex', gap: '1rem', flexDirection: 'column' }}>
              <button
                onClick={handleOtpVerification}
                style={{
                  ...styles.authOtpButton,
                  background: styles.currentTheme.gradient,
                  animation: otp.length === 6 ? 'buttonPulse 2s infinite' : 'none',
                }}
              >
                🎯 Verify & Continue
              </button>

              <div style={{ textAlign: 'center' }}>
                <p style={{
                  fontSize: '0.85rem',
                  color: styles.currentTheme.textLight,
                  marginBottom: '0.5rem'
                }}>
                  Didn't receive the code?
                </p>
                <button
                  onClick={handleResendOtp}
                  disabled={resendCooldown > 0}
                  style={{
                    background: 'none',
                    border: `1px solid ${styles.currentTheme.primary}`,
                    color: resendCooldown > 0 ? styles.currentTheme.textLight : styles.currentTheme.primary,
                    padding: '0.5rem 1rem',
                    borderRadius: '8px',
                    fontSize: '0.85rem',
                    cursor: resendCooldown > 0 ? 'not-allowed' : 'pointer',
                    transition: 'all 0.3s ease',
                    opacity: resendCooldown > 0 ? 0.6 : 1,
                  }}
                >
                  {resendCooldown > 0
                    ? `🕐 Resend in ${resendCooldown}s`
                    : '📧 Resend Code'
                  }
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div style={{
            ...styles.authErrorMessage,
            animation: 'slideIn 0.3s ease',
          }}>
            <span style={{ marginRight: '0.5rem' }}>⚠️</span>
            {error}
          </div>
        )}

        {/* Success Indicators */}
        {email && password && !error && (
          <div style={{
            textAlign: 'center',
            marginTop: '1rem',
            color: styles.currentTheme.primary,
            fontSize: '0.9rem',
            animation: 'fadeIn 0.5s ease',
          }}>
            ✅ Ready to {isSignupMode ? 'create your account' : 'sign in'}!
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthPage;