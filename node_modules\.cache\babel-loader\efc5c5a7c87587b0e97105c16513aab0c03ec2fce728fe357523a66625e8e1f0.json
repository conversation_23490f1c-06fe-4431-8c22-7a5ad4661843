{"ast": null, "code": "/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */\n\n/**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */\n\n/**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  /** @type {Array<string>} */\n  const tokens = [];\n  const input = String(value || '');\n  let index = input.indexOf(',');\n  let start = 0;\n  /** @type {boolean} */\n  let end = false;\n  while (!end) {\n    if (index === -1) {\n      index = input.length;\n      end = true;\n    }\n    const token = input.slice(start, index).trim();\n    if (token || !end) {\n      tokens.push(token);\n    }\n    start = index + 1;\n    index = input.indexOf(',', start);\n  }\n  return tokens;\n}\n\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */\nexport function stringify(values, options) {\n  const settings = options || {};\n\n  // Ensure the last empty entry is seen.\n  const input = values[values.length - 1] === '' ? [...values, ''] : values;\n  return input.join((settings.padRight ? ' ' : '') + ',' + (settings.padLeft === false ? '' : ' ')).trim();\n}", "map": {"version": 3, "names": ["parse", "value", "tokens", "input", "String", "index", "indexOf", "start", "end", "length", "token", "slice", "trim", "push", "stringify", "values", "options", "settings", "join", "padRight", "padLeft"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/node_modules/comma-separated-tokens/index.js"], "sourcesContent": ["/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */\n\n/**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */\n\n/**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  /** @type {Array<string>} */\n  const tokens = []\n  const input = String(value || '')\n  let index = input.indexOf(',')\n  let start = 0\n  /** @type {boolean} */\n  let end = false\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    const token = input.slice(start, index).trim()\n\n    if (token || !end) {\n      tokens.push(token)\n    }\n\n    start = index + 1\n    index = input.indexOf(',', start)\n  }\n\n  return tokens\n}\n\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */\nexport function stringify(values, options) {\n  const settings = options || {}\n\n  // Ensure the last empty entry is seen.\n  const input = values[values.length - 1] === '' ? [...values, ''] : values\n\n  return input\n    .join(\n      (settings.padRight ? ' ' : '') +\n        ',' +\n        (settings.padLeft === false ? '' : ' ')\n    )\n    .trim()\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,KAAKA,CAACC,KAAK,EAAE;EAC3B;EACA,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,KAAK,GAAGC,MAAM,CAACH,KAAK,IAAI,EAAE,CAAC;EACjC,IAAII,KAAK,GAAGF,KAAK,CAACG,OAAO,CAAC,GAAG,CAAC;EAC9B,IAAIC,KAAK,GAAG,CAAC;EACb;EACA,IAAIC,GAAG,GAAG,KAAK;EAEf,OAAO,CAACA,GAAG,EAAE;IACX,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBA,KAAK,GAAGF,KAAK,CAACM,MAAM;MACpBD,GAAG,GAAG,IAAI;IACZ;IAEA,MAAME,KAAK,GAAGP,KAAK,CAACQ,KAAK,CAACJ,KAAK,EAAEF,KAAK,CAAC,CAACO,IAAI,CAAC,CAAC;IAE9C,IAAIF,KAAK,IAAI,CAACF,GAAG,EAAE;MACjBN,MAAM,CAACW,IAAI,CAACH,KAAK,CAAC;IACpB;IAEAH,KAAK,GAAGF,KAAK,GAAG,CAAC;IACjBA,KAAK,GAAGF,KAAK,CAACG,OAAO,CAAC,GAAG,EAAEC,KAAK,CAAC;EACnC;EAEA,OAAOL,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASY,SAASA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACzC,MAAMC,QAAQ,GAAGD,OAAO,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMb,KAAK,GAAGY,MAAM,CAACA,MAAM,CAACN,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAGM,MAAM,EAAE,EAAE,CAAC,GAAGA,MAAM;EAEzE,OAAOZ,KAAK,CACTe,IAAI,CACH,CAACD,QAAQ,CAACE,QAAQ,GAAG,GAAG,GAAG,EAAE,IAC3B,GAAG,IACFF,QAAQ,CAACG,OAAO,KAAK,KAAK,GAAG,EAAE,GAAG,GAAG,CAC1C,CAAC,CACAR,IAAI,CAAC,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}