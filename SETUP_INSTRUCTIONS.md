# 🚀 AWS Setup Instructions for Your Project

## 📋 **Current Status**
✅ AWS dependencies installed (`aws-amplify`, `aws-sdk`)  
⏳ AWS CLI installation in progress  
📝 Ready for AWS configuration  

## 🔧 **Step-by-Step Setup Guide**

### **Step 1: Complete AWS CLI Installation**

#### **Option A: Manual Download (Recommended)**
1. Download AWS CLI: https://awscli.amazonaws.com/AWSCLIV2.msi
2. Run the installer as Administrator
3. Restart your terminal/PowerShell
4. Verify: `aws --version`

#### **Option B: Check if Already Installed**
Open a **new** PowerShell window and run:
```powershell
aws --version
```

### **Step 2: Create AWS Account (if you don't have one)**
1. Go to: https://aws.amazon.com/
2. Click "Create an AWS Account"
3. Follow the signup process (requires credit card but won't be charged for free tier)

### **Step 3: Create IAM User for CLI Access**
1. Login to AWS Console: https://console.aws.amazon.com/
2. Go to IAM service
3. Click "Users" → "Create user"
4. Username: `edunova-cli-user`
5. Select "Programmatic access"
6. Attach policies:
   - `AmazonCognitoPowerUser`
   - `AmazonS3FullAccess`
   - `AWSLambda_FullAccess`
   - `AmazonDynamoDBFullAccess`
   - `AmazonSESFullAccess`
   - `AmazonAPIGatewayAdministrator`
   - `IAMFullAccess`
7. **Save the Access Key ID and Secret Access Key**

### **Step 4: Configure AWS CLI**
```bash
aws configure
```
Enter:
- **AWS Access Key ID**: [Your Access Key]
- **AWS Secret Access Key**: [Your Secret Key]
- **Default region**: `us-east-1`
- **Default output format**: `json`

### **Step 5: Run Automated Setup**
```bash
npm run aws-setup
```

## 🎯 **What the Setup Script Will Create**

### **AWS Resources:**
- ✅ **Cognito User Pool** - Authentication
- ✅ **S3 Bucket** - File storage
- ✅ **DynamoDB Table** - User database
- ✅ **Lambda Functions** - Backend APIs
- ✅ **API Gateway** - REST endpoints
- ✅ **SES Configuration** - Email service

### **Generated Files:**
- ✅ `.env` - Environment configuration
- ✅ AWS resource IDs and endpoints

## 🚀 **Quick Test Commands**

After setup, test your configuration:

```bash
# Test AWS CLI
aws sts get-caller-identity

# Test your app
npm start
```

## 💰 **Free Tier Limits (Monthly)**
- **Cognito**: 50,000 active users
- **Lambda**: 1M requests + 400,000 GB-seconds
- **DynamoDB**: 25GB storage + 25 RCU/WCU
- **S3**: 5GB storage + 20,000 GET + 2,000 PUT
- **SES**: 62,000 emails
- **API Gateway**: 1M API calls

## 🆘 **Troubleshooting**

### **AWS CLI Not Found**
```bash
# Add to PATH (Windows)
$env:PATH += ";C:\Program Files\Amazon\AWSCLIV2"

# Or restart PowerShell/Terminal
```

### **Permission Denied**
- Ensure IAM user has required policies
- Check AWS credentials are correct

### **Region Issues**
- Use `us-east-1` for best free tier coverage
- Some services are region-specific

## 📞 **Need Help?**

If you encounter any issues:
1. Check AWS CLI installation: `aws --version`
2. Verify credentials: `aws sts get-caller-identity`
3. Check region: `aws configure get region`
4. Review IAM permissions in AWS Console

## 🎉 **Next Steps**

Once AWS CLI is working:
1. Run `npm run aws-setup`
2. Wait for resources to be created (~5-10 minutes)
3. Start your app with `npm start`
4. Test authentication and features

Your app will be running on professional AWS infrastructure at **$0 cost**! 🚀
