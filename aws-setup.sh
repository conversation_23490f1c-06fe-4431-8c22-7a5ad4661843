#!/bin/bash

# AWS Setup Script for EduNova Project
# This script sets up all required AWS services using AWS CLI

echo "🚀 Setting up AWS services for EduNova..."

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is logged in to AWS
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

# Variables
PROJECT_NAME="edunova"
REGION="us-east-1"
BUCKET_NAME="${PROJECT_NAME}-storage-$(date +%s)"
USER_POOL_NAME="${PROJECT_NAME}-user-pool"
TABLE_NAME="${PROJECT_NAME}-users"

echo "📋 Configuration:"
echo "  Project: $PROJECT_NAME"
echo "  Region: $REGION"
echo "  S3 Bucket: $BUCKET_NAME"
echo "  User Pool: $USER_POOL_NAME"
echo "  DynamoDB Table: $TABLE_NAME"
echo ""

# 1. Create S3 Bucket for file storage
echo "📦 Creating S3 bucket..."
aws s3 mb s3://$BUCKET_NAME --region $REGION

# Configure S3 bucket for public read access (for profile pictures)
aws s3api put-bucket-cors --bucket $BUCKET_NAME --cors-configuration '{
    "CORSRules": [
        {
            "AllowedHeaders": ["*"],
            "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
            "AllowedOrigins": ["*"],
            "ExposeHeaders": []
        }
    ]
}'

echo "✅ S3 bucket created: $BUCKET_NAME"

# 2. Create Cognito User Pool
echo "👥 Creating Cognito User Pool..."
USER_POOL_ID=$(aws cognito-idp create-user-pool \
    --pool-name $USER_POOL_NAME \
    --policies '{
        "PasswordPolicy": {
            "MinimumLength": 8,
            "RequireUppercase": false,
            "RequireLowercase": false,
            "RequireNumbers": false,
            "RequireSymbols": false
        }
    }' \
    --auto-verified-attributes email \
    --verification-message-template '{
        "DefaultEmailOption": "CONFIRM_WITH_CODE",
        "EmailSubject": "EduNova - Verify your email",
        "EmailMessage": "Your verification code is {####}"
    }' \
    --query 'UserPool.Id' \
    --output text)

echo "✅ User Pool created: $USER_POOL_ID"

# 3. Create Cognito User Pool Client
echo "📱 Creating User Pool Client..."
USER_POOL_CLIENT_ID=$(aws cognito-idp create-user-pool-client \
    --user-pool-id $USER_POOL_ID \
    --client-name "${PROJECT_NAME}-client" \
    --no-generate-secret \
    --explicit-auth-flows ADMIN_NO_SRP_AUTH ALLOW_USER_PASSWORD_AUTH ALLOW_REFRESH_TOKEN_AUTH \
    --query 'UserPoolClient.ClientId' \
    --output text)

echo "✅ User Pool Client created: $USER_POOL_CLIENT_ID"

# 4. Create DynamoDB Table
echo "🗄️ Creating DynamoDB table..."
aws dynamodb create-table \
    --table-name $TABLE_NAME \
    --attribute-definitions \
        AttributeName=userId,AttributeType=S \
    --key-schema \
        AttributeName=userId,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST \
    --region $REGION

echo "✅ DynamoDB table created: $TABLE_NAME"

# 5. Create IAM Role for Lambda
echo "🔐 Creating IAM role for Lambda..."
LAMBDA_ROLE_NAME="${PROJECT_NAME}-lambda-role"

# Create trust policy
cat > trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "lambda.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF

# Create the role
aws iam create-role \
    --role-name $LAMBDA_ROLE_NAME \
    --assume-role-policy-document file://trust-policy.json

# Attach policies
aws iam attach-role-policy \
    --role-name $LAMBDA_ROLE_NAME \
    --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

aws iam attach-role-policy \
    --role-name $LAMBDA_ROLE_NAME \
    --policy-arn arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess

aws iam attach-role-policy \
    --role-name $LAMBDA_ROLE_NAME \
    --policy-arn arn:aws:iam::aws:policy/AmazonSESFullAccess

# Get role ARN
LAMBDA_ROLE_ARN=$(aws iam get-role --role-name $LAMBDA_ROLE_NAME --query 'Role.Arn' --output text)

echo "✅ IAM role created: $LAMBDA_ROLE_ARN"

# Wait for role to be ready
echo "⏳ Waiting for IAM role to be ready..."
sleep 10

# 6. Create Lambda Functions
echo "⚡ Creating Lambda functions..."

# Create deployment packages
cd aws-lambda/send-otp
zip -r send-otp.zip index.js
cd ../..

cd aws-lambda/user-management
zip -r user-management.zip index.js
cd ../..

# Create send-otp Lambda function
aws lambda create-function \
    --function-name "${PROJECT_NAME}-send-otp" \
    --runtime nodejs18.x \
    --role $LAMBDA_ROLE_ARN \
    --handler index.handler \
    --zip-file fileb://aws-lambda/send-otp/send-otp.zip \
    --environment Variables="{USERS_TABLE=$TABLE_NAME,FROM_EMAIL=<EMAIL>}" \
    --timeout 30

# Create user-management Lambda function
aws lambda create-function \
    --function-name "${PROJECT_NAME}-user-management" \
    --runtime nodejs18.x \
    --role $LAMBDA_ROLE_ARN \
    --handler index.handler \
    --zip-file fileb://aws-lambda/user-management/user-management.zip \
    --environment Variables="{USERS_TABLE=$TABLE_NAME}" \
    --timeout 30

echo "✅ Lambda functions created"

# 7. Create API Gateway
echo "🌐 Creating API Gateway..."
API_ID=$(aws apigatewayv2 create-api \
    --name "${PROJECT_NAME}-api" \
    --protocol-type HTTP \
    --cors-configuration AllowCredentials=false,AllowHeaders="*",AllowMethods="*",AllowOrigins="*" \
    --query 'ApiId' \
    --output text)

echo "✅ API Gateway created: $API_ID"

# Get Lambda function ARNs
SEND_OTP_ARN=$(aws lambda get-function --function-name "${PROJECT_NAME}-send-otp" --query 'Configuration.FunctionArn' --output text)
USER_MGMT_ARN=$(aws lambda get-function --function-name "${PROJECT_NAME}-user-management" --query 'Configuration.FunctionArn' --output text)

# Create integrations
SEND_OTP_INTEGRATION_ID=$(aws apigatewayv2 create-integration \
    --api-id $API_ID \
    --integration-type AWS_PROXY \
    --integration-uri $SEND_OTP_ARN \
    --payload-format-version 2.0 \
    --query 'IntegrationId' \
    --output text)

USER_MGMT_INTEGRATION_ID=$(aws apigatewayv2 create-integration \
    --api-id $API_ID \
    --integration-type AWS_PROXY \
    --integration-uri $USER_MGMT_ARN \
    --payload-format-version 2.0 \
    --query 'IntegrationId' \
    --output text)

# Create routes
aws apigatewayv2 create-route \
    --api-id $API_ID \
    --route-key "POST /send-otp" \
    --target "integrations/$SEND_OTP_INTEGRATION_ID"

aws apigatewayv2 create-route \
    --api-id $API_ID \
    --route-key "POST /users" \
    --target "integrations/$USER_MGMT_INTEGRATION_ID"

aws apigatewayv2 create-route \
    --api-id $API_ID \
    --route-key "GET /users/{userId}" \
    --target "integrations/$USER_MGMT_INTEGRATION_ID"

aws apigatewayv2 create-route \
    --api-id $API_ID \
    --route-key "PUT /users/{userId}" \
    --target "integrations/$USER_MGMT_INTEGRATION_ID"

# Create stage
aws apigatewayv2 create-stage \
    --api-id $API_ID \
    --stage-name prod \
    --auto-deploy

# Add Lambda permissions
aws lambda add-permission \
    --function-name "${PROJECT_NAME}-send-otp" \
    --statement-id api-gateway-invoke \
    --action lambda:InvokeFunction \
    --principal apigateway.amazonaws.com \
    --source-arn "arn:aws:execute-api:$REGION:$(aws sts get-caller-identity --query Account --output text):$API_ID/*/*"

aws lambda add-permission \
    --function-name "${PROJECT_NAME}-user-management" \
    --statement-id api-gateway-invoke \
    --action lambda:InvokeFunction \
    --principal apigateway.amazonaws.com \
    --source-arn "arn:aws:execute-api:$REGION:$(aws sts get-caller-identity --query Account --output text):$API_ID/*/*"

# Get API endpoint
API_ENDPOINT="https://$API_ID.execute-api.$REGION.amazonaws.com/prod"

echo "✅ API Gateway setup complete: $API_ENDPOINT"

# 8. Generate environment file
echo "📝 Generating environment configuration..."
cat > .env << EOF
# AWS Configuration
REACT_APP_AWS_REGION=$REGION
REACT_APP_USER_POOL_ID=$USER_POOL_ID
REACT_APP_USER_POOL_CLIENT_ID=$USER_POOL_CLIENT_ID
REACT_APP_API_ENDPOINT=$API_ENDPOINT
REACT_APP_S3_BUCKET=$BUCKET_NAME
EOF

echo "✅ Environment file created: .env"

# Cleanup
rm -f trust-policy.json
rm -f aws-lambda/send-otp/send-otp.zip
rm -f aws-lambda/user-management/user-management.zip

echo ""
echo "🎉 AWS setup complete!"
echo ""
echo "📋 Summary:"
echo "  ✅ S3 Bucket: $BUCKET_NAME"
echo "  ✅ Cognito User Pool: $USER_POOL_ID"
echo "  ✅ User Pool Client: $USER_POOL_CLIENT_ID"
echo "  ✅ DynamoDB Table: $TABLE_NAME"
echo "  ✅ API Gateway: $API_ENDPOINT"
echo "  ✅ Environment file: .env"
echo ""
echo "🚀 Next steps:"
echo "  1. Run 'npm install' to install AWS dependencies"
echo "  2. Run 'npm start' to start your application"
echo "  3. Your app will now use AWS services instead of Firebase!"
echo ""
