{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\quiz\\\\aich (4)\\\\aich (3)\\\\aich\\\\src\\\\AcademicDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiBook, FiCheckCircle, FiLoader, FiAlertCircle, FiPlus, FiSettings, FiStar, FiUser, FiCalendar, FiFileText, FiBookOpen, FiClipboard, FiTrendingUp, FiMaximize2, FiSave, FiCpu, FiX, FiSend, FiTarget, FiZap, FiDownload, FiTrash2, FiUpload } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AcademicDashboard = () => {\n  _s();\n  // Chatbot states\n  const [chatbotOpen, setChatbotOpen] = useState(false);\n  const [chatMessages, setChatMessages] = useState([{\n    text: \"Hello! I'm your EduAI assistant. How can I help you with your academics today?\",\n    isUser: false\n  }]);\n  const [chatInput, setChatInput] = useState('');\n\n  // Modal states\n  const [showAddCourseModal, setShowAddCourseModal] = useState(false);\n  const [showAddExamModal, setShowAddExamModal] = useState(false);\n  const [showCourseDetailsModal, setShowCourseDetailsModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [showStudyMaterialsModal, setShowStudyMaterialsModal] = useState(false);\n  const [showAssignmentsModal, setShowAssignmentsModal] = useState(false);\n  const [showGradesModal, setShowGradesModal] = useState(false);\n  const [showScheduleModal, setShowScheduleModal] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n\n  // Filter states\n  const [courseFilter, setCourseFilter] = useState('All');\n\n  // Data states\n  const [courses, setCourses] = useState([{\n    id: 1,\n    subject: 'Computer Science',\n    title: 'Data Structures & Algorithms',\n    description: 'Master fundamental algorithms and problem-solving techniques',\n    professor: 'Prof. Smith',\n    duration: '12 Weeks',\n    rating: 4.8,\n    color: '#DC2626',\n    bgColor: '#FEE2E2',\n    status: 'Active',\n    progress: 82,\n    materials: ['Textbook Chapter 1-5', 'Video Lectures', 'Practice Problems'],\n    assignments: ['Assignment 1: Arrays', 'Assignment 2: Linked Lists']\n  }, {\n    id: 2,\n    subject: 'Mathematics',\n    title: 'Linear Algebra',\n    description: 'Vectors, matrices, and their applications in computer science',\n    professor: 'Prof. Johnson',\n    duration: '8 Weeks',\n    rating: 4.5,\n    color: '#7C3AED',\n    bgColor: '#EDE9FE',\n    status: 'Active',\n    progress: 90,\n    materials: ['Linear Algebra Textbook', 'Khan Academy Videos'],\n    assignments: ['Matrix Operations Quiz', 'Eigenvalues Problem Set']\n  }, {\n    id: 3,\n    subject: 'Physics',\n    title: 'Quantum Mechanics',\n    description: 'Introduction to quantum theory and its applications',\n    professor: 'Prof. Williams',\n    duration: '10 Weeks',\n    rating: 4.2,\n    color: '#EA580C',\n    bgColor: '#FED7AA',\n    status: 'Active',\n    progress: 65,\n    materials: ['Quantum Physics Textbook', 'Lab Manual'],\n    assignments: ['Wave Function Analysis', 'Quantum States Problem']\n  }, {\n    id: 4,\n    subject: 'Literature',\n    title: 'Modern Poetry',\n    description: 'Analysis of 20th century poetry and poetic techniques',\n    professor: 'Prof. Brown',\n    duration: '6 Weeks',\n    rating: 4.7,\n    color: '#6B7280',\n    bgColor: '#F3F4F6',\n    status: 'Completed',\n    progress: 100,\n    materials: ['Poetry Anthology', 'Critical Essays'],\n    assignments: ['Poetry Analysis Essay', 'Creative Writing Assignment']\n  }]);\n  const [exams, setExams] = useState([{\n    id: 1,\n    title: 'Midterm Exam - Data Structures',\n    description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n    date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n    timeLeft: '3 Days Left',\n    icon: FiFileText,\n    color: '#DC2626',\n    bgColor: '#FEE2E2',\n    courseId: 1\n  }, {\n    id: 2,\n    title: 'Quiz 2 - Linear Algebra',\n    description: 'Coverage: Matrix Operations, Determinants',\n    date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n    timeLeft: '1 Week Left',\n    icon: FiTarget,\n    color: '#7C3AED',\n    bgColor: '#EDE9FE',\n    courseId: 2\n  }, {\n    id: 3,\n    title: 'Final Exam - Quantum Mechanics',\n    description: 'Coverage: Entire Semester',\n    date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n    timeLeft: '2 Weeks Left',\n    icon: FiZap,\n    color: '#EA580C',\n    bgColor: '#FED7AA',\n    courseId: 3\n  }]);\n\n  // Form states\n  const [newCourse, setNewCourse] = useState({\n    subject: '',\n    title: '',\n    description: '',\n    professor: '',\n    duration: '',\n    color: '#DC2626'\n  });\n  const [newExam, setNewExam] = useState({\n    title: '',\n    description: '',\n    date: '',\n    courseId: ''\n  });\n\n  // File upload states\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [uploadCourseId, setUploadCourseId] = useState('');\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Animation effect for mindmap nodes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      const nodes = document.querySelectorAll('.mindmap-node');\n      nodes.forEach((node, index) => {\n        node.style.opacity = '0';\n        setTimeout(() => {\n          node.style.opacity = '1';\n          node.style.transition = 'opacity 0.5s ease, transform 0.3s ease';\n        }, 200 * index);\n      });\n    }, 100);\n    return () => clearTimeout(timer);\n  }, []);\n  const handleChatSend = () => {\n    if (chatInput.trim()) {\n      setChatMessages(prev => [...prev, {\n        text: chatInput,\n        isUser: true\n      }]);\n      setChatInput('');\n\n      // Simulate bot response\n      setTimeout(() => {\n        const responses = [\"I can help you with that. What specific aspect do you need assistance with?\", \"That's an interesting question. Let me check my knowledge base...\", \"For that topic, I recommend reviewing chapter 3 of your textbook.\", \"I'm still learning about that subject, but here's what I know...\", \"Would you like me to find study resources for that topic?\"];\n        const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n        setChatMessages(prev => [...prev, {\n          text: randomResponse,\n          isUser: false\n        }]);\n      }, 1000);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleChatSend();\n    }\n  };\n\n  // Functionality handlers\n  const handleAddCourse = () => {\n    if (newCourse.title && newCourse.subject && newCourse.professor) {\n      const course = {\n        id: Date.now(),\n        ...newCourse,\n        rating: 0,\n        bgColor: newCourse.color + '20',\n        status: 'Active',\n        progress: 0,\n        materials: [],\n        assignments: [],\n        grade: 'N/A'\n      };\n      setCourses(prev => [...prev, course]);\n      setNewCourse({\n        subject: '',\n        title: '',\n        description: '',\n        professor: '',\n        duration: '',\n        color: '#DC2626'\n      });\n      setShowAddCourseModal(false);\n      alert('Course added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n  const handleAddExam = () => {\n    if (newExam.title && newExam.date && newExam.courseId) {\n      const exam = {\n        id: Date.now(),\n        ...newExam,\n        timeLeft: calculateTimeLeft(newExam.date),\n        icon: FiFileText,\n        color: '#DC2626',\n        bgColor: '#FEE2E2'\n      };\n      setExams(prev => [...prev, exam]);\n      setNewExam({\n        title: '',\n        description: '',\n        date: '',\n        courseId: ''\n      });\n      setShowAddExamModal(false);\n      alert('Exam added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n  const calculateTimeLeft = examDate => {\n    const today = new Date();\n    const exam = new Date(examDate);\n    const diffTime = exam - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays < 0) return 'Past Due';\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return '1 Day Left';\n    if (diffDays < 7) return `${diffDays} Days Left`;\n    if (diffDays < 14) return '1 Week Left';\n    return `${Math.ceil(diffDays / 7)} Weeks Left`;\n  };\n  const handleCourseClick = course => {\n    setSelectedCourse(course);\n    setShowCourseDetailsModal(true);\n  };\n  const handleFilterChange = filter => {\n    setCourseFilter(filter);\n  };\n  const getFilteredCourses = () => {\n    if (courseFilter === 'All') return courses;\n    return courses.filter(course => course.status === courseFilter);\n  };\n  const calculateStats = () => {\n    const total = courses.length;\n    const completed = courses.filter(c => c.status === 'Completed').length;\n    const active = courses.filter(c => c.status === 'Active').length;\n    const pending = courses.filter(c => c.progress === 0).length;\n    return {\n      total,\n      completed,\n      active,\n      pending\n    };\n  };\n  const stats = calculateStats();\n\n  // File upload handlers\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      // Check if file is PDF\n      if (file.type !== 'application/pdf') {\n        alert('Please select a PDF file only');\n        return;\n      }\n\n      // Check file size (max 10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        alert('File size should be less than 10MB');\n        return;\n      }\n      setSelectedFile(file);\n    }\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile || !uploadCourseId) {\n      alert('Please select a file and course');\n      return;\n    }\n    setIsUploading(true);\n    setUploadProgress(0);\n    try {\n      // Simulate file upload progress\n      const uploadInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(uploadInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Create file URL for preview (in real app, this would be uploaded to server)\n      const fileURL = URL.createObjectURL(selectedFile);\n\n      // Add file to course materials\n      setTimeout(() => {\n        setCourses(prev => prev.map(course => {\n          if (course.id.toString() === uploadCourseId) {\n            return {\n              ...course,\n              materials: [...(course.materials || []), {\n                name: selectedFile.name,\n                type: 'pdf',\n                size: (selectedFile.size / 1024 / 1024).toFixed(2) + ' MB',\n                uploadDate: new Date().toLocaleDateString(),\n                url: fileURL\n              }]\n            };\n          }\n          return course;\n        }));\n        setUploadProgress(100);\n        setTimeout(() => {\n          setIsUploading(false);\n          setShowUploadModal(false);\n          setSelectedFile(null);\n          setUploadCourseId('');\n          setUploadProgress(0);\n          alert('PDF uploaded successfully!');\n        }, 500);\n      }, 2000);\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Upload failed. Please try again.');\n      setIsUploading(false);\n    }\n  };\n  const handleDownloadFile = material => {\n    if (material.url) {\n      // Create download link\n      const link = document.createElement('a');\n      link.href = material.url;\n      link.download = material.name;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } else {\n      alert('File not available for download');\n    }\n  };\n  const handleDeleteFile = (courseId, materialIndex) => {\n    if (window.confirm('Are you sure you want to delete this file?')) {\n      setCourses(prev => prev.map(course => {\n        if (course.id === courseId) {\n          const newMaterials = [...course.materials];\n          newMaterials.splice(materialIndex, 1);\n          return {\n            ...course,\n            materials: newMaterials\n          };\n        }\n        return course;\n      }));\n      alert('File deleted successfully!');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n      padding: '2rem 1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '1200px',\n        margin: '0 auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '2rem',\n          flexWrap: 'wrap',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '2.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0,\n              marginBottom: '0.5rem'\n            },\n            children: \"Academic Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6B7280',\n              margin: 0\n            },\n            children: \"Track your academic progress and resources\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCourseModal(true),\n            style: {\n              background: '#DC2626',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#B91C1C',\n            onMouseLeave: e => e.target.style.background = '#DC2626',\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), \" Add New Course\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSettingsModal(true),\n            style: {\n              background: 'white',\n              color: '#DC2626',\n              border: '2px solid #DC2626',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#FEE2E2',\n            onMouseLeave: e => e.target.style.background = 'white',\n            children: [/*#__PURE__*/_jsxDEV(FiSettings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), \" Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        },\n        children: [{\n          icon: FiBook,\n          label: 'Total Courses',\n          value: stats.total.toString(),\n          color: '#DC2626',\n          bgColor: '#FEE2E2'\n        }, {\n          icon: FiCheckCircle,\n          label: 'Completed',\n          value: stats.completed.toString(),\n          color: '#7C3AED',\n          bgColor: '#EDE9FE'\n        }, {\n          icon: FiLoader,\n          label: 'In Progress',\n          value: stats.active.toString(),\n          color: '#EA580C',\n          bgColor: '#FED7AA'\n        }, {\n          icon: FiAlertCircle,\n          label: 'Pending',\n          value: stats.pending.toString(),\n          color: '#6B7280',\n          bgColor: '#F3F4F6'\n        }].map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '1rem',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n            padding: '1.5rem',\n            display: 'flex',\n            alignItems: 'center',\n            transition: 'transform 0.3s ease',\n            cursor: 'pointer'\n          },\n          onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n          onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: stat.bgColor,\n              padding: '0.75rem',\n              borderRadius: '50%',\n              marginRight: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              size: 24,\n              color: stat.color\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6B7280',\n                fontSize: '0.875rem',\n                margin: 0\n              },\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '2rem',\n                fontWeight: 'bold',\n                color: stat.color,\n                margin: 0\n              },\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '2fr 1fr',\n          gap: '2rem',\n          '@media (max-width: 1024px)': {\n            gridTemplateColumns: '1fr'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  margin: 0\n                },\n                children: \"Your Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '0.5rem'\n                },\n                children: ['All', 'Active', 'Completed'].map((filter, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleFilterChange(filter),\n                  style: {\n                    background: courseFilter === filter ? 'white' : '#B91C1C',\n                    color: courseFilter === filter ? '#DC2626' : 'white',\n                    padding: '0.5rem 1rem',\n                    borderRadius: '0.375rem',\n                    border: 'none',\n                    fontSize: '0.875rem',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease'\n                  },\n                  children: filter\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                  gap: '1rem'\n                },\n                children: getFilteredCourses().map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => handleCourseClick(course),\n                  style: {\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    padding: '1rem',\n                    transition: 'all 0.3s ease',\n                    cursor: 'pointer'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.borderColor = course.color;\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.borderColor = '#E5E7EB';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      marginBottom: '0.75rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        background: course.bgColor,\n                        color: course.color,\n                        fontSize: '0.75rem',\n                        padding: '0.25rem 0.5rem',\n                        borderRadius: '0.25rem'\n                      },\n                      children: course.subject\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#F59E0B',\n                        fontSize: '0.875rem',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 598,\n                        columnNumber: 27\n                      }, this), \" \", course.rating]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      fontWeight: 'bold',\n                      fontSize: '1.125rem',\n                      marginBottom: '0.5rem'\n                    },\n                    children: course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      color: '#6B7280',\n                      fontSize: '0.875rem',\n                      marginBottom: '1rem'\n                    },\n                    children: course.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '2rem',\n                          height: '2rem',\n                          borderRadius: '50%',\n                          background: course.bgColor,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          marginRight: '0.5rem'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(FiUser, {\n                          size: 14,\n                          color: course.color\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 619,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 609,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontSize: '0.875rem'\n                        },\n                        children: course.professor\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 621,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.875rem',\n                        color: '#6B7280'\n                      },\n                      children: course.duration\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowAddCourseModal(true),\n                style: {\n                  marginTop: '1.5rem',\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '2px dashed #D1D5DB',\n                  borderRadius: '0.5rem',\n                  background: 'transparent',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                },\n                onMouseEnter: e => {\n                  e.target.style.borderColor = '#DC2626';\n                  e.target.style.color = '#DC2626';\n                },\n                onMouseLeave: e => {\n                  e.target.style.borderColor = '#D1D5DB';\n                  e.target.style.color = '#6B7280';\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 19\n                }, this), \" Add More Courses\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  margin: 0\n                },\n                children: \"Upcoming Exams\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '1rem'\n                },\n                children: [{\n                  title: 'Midterm Exam - Data Structures',\n                  description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n                  date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n                  timeLeft: '3 Days Left',\n                  icon: FiFileText,\n                  color: '#DC2626',\n                  bgColor: '#FEE2E2'\n                }, {\n                  title: 'Quiz 2 - Linear Algebra',\n                  description: 'Coverage: Matrix Operations, Determinants',\n                  date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n                  timeLeft: '1 Week Left',\n                  icon: FiTarget,\n                  color: '#7C3AED',\n                  bgColor: '#EDE9FE'\n                }, {\n                  title: 'Final Exam - Quantum Mechanics',\n                  description: 'Coverage: Entire Semester',\n                  date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n                  timeLeft: '2 Weeks Left',\n                  icon: FiZap,\n                  color: '#EA580C',\n                  bgColor: '#FED7AA'\n                }].map((exam, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    padding: '1rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    transition: 'all 0.3s ease',\n                    cursor: 'pointer'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = exam.bgColor;\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'transparent';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: exam.bgColor,\n                      color: exam.color,\n                      padding: '0.75rem',\n                      borderRadius: '0.5rem',\n                      marginRight: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(exam.icon, {\n                      size: 20\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'flex-start',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          style: {\n                            fontWeight: 'bold',\n                            margin: 0,\n                            marginBottom: '0.25rem'\n                          },\n                          children: exam.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 735,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            color: '#6B7280',\n                            fontSize: '0.875rem',\n                            margin: 0\n                          },\n                          children: exam.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 736,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: exam.bgColor,\n                          color: exam.color,\n                          fontSize: '0.75rem',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '0.25rem'\n                        },\n                        children: exam.timeLeft\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 738,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        fontSize: '0.875rem',\n                        color: '#6B7280'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                        size: 14,\n                        style: {\n                          marginRight: '0.5rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 749,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: exam.date\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 750,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 748,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowAddExamModal(true),\n                style: {\n                  marginTop: '1.5rem',\n                  width: '100%',\n                  padding: '0.75rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                },\n                onMouseEnter: e => e.target.style.background = '#B91C1C',\n                onMouseLeave: e => e.target.style.background = '#DC2626',\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 19\n                }, this), \" Add Exam Reminder\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: 'bold',\n                color: '#DC2626',\n                marginBottom: '1rem'\n              },\n              children: \"Academic Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center',\n                marginBottom: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative',\n                  width: '160px',\n                  height: '160px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  style: {\n                    width: '100%',\n                    height: '100%'\n                  },\n                  viewBox: \"0 0 100 100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"50\",\n                    r: \"40\",\n                    stroke: \"#E5E7EB\",\n                    strokeWidth: \"8\",\n                    fill: \"transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"50\",\n                    cy: \"50\",\n                    r: \"40\",\n                    stroke: \"#DC2626\",\n                    strokeWidth: \"8\",\n                    fill: \"transparent\",\n                    strokeDasharray: \"251.2\",\n                    strokeDashoffset: \"62.8\",\n                    strokeLinecap: \"round\",\n                    style: {\n                      transform: 'rotate(-90deg)',\n                      transformOrigin: '50% 50%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 797,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    inset: 0,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    flexDirection: 'column'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '2rem',\n                      fontWeight: 'bold',\n                      color: '#DC2626'\n                    },\n                    children: \"75%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#6B7280',\n                      fontSize: '0.875rem'\n                    },\n                    children: \"Overall\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.75rem'\n              },\n              children: [{\n                name: 'Data Structures',\n                progress: '82%',\n                color: '#DC2626'\n              }, {\n                name: 'Linear Algebra',\n                progress: '90%',\n                color: '#7C3AED'\n              }, {\n                name: 'Quantum Mechanics',\n                progress: '65%',\n                color: '#EA580C'\n              }, {\n                name: 'Modern Poetry',\n                progress: '45%',\n                color: '#6B7280'\n              }].map((subject, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '0.75rem',\n                      height: '0.75rem',\n                      borderRadius: '50%',\n                      background: subject.color,\n                      marginRight: '0.5rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: subject.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: subject.progress\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 850,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: '1.25rem',\n                  fontWeight: 'bold',\n                  color: '#DC2626',\n                  margin: 0\n                },\n                children: \"Course Mindmap\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(FiMaximize2, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '300px',\n                background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n                borderRadius: '1rem',\n                position: 'relative',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mindmap-node\",\n                style: {\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  background: '#DC2626',\n                  color: 'white',\n                  borderRadius: '0.5rem',\n                  padding: '0.5rem 1rem',\n                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  border: '2px solid transparent'\n                },\n                children: \"Computer Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 17\n              }, this), [{\n                text: 'Data Structures',\n                top: '30%',\n                left: '20%'\n              }, {\n                text: 'Algorithms',\n                top: '50%',\n                left: '20%'\n              }, {\n                text: 'Databases',\n                top: '70%',\n                left: '20%'\n              }, {\n                text: 'AI/ML',\n                top: '30%',\n                left: '80%'\n              }, {\n                text: 'Networking',\n                top: '50%',\n                left: '80%'\n              }, {\n                text: 'Cybersecurity',\n                top: '70%',\n                left: '80%'\n              }].map((node, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mindmap-node\",\n                  style: {\n                    position: 'absolute',\n                    top: node.top,\n                    left: node.left,\n                    background: 'white',\n                    borderRadius: '0.5rem',\n                    padding: '0.5rem 1rem',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    border: '2px solid transparent',\n                    transform: 'translate(-50%, -50%)'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.transform = 'translate(-50%, -50%) scale(1.05)';\n                    e.target.style.borderColor = '#DC2626';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.transform = 'translate(-50%, -50%) scale(1)';\n                    e.target.style.borderColor = 'transparent';\n                  },\n                  children: node.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    background: '#FCA5A5',\n                    height: '2px',\n                    width: '100px',\n                    top: node.top,\n                    left: node.left === '20%' ? '30%' : '70%',\n                    transformOrigin: 'left center',\n                    transform: node.left === '20%' ? node.top === '30%' ? 'translateY(-50%) rotate(-30deg)' : node.top === '50%' ? 'translateY(-50%)' : 'translateY(-50%) rotate(30deg)' : node.top === '30%' ? 'translateY(-50%) rotate(30deg)' : node.top === '50%' ? 'translateY(-50%)' : 'translateY(-50%) rotate(-30deg)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '1rem',\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 19\n                }, this), \" Add Node\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiSave, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 19\n                }, this), \" Save\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: 'bold',\n                color: '#DC2626',\n                marginBottom: '1rem'\n              },\n              children: \"Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(2, 1fr)',\n                gap: '0.75rem'\n              },\n              children: [{\n                icon: FiBookOpen,\n                label: 'Study Materials',\n                color: '#DC2626',\n                bgColor: '#FEE2E2',\n                action: () => setShowStudyMaterialsModal(true)\n              }, {\n                icon: FiClipboard,\n                label: 'Assignments',\n                color: '#7C3AED',\n                bgColor: '#EDE9FE',\n                action: () => setShowAssignmentsModal(true)\n              }, {\n                icon: FiTrendingUp,\n                label: 'Grades',\n                color: '#EA580C',\n                bgColor: '#FED7AA',\n                action: () => setShowGradesModal(true)\n              }, {\n                icon: FiCalendar,\n                label: 'Schedule',\n                color: '#6B7280',\n                bgColor: '#F3F4F6',\n                action: () => setShowScheduleModal(true)\n              }].map((action, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: action.action,\n                style: {\n                  background: action.bgColor,\n                  color: action.color,\n                  padding: '0.75rem',\n                  borderRadius: '0.5rem',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  transition: 'all 0.3s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                },\n                children: [/*#__PURE__*/_jsxDEV(action.icon, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '0.875rem'\n                  },\n                  children: action.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '2rem',\n        right: '2rem',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'flex-end',\n        gap: '1rem',\n        zIndex: 1000\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setChatbotOpen(!chatbotOpen),\n        style: {\n          width: '3.5rem',\n          height: '3.5rem',\n          borderRadius: '50%',\n          background: '#DC2626',\n          color: 'white',\n          border: 'none',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n          transition: 'all 0.3s ease',\n          animation: 'float 3s ease-in-out infinite'\n        },\n        onMouseEnter: e => e.target.style.background = '#B91C1C',\n        onMouseLeave: e => e.target.style.background = '#DC2626',\n        children: /*#__PURE__*/_jsxDEV(FiCpu, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1076,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1056,\n        columnNumber: 9\n      }, this), chatbotOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '350px',\n          height: '500px',\n          background: 'white',\n          borderRadius: '0.75rem',\n          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#DC2626',\n            color: 'white',\n            padding: '0.75rem 1rem',\n            fontWeight: 'bold',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"EduAI Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setChatbotOpen(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              cursor: 'pointer'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1102,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1092,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            padding: '1rem',\n            overflowY: 'auto',\n            background: '#F8FAFC'\n          },\n          children: chatMessages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem',\n              display: 'flex',\n              justifyContent: message.isUser ? 'flex-end' : 'flex-start'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: message.isUser ? '#DC2626' : '#FEE2E2',\n                color: message.isUser ? 'white' : '#1F2937',\n                padding: '0.75rem',\n                borderRadius: '0.5rem',\n                maxWidth: '75%'\n              },\n              children: message.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1128,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1123,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1116,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            padding: '0.75rem',\n            borderTop: '1px solid #E5E7EB',\n            background: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: chatInput,\n            onChange: e => setChatInput(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Type your question...\",\n            style: {\n              flex: 1,\n              padding: '0.5rem 0.75rem',\n              border: '1px solid #E5E7EB',\n              borderRadius: '1.25rem',\n              outline: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleChatSend,\n            style: {\n              marginLeft: '0.5rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '1.25rem',\n              padding: '0.5rem 1rem',\n              cursor: 'pointer'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiSend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1174,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1142,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1081,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1045,\n      columnNumber: 7\n    }, this), showAddCourseModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '500px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Add New Course\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCourseModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Course Title *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCourse.title,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                title: e.target.value\n              })),\n              placeholder: \"e.g., Data Structures & Algorithms\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Subject *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCourse.subject,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                subject: e.target.value\n              })),\n              placeholder: \"e.g., Computer Science\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1242,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Professor *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCourse.professor,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                professor: e.target.value\n              })),\n              placeholder: \"e.g., Prof. Smith\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newCourse.description,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                description: e.target.value\n              })),\n              placeholder: \"Course description...\",\n              rows: 3,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem',\n                resize: 'vertical'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Duration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1293,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newCourse.duration,\n              onChange: e => setNewCourse(prev => ({\n                ...prev,\n                duration: e.target.value\n              })),\n              placeholder: \"e.g., 12 Weeks\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1294,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Color Theme\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '0.5rem'\n              },\n              children: ['#DC2626', '#7C3AED', '#EA580C', '#6B7280', '#059669', '#0891B2'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setNewCourse(prev => ({\n                  ...prev,\n                  color\n                })),\n                style: {\n                  width: '2rem',\n                  height: '2rem',\n                  borderRadius: '50%',\n                  background: color,\n                  border: newCourse.color === color ? '3px solid #000' : '1px solid #E5E7EB',\n                  cursor: 'pointer'\n                }\n              }, color, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1311,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1222,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginTop: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCourseModal(false),\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: 'white',\n              color: '#6B7280',\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1331,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCourse,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Add Course\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1345,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1330,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1195,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1183,\n      columnNumber: 9\n    }, this), showAddExamModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '500px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Add Exam Reminder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddExamModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1399,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1389,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1385,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Exam Title *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newExam.title,\n              onChange: e => setNewExam(prev => ({\n                ...prev,\n                title: e.target.value\n              })),\n              placeholder: \"e.g., Midterm Exam - Data Structures\",\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Course *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: newExam.courseId,\n              onChange: e => setNewExam(prev => ({\n                ...prev,\n                courseId: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a course\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1434,\n                columnNumber: 19\n              }, this), courses.map(course => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: course.id,\n                children: course.title\n              }, course.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1436,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1421,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Exam Date *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: newExam.date,\n              onChange: e => setNewExam(prev => ({\n                ...prev,\n                date: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1443,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1441,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newExam.description,\n              onChange: e => setNewExam(prev => ({\n                ...prev,\n                description: e.target.value\n              })),\n              placeholder: \"Exam coverage and details...\",\n              rows: 3,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem',\n                resize: 'vertical'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1459,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1457,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1403,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginTop: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddExamModal(false),\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: 'white',\n              color: '#6B7280',\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddExam,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Add Exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1491,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1476,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1378,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1366,\n      columnNumber: 9\n    }, this), showSettingsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '600px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Dashboard Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1534,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSettingsModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1547,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1537,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1533,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#374151'\n              },\n              children: \"Preferences\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Email Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1558,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    background: '#DC2626',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '1rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '0.875rem'\n                  },\n                  children: \"Enabled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1559,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1557,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Dark Mode\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1572,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    background: '#E5E7EB',\n                    color: '#6B7280',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '1rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '0.875rem'\n                  },\n                  children: \"Disabled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1573,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1571,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Auto-save Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1586,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    background: '#DC2626',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '1rem',\n                    border: 'none',\n                    cursor: 'pointer',\n                    fontSize: '0.875rem'\n                  },\n                  children: \"Enabled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1587,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1585,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1556,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1552,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                marginBottom: '1rem',\n                color: '#374151'\n              },\n              children: \"Data Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1603,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  padding: '0.75rem',\n                  background: '#F3F4F6',\n                  color: '#374151',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  textAlign: 'left'\n                },\n                children: \"Export Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  padding: '0.75rem',\n                  background: '#F3F4F6',\n                  color: '#374151',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  textAlign: 'left'\n                },\n                children: \"Import Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1618,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  padding: '0.75rem',\n                  background: '#FEE2E2',\n                  color: '#DC2626',\n                  border: '1px solid #FECACA',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer',\n                  textAlign: 'left'\n                },\n                children: \"Reset All Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1629,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1606,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1602,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1551,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            marginTop: '2rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSettingsModal(false),\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '0.5rem',\n              cursor: 'pointer'\n            },\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1645,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1644,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1524,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1512,\n      columnNumber: 9\n    }, this), showStudyMaterialsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '700px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Study Materials\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1687,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowStudyMaterialsModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1700,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1690,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1686,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowUploadModal(true),\n            style: {\n              background: '#DC2626',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.background = '#B91C1C',\n            onMouseLeave: e => e.target.style.background = '#DC2626',\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1722,\n              columnNumber: 17\n            }, this), \" Upload PDF Material\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1705,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1704,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: courses.map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              padding: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                marginBottom: '0.5rem',\n                color: course.color\n              },\n              children: course.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1733,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.5rem'\n              },\n              children: course.materials && course.materials.length > 0 ? course.materials.map((material, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  padding: '0.75rem',\n                  background: '#F9FAFB',\n                  borderRadius: '0.5rem',\n                  border: '1px solid #E5E7EB'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: '#DC2626',\n                      color: 'white',\n                      padding: '0.5rem',\n                      borderRadius: '0.25rem',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FiFileText, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1757,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1748,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 500,\n                        fontSize: '0.9rem'\n                      },\n                      children: typeof material === 'string' ? material : material.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1760,\n                      columnNumber: 29\n                    }, this), typeof material === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.75rem',\n                        color: '#6B7280'\n                      },\n                      children: [material.size, \" \\u2022 Uploaded on \", material.uploadDate]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1764,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1759,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1747,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => typeof material === 'object' ? handleDownloadFile(material) : alert('Download not available'),\n                    style: {\n                      background: '#059669',\n                      color: 'white',\n                      border: 'none',\n                      padding: '0.5rem',\n                      borderRadius: '0.25rem',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    title: \"Download PDF\",\n                    children: /*#__PURE__*/_jsxDEV(FiDownload, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1786,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1771,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteFile(course.id, idx),\n                    style: {\n                      background: '#EF4444',\n                      color: 'white',\n                      border: 'none',\n                      padding: '0.5rem',\n                      borderRadius: '0.25rem',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    title: \"Delete PDF\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1803,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1788,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1770,\n                  columnNumber: 25\n                }, this)]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1738,\n                columnNumber: 23\n              }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  padding: '2rem',\n                  color: '#6B7280',\n                  background: '#F9FAFB',\n                  borderRadius: '0.5rem',\n                  border: '2px dashed #D1D5DB'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiFileText, {\n                  size: 48,\n                  style: {\n                    margin: '0 auto 1rem',\n                    opacity: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1816,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontStyle: 'italic'\n                  },\n                  children: \"No PDF materials uploaded yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1817,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0.5rem 0 0',\n                    fontSize: '0.875rem'\n                  },\n                  children: \"Click \\\"Upload PDF Material\\\" to add study materials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1818,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1808,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1736,\n              columnNumber: 19\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1728,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1726,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1677,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1665,\n      columnNumber: 9\n    }, this), showAssignmentsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '700px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Assignments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1855,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAssignmentsModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1868,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1858,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1854,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: '#7C3AED',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1884,\n              columnNumber: 17\n            }, this), \" Create Assignment\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1873,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1872,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: courses.map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              padding: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                marginBottom: '0.5rem',\n                color: course.color\n              },\n              children: course.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1895,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.5rem'\n              },\n              children: course.assignments && course.assignments.length > 0 ? course.assignments.map((assignment, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  padding: '0.75rem',\n                  background: '#F9FAFB',\n                  borderRadius: '0.25rem',\n                  border: '1px solid #E5E7EB'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FiClipboard, {\n                    size: 16,\n                    color: \"#7C3AED\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1910,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: assignment\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1911,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1909,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      background: '#7C3AED',\n                      color: 'white',\n                      padding: '0.25rem 0.5rem',\n                      borderRadius: '0.25rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.75rem'\n                    },\n                    children: \"View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1914,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      background: '#059669',\n                      color: 'white',\n                      padding: '0.25rem 0.5rem',\n                      borderRadius: '0.25rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.75rem'\n                    },\n                    children: \"Submit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1925,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1913,\n                  columnNumber: 25\n                }, this)]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1900,\n                columnNumber: 23\n              }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6B7280',\n                  fontStyle: 'italic'\n                },\n                children: \"No assignments yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1939,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1898,\n              columnNumber: 19\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1890,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1888,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1845,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1833,\n      columnNumber: 9\n    }, this), showGradesModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '600px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Grades Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1973,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowGradesModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1986,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1976,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1972,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: courses.map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              padding: '1rem',\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              background: course.bgColor\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: '1.1rem',\n                  fontWeight: 600,\n                  margin: 0,\n                  color: course.color\n                },\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2002,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6B7280',\n                  margin: 0\n                },\n                children: [\"Progress: \", course.progress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2005,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2001,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'right'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '1.5rem',\n                  fontWeight: 'bold',\n                  color: course.color\n                },\n                children: course.grade || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2010,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6B7280'\n                },\n                children: \"Current Grade\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2017,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2009,\n              columnNumber: 19\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1992,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1990,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1.5rem',\n            padding: '1rem',\n            background: '#F9FAFB',\n            borderRadius: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              marginBottom: '0.5rem'\n            },\n            children: \"Overall GPA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2026,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold',\n              color: '#DC2626'\n            },\n            children: \"3.7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2027,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6B7280',\n              margin: 0\n            },\n            children: \"Based on completed courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2028,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2025,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1963,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1951,\n      columnNumber: 9\n    }, this), showScheduleModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '800px',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Academic Schedule\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2060,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowScheduleModal(false),\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2073,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2063,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2059,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              background: '#6B7280',\n              color: 'white',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '0.5rem',\n              border: 'none',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2089,\n              columnNumber: 17\n            }, this), \" Add Event\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2078,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2077,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(7, 1fr)',\n            gap: '1px',\n            background: '#E5E7EB',\n            borderRadius: '0.5rem',\n            overflow: 'hidden'\n          },\n          children: [['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#F9FAFB',\n              padding: '0.75rem',\n              textAlign: 'center',\n              fontWeight: 600,\n              fontSize: '0.875rem'\n            },\n            children: day\n          }, day, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2095,\n            columnNumber: 17\n          }, this)), Array.from({\n            length: 35\n          }, (_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              padding: '0.75rem',\n              minHeight: '60px',\n              fontSize: '0.875rem',\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6B7280'\n              },\n              children: i % 31 + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2114,\n              columnNumber: 19\n            }, this), i === 14 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#DC2626',\n                color: 'white',\n                fontSize: '0.75rem',\n                padding: '0.25rem',\n                borderRadius: '0.25rem',\n                marginTop: '0.25rem'\n              },\n              children: \"Midterm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2116,\n              columnNumber: 21\n            }, this), i === 21 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#7C3AED',\n                color: 'white',\n                fontSize: '0.75rem',\n                padding: '0.25rem',\n                borderRadius: '0.25rem',\n                marginTop: '0.25rem'\n              },\n              children: \"Quiz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2128,\n              columnNumber: 21\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2107,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2093,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              marginBottom: '1rem'\n            },\n            children: \"Upcoming Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '0.5rem'\n            },\n            children: exams.slice(0, 3).map(exam => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                padding: '0.75rem',\n                background: exam.bgColor,\n                borderRadius: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(exam.icon, {\n                  size: 16,\n                  color: exam.color\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2156,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 500\n                  },\n                  children: exam.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2157,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2155,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: exam.color,\n                  fontWeight: 500\n                },\n                children: exam.timeLeft\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2159,\n                columnNumber: 21\n              }, this)]\n            }, exam.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2147,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2145,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2143,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2050,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2038,\n      columnNumber: 9\n    }, this), showUploadModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '1rem',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '500px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0\n            },\n            children: \"Upload PDF Material\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowUploadModal(false);\n              setSelectedFile(null);\n              setUploadCourseId('');\n              setUploadProgress(0);\n            },\n            style: {\n              background: 'none',\n              border: 'none',\n              color: '#6B7280',\n              cursor: 'pointer',\n              fontSize: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2210,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2191,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Select Course *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: uploadCourseId,\n              onChange: e => setUploadCourseId(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #E5E7EB',\n                borderRadius: '0.5rem',\n                fontSize: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Choose a course...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2230,\n                columnNumber: 19\n              }, this), courses.map(course => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: course.id,\n                children: course.title\n              }, course.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2232,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 500\n              },\n              children: \"Select PDF File *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                border: '2px dashed #D1D5DB',\n                borderRadius: '0.5rem',\n                padding: '2rem',\n                textAlign: 'center',\n                background: selectedFile ? '#F0FDF4' : '#F9FAFB',\n                borderColor: selectedFile ? '#10B981' : '#D1D5DB'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".pdf\",\n                onChange: handleFileSelect,\n                style: {\n                  display: 'none'\n                },\n                id: \"pdf-upload\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"pdf-upload\",\n                style: {\n                  cursor: 'pointer',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                  size: 32,\n                  color: selectedFile ? '#10B981' : '#6B7280'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2268,\n                  columnNumber: 21\n                }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: 500,\n                      color: '#10B981'\n                    },\n                    children: selectedFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2271,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.875rem',\n                      color: '#6B7280'\n                    },\n                    children: [(selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2274,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2270,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: 500,\n                      color: '#6B7280'\n                    },\n                    children: \"Click to select PDF file\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2280,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.875rem',\n                      color: '#9CA3AF'\n                    },\n                    children: \"Maximum file size: 10MB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2283,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2279,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2243,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2239,\n            columnNumber: 15\n          }, this), isUploading && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: 500\n                },\n                children: \"Uploading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2295,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6B7280'\n                },\n                children: [uploadProgress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2296,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2294,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '0.5rem',\n                background: '#E5E7EB',\n                borderRadius: '0.25rem',\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: `${uploadProgress}%`,\n                  height: '100%',\n                  background: '#DC2626',\n                  transition: 'width 0.3s ease'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2305,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2298,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2293,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginTop: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowUploadModal(false);\n              setSelectedFile(null);\n              setUploadCourseId('');\n              setUploadProgress(0);\n            },\n            disabled: isUploading,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: 'white',\n              color: '#6B7280',\n              border: '1px solid #E5E7EB',\n              borderRadius: '0.5rem',\n              cursor: isUploading ? 'not-allowed' : 'pointer',\n              opacity: isUploading ? 0.5 : 1\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2317,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleFileUpload,\n            disabled: !selectedFile || !uploadCourseId || isUploading,\n            style: {\n              flex: 1,\n              padding: '0.75rem',\n              background: !selectedFile || !uploadCourseId || isUploading ? '#D1D5DB' : '#DC2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '0.5rem',\n              cursor: !selectedFile || !uploadCourseId || isUploading ? 'not-allowed' : 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              gap: '0.5rem'\n            },\n            children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(FiLoader, {\n                className: \"animate-spin\",\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2357,\n                columnNumber: 21\n              }, this), \"Uploading...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(FiUpload, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2362,\n                columnNumber: 21\n              }, this), \"Upload PDF\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2338,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2316,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2184,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2172,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes float {\n            0% { transform: translateY(0px); }\n            50% { transform: translateY(-10px); }\n            100% { transform: translateY(0px); }\n          }\n\n          .animate-spin {\n            animation: spin 1s linear infinite;\n          }\n\n          @keyframes spin {\n            from { transform: rotate(0deg); }\n            to { transform: rotate(360deg); }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2373,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 394,\n    columnNumber: 5\n  }, this);\n};\n_s(AcademicDashboard, \"lS3rto5HtZMJ0wk/y6oR3Ytm/qg=\");\n_c = AcademicDashboard;\nexport default AcademicDashboard;\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiBook", "FiCheckCircle", "<PERSON><PERSON><PERSON><PERSON>", "FiAlertCircle", "FiPlus", "FiSettings", "FiStar", "FiUser", "FiCalendar", "FiFileText", "FiBookOpen", "FiClipboard", "FiTrendingUp", "FiMaximize2", "FiSave", "FiCpu", "FiX", "FiSend", "<PERSON><PERSON><PERSON><PERSON>", "FiZap", "FiDownload", "FiTrash2", "FiUpload", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AcademicDashboard", "_s", "chatbotOpen", "setChatbotOpen", "chatMessages", "setChatMessages", "text", "isUser", "chatInput", "setChatInput", "showAddCourseModal", "setShowAddCourseModal", "showAddExamModal", "setShowAddExamModal", "showCourseDetailsModal", "setShowCourseDetailsModal", "showSettingsModal", "setShowSettingsModal", "showStudyMaterialsModal", "setShowStudyMaterialsModal", "showAssignmentsModal", "setShowAssignmentsModal", "showGradesModal", "setShowGradesModal", "showScheduleModal", "setShowScheduleModal", "selectedCourse", "setSelectedCourse", "courseFilter", "setCourseFilter", "courses", "setCourses", "id", "subject", "title", "description", "professor", "duration", "rating", "color", "bgColor", "status", "progress", "materials", "assignments", "exams", "setExams", "date", "timeLeft", "icon", "courseId", "newCourse", "setNewCourse", "newExam", "setNewExam", "showUploadModal", "setShowUploadModal", "selectedFile", "setSelectedFile", "uploadCourseId", "setUploadCourseId", "uploadProgress", "setUploadProgress", "isUploading", "setIsUploading", "timer", "setTimeout", "nodes", "document", "querySelectorAll", "for<PERSON>ach", "node", "index", "style", "opacity", "transition", "clearTimeout", "handleChatSend", "trim", "prev", "responses", "randomResponse", "Math", "floor", "random", "length", "handleKeyPress", "e", "key", "handleAddCourse", "course", "Date", "now", "grade", "alert", "handleAddExam", "exam", "calculateTimeLeft", "examDate", "today", "diffTime", "diffDays", "ceil", "handleCourseClick", "handleFilterChange", "filter", "getFilteredCourses", "calculateStats", "total", "completed", "c", "active", "pending", "stats", "handleFileSelect", "event", "file", "target", "files", "type", "size", "handleFileUpload", "uploadInterval", "setInterval", "clearInterval", "fileURL", "URL", "createObjectURL", "map", "toString", "name", "toFixed", "uploadDate", "toLocaleDateString", "url", "error", "console", "handleDownloadFile", "material", "link", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleDeleteFile", "materialIndex", "window", "confirm", "newMaterials", "splice", "minHeight", "background", "padding", "children", "max<PERSON><PERSON><PERSON>", "margin", "display", "justifyContent", "alignItems", "marginBottom", "flexWrap", "gap", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "borderRadius", "border", "cursor", "onMouseEnter", "onMouseLeave", "gridTemplateColumns", "label", "value", "stat", "boxShadow", "currentTarget", "transform", "marginRight", "flexDirection", "overflow", "borderColor", "width", "height", "marginTop", "flex", "position", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeLinecap", "transform<PERSON><PERSON>in", "inset", "className", "top", "left", "action", "bottom", "right", "zIndex", "animation", "overflowY", "message", "borderTop", "onChange", "onKeyPress", "placeholder", "outline", "marginLeft", "maxHeight", "rows", "resize", "textAlign", "idx", "fontStyle", "assignment", "day", "Array", "from", "_", "i", "slice", "accept", "htmlFor", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich/src/AcademicDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>Book, FiCheckCircle, FiLoader, FiAlertCircle, FiPlus, FiSettings, FiStar, FiUser, FiCalendar, FiFileText, FiBookOpen, FiClipboard, FiTrendingUp, FiMaximize2, FiSave, FiCpu, FiX, FiSend, FiTarget, FiZap, FiDownload, FiTrash2, FiUpload } from 'react-icons/fi';\n\nconst AcademicDashboard = () => {\n  // Chatbot states\n  const [chatbotOpen, setChatbotOpen] = useState(false);\n  const [chatMessages, setChatMessages] = useState([\n    { text: \"Hello! I'm your EduAI assistant. How can I help you with your academics today?\", isUser: false }\n  ]);\n  const [chatInput, setChatInput] = useState('');\n\n  // Modal states\n  const [showAddCourseModal, setShowAddCourseModal] = useState(false);\n  const [showAddExamModal, setShowAddExamModal] = useState(false);\n  const [showCourseDetailsModal, setShowCourseDetailsModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [showStudyMaterialsModal, setShowStudyMaterialsModal] = useState(false);\n  const [showAssignmentsModal, setShowAssignmentsModal] = useState(false);\n  const [showGradesModal, setShowGradesModal] = useState(false);\n  const [showScheduleModal, setShowScheduleModal] = useState(false);\n  const [selectedCourse, setSelectedCourse] = useState(null);\n\n  // Filter states\n  const [courseFilter, setCourseFilter] = useState('All');\n\n  // Data states\n  const [courses, setCourses] = useState([\n    {\n      id: 1,\n      subject: 'Computer Science',\n      title: 'Data Structures & Algorithms',\n      description: 'Master fundamental algorithms and problem-solving techniques',\n      professor: 'Prof. Smith',\n      duration: '12 Weeks',\n      rating: 4.8,\n      color: '#DC2626',\n      bgColor: '#FEE2E2',\n      status: 'Active',\n      progress: 82,\n      materials: ['Textbook Chapter 1-5', 'Video Lectures', 'Practice Problems'],\n      assignments: ['Assignment 1: Arrays', 'Assignment 2: Linked Lists']\n    },\n    {\n      id: 2,\n      subject: 'Mathematics',\n      title: 'Linear Algebra',\n      description: 'Vectors, matrices, and their applications in computer science',\n      professor: 'Prof. Johnson',\n      duration: '8 Weeks',\n      rating: 4.5,\n      color: '#7C3AED',\n      bgColor: '#EDE9FE',\n      status: 'Active',\n      progress: 90,\n      materials: ['Linear Algebra Textbook', 'Khan Academy Videos'],\n      assignments: ['Matrix Operations Quiz', 'Eigenvalues Problem Set']\n    },\n    {\n      id: 3,\n      subject: 'Physics',\n      title: 'Quantum Mechanics',\n      description: 'Introduction to quantum theory and its applications',\n      professor: 'Prof. Williams',\n      duration: '10 Weeks',\n      rating: 4.2,\n      color: '#EA580C',\n      bgColor: '#FED7AA',\n      status: 'Active',\n      progress: 65,\n      materials: ['Quantum Physics Textbook', 'Lab Manual'],\n      assignments: ['Wave Function Analysis', 'Quantum States Problem']\n    },\n    {\n      id: 4,\n      subject: 'Literature',\n      title: 'Modern Poetry',\n      description: 'Analysis of 20th century poetry and poetic techniques',\n      professor: 'Prof. Brown',\n      duration: '6 Weeks',\n      rating: 4.7,\n      color: '#6B7280',\n      bgColor: '#F3F4F6',\n      status: 'Completed',\n      progress: 100,\n      materials: ['Poetry Anthology', 'Critical Essays'],\n      assignments: ['Poetry Analysis Essay', 'Creative Writing Assignment']\n    }\n  ]);\n\n  const [exams, setExams] = useState([\n    {\n      id: 1,\n      title: 'Midterm Exam - Data Structures',\n      description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n      date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n      timeLeft: '3 Days Left',\n      icon: FiFileText,\n      color: '#DC2626',\n      bgColor: '#FEE2E2',\n      courseId: 1\n    },\n    {\n      id: 2,\n      title: 'Quiz 2 - Linear Algebra',\n      description: 'Coverage: Matrix Operations, Determinants',\n      date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n      timeLeft: '1 Week Left',\n      icon: FiTarget,\n      color: '#7C3AED',\n      bgColor: '#EDE9FE',\n      courseId: 2\n    },\n    {\n      id: 3,\n      title: 'Final Exam - Quantum Mechanics',\n      description: 'Coverage: Entire Semester',\n      date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n      timeLeft: '2 Weeks Left',\n      icon: FiZap,\n      color: '#EA580C',\n      bgColor: '#FED7AA',\n      courseId: 3\n    }\n  ]);\n\n  // Form states\n  const [newCourse, setNewCourse] = useState({\n    subject: '',\n    title: '',\n    description: '',\n    professor: '',\n    duration: '',\n    color: '#DC2626'\n  });\n\n  const [newExam, setNewExam] = useState({\n    title: '',\n    description: '',\n    date: '',\n    courseId: ''\n  });\n\n  // File upload states\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [uploadCourseId, setUploadCourseId] = useState('');\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Animation effect for mindmap nodes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      const nodes = document.querySelectorAll('.mindmap-node');\n      nodes.forEach((node, index) => {\n        node.style.opacity = '0';\n        setTimeout(() => {\n          node.style.opacity = '1';\n          node.style.transition = 'opacity 0.5s ease, transform 0.3s ease';\n        }, 200 * index);\n      });\n    }, 100);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const handleChatSend = () => {\n    if (chatInput.trim()) {\n      setChatMessages(prev => [...prev, { text: chatInput, isUser: true }]);\n      setChatInput('');\n\n      // Simulate bot response\n      setTimeout(() => {\n        const responses = [\n          \"I can help you with that. What specific aspect do you need assistance with?\",\n          \"That's an interesting question. Let me check my knowledge base...\",\n          \"For that topic, I recommend reviewing chapter 3 of your textbook.\",\n          \"I'm still learning about that subject, but here's what I know...\",\n          \"Would you like me to find study resources for that topic?\"\n        ];\n        const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n        setChatMessages(prev => [...prev, { text: randomResponse, isUser: false }]);\n      }, 1000);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter') {\n      handleChatSend();\n    }\n  };\n\n  // Functionality handlers\n  const handleAddCourse = () => {\n    if (newCourse.title && newCourse.subject && newCourse.professor) {\n      const course = {\n        id: Date.now(),\n        ...newCourse,\n        rating: 0,\n        bgColor: newCourse.color + '20',\n        status: 'Active',\n        progress: 0,\n        materials: [],\n        assignments: [],\n        grade: 'N/A'\n      };\n      setCourses(prev => [...prev, course]);\n      setNewCourse({\n        subject: '',\n        title: '',\n        description: '',\n        professor: '',\n        duration: '',\n        color: '#DC2626'\n      });\n      setShowAddCourseModal(false);\n      alert('Course added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n\n  const handleAddExam = () => {\n    if (newExam.title && newExam.date && newExam.courseId) {\n      const exam = {\n        id: Date.now(),\n        ...newExam,\n        timeLeft: calculateTimeLeft(newExam.date),\n        icon: FiFileText,\n        color: '#DC2626',\n        bgColor: '#FEE2E2'\n      };\n      setExams(prev => [...prev, exam]);\n      setNewExam({\n        title: '',\n        description: '',\n        date: '',\n        courseId: ''\n      });\n      setShowAddExamModal(false);\n      alert('Exam added successfully!');\n    } else {\n      alert('Please fill in all required fields');\n    }\n  };\n\n  const calculateTimeLeft = (examDate) => {\n    const today = new Date();\n    const exam = new Date(examDate);\n    const diffTime = exam - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays < 0) return 'Past Due';\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return '1 Day Left';\n    if (diffDays < 7) return `${diffDays} Days Left`;\n    if (diffDays < 14) return '1 Week Left';\n    return `${Math.ceil(diffDays / 7)} Weeks Left`;\n  };\n\n  const handleCourseClick = (course) => {\n    setSelectedCourse(course);\n    setShowCourseDetailsModal(true);\n  };\n\n  const handleFilterChange = (filter) => {\n    setCourseFilter(filter);\n  };\n\n  const getFilteredCourses = () => {\n    if (courseFilter === 'All') return courses;\n    return courses.filter(course => course.status === courseFilter);\n  };\n\n  const calculateStats = () => {\n    const total = courses.length;\n    const completed = courses.filter(c => c.status === 'Completed').length;\n    const active = courses.filter(c => c.status === 'Active').length;\n    const pending = courses.filter(c => c.progress === 0).length;\n\n    return { total, completed, active, pending };\n  };\n\n  const stats = calculateStats();\n\n  // File upload handlers\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      // Check if file is PDF\n      if (file.type !== 'application/pdf') {\n        alert('Please select a PDF file only');\n        return;\n      }\n\n      // Check file size (max 10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        alert('File size should be less than 10MB');\n        return;\n      }\n\n      setSelectedFile(file);\n    }\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile || !uploadCourseId) {\n      alert('Please select a file and course');\n      return;\n    }\n\n    setIsUploading(true);\n    setUploadProgress(0);\n\n    try {\n      // Simulate file upload progress\n      const uploadInterval = setInterval(() => {\n        setUploadProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(uploadInterval);\n            return 90;\n          }\n          return prev + 10;\n        });\n      }, 200);\n\n      // Create file URL for preview (in real app, this would be uploaded to server)\n      const fileURL = URL.createObjectURL(selectedFile);\n\n      // Add file to course materials\n      setTimeout(() => {\n        setCourses(prev => prev.map(course => {\n          if (course.id.toString() === uploadCourseId) {\n            return {\n              ...course,\n              materials: [...(course.materials || []), {\n                name: selectedFile.name,\n                type: 'pdf',\n                size: (selectedFile.size / 1024 / 1024).toFixed(2) + ' MB',\n                uploadDate: new Date().toLocaleDateString(),\n                url: fileURL\n              }]\n            };\n          }\n          return course;\n        }));\n\n        setUploadProgress(100);\n        setTimeout(() => {\n          setIsUploading(false);\n          setShowUploadModal(false);\n          setSelectedFile(null);\n          setUploadCourseId('');\n          setUploadProgress(0);\n          alert('PDF uploaded successfully!');\n        }, 500);\n      }, 2000);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Upload failed. Please try again.');\n      setIsUploading(false);\n    }\n  };\n\n  const handleDownloadFile = (material) => {\n    if (material.url) {\n      // Create download link\n      const link = document.createElement('a');\n      link.href = material.url;\n      link.download = material.name;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } else {\n      alert('File not available for download');\n    }\n  };\n\n  const handleDeleteFile = (courseId, materialIndex) => {\n    if (window.confirm('Are you sure you want to delete this file?')) {\n      setCourses(prev => prev.map(course => {\n        if (course.id === courseId) {\n          const newMaterials = [...course.materials];\n          newMaterials.splice(materialIndex, 1);\n          return { ...course, materials: newMaterials };\n        }\n        return course;\n      }));\n      alert('File deleted successfully!');\n    }\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n      padding: '2rem 1rem'\n    }}>\n      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n        {/* Header */}\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '2rem',\n          flexWrap: 'wrap',\n          gap: '1rem'\n        }}>\n          <div>\n            <h1 style={{\n              fontSize: '2.5rem',\n              fontWeight: 'bold',\n              color: '#DC2626',\n              margin: 0,\n              marginBottom: '0.5rem'\n            }}>\n              Academic Dashboard\n            </h1>\n            <p style={{ color: '#6B7280', margin: 0 }}>\n              Track your academic progress and resources\n            </p>\n          </div>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={() => setShowAddCourseModal(true)}\n              style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              }}\n              onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n              onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n            >\n              <FiPlus /> Add New Course\n            </button>\n            <button\n              onClick={() => setShowSettingsModal(true)}\n              style={{\n                background: 'white',\n                color: '#DC2626',\n                border: '2px solid #DC2626',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                transition: 'all 0.3s ease'\n              }}\n              onMouseEnter={(e) => e.target.style.background = '#FEE2E2'}\n              onMouseLeave={(e) => e.target.style.background = 'white'}\n            >\n              <FiSettings /> Settings\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Overview */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        }}>\n          {[\n            { icon: FiBook, label: 'Total Courses', value: stats.total.toString(), color: '#DC2626', bgColor: '#FEE2E2' },\n            { icon: FiCheckCircle, label: 'Completed', value: stats.completed.toString(), color: '#7C3AED', bgColor: '#EDE9FE' },\n            { icon: FiLoader, label: 'In Progress', value: stats.active.toString(), color: '#EA580C', bgColor: '#FED7AA' },\n            { icon: FiAlertCircle, label: 'Pending', value: stats.pending.toString(), color: '#6B7280', bgColor: '#F3F4F6' }\n          ].map((stat, index) => (\n            <div key={index} style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem',\n              display: 'flex',\n              alignItems: 'center',\n              transition: 'transform 0.3s ease',\n              cursor: 'pointer'\n            }}\n            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n            >\n              <div style={{\n                background: stat.bgColor,\n                padding: '0.75rem',\n                borderRadius: '50%',\n                marginRight: '1rem'\n              }}>\n                <stat.icon size={24} color={stat.color} />\n              </div>\n              <div>\n                <p style={{ color: '#6B7280', fontSize: '0.875rem', margin: 0 }}>{stat.label}</p>\n                <h3 style={{ fontSize: '2rem', fontWeight: 'bold', color: stat.color, margin: 0 }}>\n                  {stat.value}\n                </h3>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: '2fr 1fr',\n          gap: '2rem',\n          '@media (max-width: 1024px)': {\n            gridTemplateColumns: '1fr'\n          }\n        }}>\n          {/* Left Column */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n            {/* Courses Section */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '1rem'\n              }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', margin: 0 }}>Your Courses</h2>\n                <div style={{ display: 'flex', gap: '0.5rem' }}>\n                  {['All', 'Active', 'Completed'].map((filter, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleFilterChange(filter)}\n                      style={{\n                        background: courseFilter === filter ? 'white' : '#B91C1C',\n                        color: courseFilter === filter ? '#DC2626' : 'white',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '0.375rem',\n                        border: 'none',\n                        fontSize: '0.875rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease'\n                      }}\n                    >\n                      {filter}\n                    </button>\n                  ))}\n                </div>\n              </div>\n              <div style={{ padding: '1.5rem' }}>\n                <div style={{\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                  gap: '1rem'\n                }}>\n                  {getFilteredCourses().map((course, index) => (\n                    <div\n                      key={index}\n                      onClick={() => handleCourseClick(course)}\n                      style={{\n                        border: '1px solid #E5E7EB',\n                        borderRadius: '0.5rem',\n                        padding: '1rem',\n                        transition: 'all 0.3s ease',\n                        cursor: 'pointer'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.borderColor = course.color;\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.borderColor = '#E5E7EB';\n                        e.currentTarget.style.transform = 'translateY(0)';\n                      }}\n                    >\n                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.75rem' }}>\n                        <span style={{\n                          background: course.bgColor,\n                          color: course.color,\n                          fontSize: '0.75rem',\n                          padding: '0.25rem 0.5rem',\n                          borderRadius: '0.25rem'\n                        }}>\n                          {course.subject}\n                        </span>\n                        <span style={{ color: '#F59E0B', fontSize: '0.875rem', display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                          <FiStar size={14} /> {course.rating}\n                        </span>\n                      </div>\n                      <h3 style={{ fontWeight: 'bold', fontSize: '1.125rem', marginBottom: '0.5rem' }}>\n                        {course.title}\n                      </h3>\n                      <p style={{ color: '#6B7280', fontSize: '0.875rem', marginBottom: '1rem' }}>\n                        {course.description}\n                      </p>\n                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <div style={{ display: 'flex', alignItems: 'center' }}>\n                          <div style={{\n                            width: '2rem',\n                            height: '2rem',\n                            borderRadius: '50%',\n                            background: course.bgColor,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            marginRight: '0.5rem'\n                          }}>\n                            <FiUser size={14} color={course.color} />\n                          </div>\n                          <span style={{ fontSize: '0.875rem' }}>{course.professor}</span>\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>{course.duration}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <button\n                  onClick={() => setShowAddCourseModal(true)}\n                  style={{\n                    marginTop: '1.5rem',\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '2px dashed #D1D5DB',\n                    borderRadius: '0.5rem',\n                    background: 'transparent',\n                    color: '#6B7280',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.5rem'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.target.style.borderColor = '#DC2626';\n                    e.target.style.color = '#DC2626';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.target.style.borderColor = '#D1D5DB';\n                    e.target.style.color = '#6B7280';\n                  }}\n                >\n                  <FiPlus /> Add More Courses\n                </button>\n              </div>\n            </div>\n\n            {/* Exams Section */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                background: '#DC2626',\n                color: 'white',\n                padding: '1.5rem'\n              }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', margin: 0 }}>Upcoming Exams</h2>\n              </div>\n              <div style={{ padding: '1.5rem' }}>\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  {[\n                    {\n                      title: 'Midterm Exam - Data Structures',\n                      description: 'Coverage: Arrays, Linked Lists, Stacks, Queues',\n                      date: 'Nov 15, 2023 • 9:00 AM - 11:00 AM',\n                      timeLeft: '3 Days Left',\n                      icon: FiFileText,\n                      color: '#DC2626',\n                      bgColor: '#FEE2E2'\n                    },\n                    {\n                      title: 'Quiz 2 - Linear Algebra',\n                      description: 'Coverage: Matrix Operations, Determinants',\n                      date: 'Nov 22, 2023 • 2:00 PM - 3:00 PM',\n                      timeLeft: '1 Week Left',\n                      icon: FiTarget,\n                      color: '#7C3AED',\n                      bgColor: '#EDE9FE'\n                    },\n                    {\n                      title: 'Final Exam - Quantum Mechanics',\n                      description: 'Coverage: Entire Semester',\n                      date: 'Dec 5, 2023 • 10:00 AM - 12:00 PM',\n                      timeLeft: '2 Weeks Left',\n                      icon: FiZap,\n                      color: '#EA580C',\n                      bgColor: '#FED7AA'\n                    }\n                  ].map((exam, index) => (\n                    <div key={index} style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      padding: '1rem',\n                      border: '1px solid #E5E7EB',\n                      borderRadius: '0.5rem',\n                      transition: 'all 0.3s ease',\n                      cursor: 'pointer'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = exam.bgColor;\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = 'transparent';\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    }}\n                    >\n                      <div style={{\n                        background: exam.bgColor,\n                        color: exam.color,\n                        padding: '0.75rem',\n                        borderRadius: '0.5rem',\n                        marginRight: '1rem'\n                      }}>\n                        <exam.icon size={20} />\n                      </div>\n                      <div style={{ flex: 1 }}>\n                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '0.5rem' }}>\n                          <div>\n                            <h3 style={{ fontWeight: 'bold', margin: 0, marginBottom: '0.25rem' }}>{exam.title}</h3>\n                            <p style={{ color: '#6B7280', fontSize: '0.875rem', margin: 0 }}>{exam.description}</p>\n                          </div>\n                          <span style={{\n                            background: exam.bgColor,\n                            color: exam.color,\n                            fontSize: '0.75rem',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '0.25rem'\n                          }}>\n                            {exam.timeLeft}\n                          </span>\n                        </div>\n                        <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', color: '#6B7280' }}>\n                          <FiCalendar size={14} style={{ marginRight: '0.5rem' }} />\n                          <span>{exam.date}</span>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <button\n                  onClick={() => setShowAddExamModal(true)}\n                  style={{\n                    marginTop: '1.5rem',\n                    width: '100%',\n                    padding: '0.75rem',\n                    background: '#DC2626',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.5rem'\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n                  onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n                >\n                  <FiPlus /> Add Exam Reminder\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n            {/* Progress Overview */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', marginBottom: '1rem' }}>\n                Academic Progress\n              </h2>\n              <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '1.5rem' }}>\n                <div style={{ position: 'relative', width: '160px', height: '160px' }}>\n                  <svg style={{ width: '100%', height: '100%' }} viewBox=\"0 0 100 100\">\n                    <circle\n                      cx=\"50\"\n                      cy=\"50\"\n                      r=\"40\"\n                      stroke=\"#E5E7EB\"\n                      strokeWidth=\"8\"\n                      fill=\"transparent\"\n                    />\n                    <circle\n                      cx=\"50\"\n                      cy=\"50\"\n                      r=\"40\"\n                      stroke=\"#DC2626\"\n                      strokeWidth=\"8\"\n                      fill=\"transparent\"\n                      strokeDasharray=\"251.2\"\n                      strokeDashoffset=\"62.8\"\n                      strokeLinecap=\"round\"\n                      style={{ transform: 'rotate(-90deg)', transformOrigin: '50% 50%' }}\n                    />\n                  </svg>\n                  <div style={{\n                    position: 'absolute',\n                    inset: 0,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    flexDirection: 'column'\n                  }}>\n                    <span style={{ fontSize: '2rem', fontWeight: 'bold', color: '#DC2626' }}>75%</span>\n                    <span style={{ color: '#6B7280', fontSize: '0.875rem' }}>Overall</span>\n                  </div>\n                </div>\n              </div>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n                {[\n                  { name: 'Data Structures', progress: '82%', color: '#DC2626' },\n                  { name: 'Linear Algebra', progress: '90%', color: '#7C3AED' },\n                  { name: 'Quantum Mechanics', progress: '65%', color: '#EA580C' },\n                  { name: 'Modern Poetry', progress: '45%', color: '#6B7280' }\n                ].map((subject, index) => (\n                  <div key={index} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                    <div style={{ display: 'flex', alignItems: 'center' }}>\n                      <div style={{\n                        width: '0.75rem',\n                        height: '0.75rem',\n                        borderRadius: '50%',\n                        background: subject.color,\n                        marginRight: '0.5rem'\n                      }} />\n                      <span>{subject.name}</span>\n                    </div>\n                    <span style={{ fontWeight: 'bold' }}>{subject.progress}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Mind Map */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n                <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                  Course Mindmap\n                </h2>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer'\n                }}>\n                  <FiMaximize2 />\n                </button>\n              </div>\n              <div style={{\n                height: '300px',\n                background: 'linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%)',\n                borderRadius: '1rem',\n                position: 'relative',\n                overflow: 'hidden'\n              }}>\n                {/* Central Node */}\n                <div\n                  className=\"mindmap-node\"\n                  style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    background: '#DC2626',\n                    color: 'white',\n                    borderRadius: '0.5rem',\n                    padding: '0.5rem 1rem',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    border: '2px solid transparent'\n                  }}\n                >\n                  Computer Science\n                </div>\n\n                {/* Child Nodes */}\n                {[\n                  { text: 'Data Structures', top: '30%', left: '20%' },\n                  { text: 'Algorithms', top: '50%', left: '20%' },\n                  { text: 'Databases', top: '70%', left: '20%' },\n                  { text: 'AI/ML', top: '30%', left: '80%' },\n                  { text: 'Networking', top: '50%', left: '80%' },\n                  { text: 'Cybersecurity', top: '70%', left: '80%' }\n                ].map((node, index) => (\n                  <div key={index}>\n                    <div\n                      className=\"mindmap-node\"\n                      style={{\n                        position: 'absolute',\n                        top: node.top,\n                        left: node.left,\n                        background: 'white',\n                        borderRadius: '0.5rem',\n                        padding: '0.5rem 1rem',\n                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease',\n                        border: '2px solid transparent',\n                        transform: 'translate(-50%, -50%)'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.transform = 'translate(-50%, -50%) scale(1.05)';\n                        e.target.style.borderColor = '#DC2626';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.transform = 'translate(-50%, -50%) scale(1)';\n                        e.target.style.borderColor = 'transparent';\n                      }}\n                    >\n                      {node.text}\n                    </div>\n                    {/* Connector lines */}\n                    <div style={{\n                      position: 'absolute',\n                      background: '#FCA5A5',\n                      height: '2px',\n                      width: '100px',\n                      top: node.top,\n                      left: node.left === '20%' ? '30%' : '70%',\n                      transformOrigin: 'left center',\n                      transform: node.left === '20%' ?\n                        (node.top === '30%' ? 'translateY(-50%) rotate(-30deg)' :\n                         node.top === '50%' ? 'translateY(-50%)' :\n                         'translateY(-50%) rotate(30deg)') :\n                        (node.top === '30%' ? 'translateY(-50%) rotate(30deg)' :\n                         node.top === '50%' ? 'translateY(-50%)' :\n                         'translateY(-50%) rotate(-30deg)')\n                    }} />\n                  </div>\n                ))}\n              </div>\n              <div style={{ marginTop: '1rem', display: 'flex', justifyContent: 'space-between' }}>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <FiPlus size={14} /> Add Node\n                </button>\n                <button style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#DC2626',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <FiSave size={14} /> Save\n                </button>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div style={{\n              background: 'white',\n              borderRadius: '1rem',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              padding: '1.5rem'\n            }}>\n              <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#DC2626', marginBottom: '1rem' }}>\n                Quick Actions\n              </h2>\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(2, 1fr)',\n                gap: '0.75rem'\n              }}>\n                {[\n                  { icon: FiBookOpen, label: 'Study Materials', color: '#DC2626', bgColor: '#FEE2E2', action: () => setShowStudyMaterialsModal(true) },\n                  { icon: FiClipboard, label: 'Assignments', color: '#7C3AED', bgColor: '#EDE9FE', action: () => setShowAssignmentsModal(true) },\n                  { icon: FiTrendingUp, label: 'Grades', color: '#EA580C', bgColor: '#FED7AA', action: () => setShowGradesModal(true) },\n                  { icon: FiCalendar, label: 'Schedule', color: '#6B7280', bgColor: '#F3F4F6', action: () => setShowScheduleModal(true) }\n                ].map((action, index) => (\n                  <button\n                    key={index}\n                    onClick={action.action}\n                    style={{\n                      background: action.bgColor,\n                      color: action.color,\n                      padding: '0.75rem',\n                      borderRadius: '0.5rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                      e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                      e.currentTarget.style.boxShadow = 'none';\n                    }}\n                  >\n                    <action.icon size={24} />\n                    <span style={{ fontSize: '0.875rem' }}>{action.label}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Chatbot */}\n      <div style={{\n        position: 'fixed',\n        bottom: '2rem',\n        right: '2rem',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'flex-end',\n        gap: '1rem',\n        zIndex: 1000\n      }}>\n        {/* Chatbot Toggle Button */}\n        <button\n          onClick={() => setChatbotOpen(!chatbotOpen)}\n          style={{\n            width: '3.5rem',\n            height: '3.5rem',\n            borderRadius: '50%',\n            background: '#DC2626',\n            color: 'white',\n            border: 'none',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n            transition: 'all 0.3s ease',\n            animation: 'float 3s ease-in-out infinite'\n          }}\n          onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n          onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n        >\n          <FiCpu size={20} />\n        </button>\n\n        {/* Chatbot Container */}\n        {chatbotOpen && (\n          <div style={{\n            width: '350px',\n            height: '500px',\n            background: 'white',\n            borderRadius: '0.75rem',\n            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n            overflow: 'hidden',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            {/* Chatbot Header */}\n            <div style={{\n              background: '#DC2626',\n              color: 'white',\n              padding: '0.75rem 1rem',\n              fontWeight: 'bold',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            }}>\n              <span>EduAI Assistant</span>\n              <button\n                onClick={() => setChatbotOpen(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: 'white',\n                  cursor: 'pointer'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            {/* Messages */}\n            <div style={{\n              flex: 1,\n              padding: '1rem',\n              overflowY: 'auto',\n              background: '#F8FAFC'\n            }}>\n              {chatMessages.map((message, index) => (\n                <div key={index} style={{\n                  marginBottom: '1rem',\n                  display: 'flex',\n                  justifyContent: message.isUser ? 'flex-end' : 'flex-start'\n                }}>\n                  <div style={{\n                    background: message.isUser ? '#DC2626' : '#FEE2E2',\n                    color: message.isUser ? 'white' : '#1F2937',\n                    padding: '0.75rem',\n                    borderRadius: '0.5rem',\n                    maxWidth: '75%'\n                  }}>\n                    {message.text}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Input */}\n            <div style={{\n              display: 'flex',\n              padding: '0.75rem',\n              borderTop: '1px solid #E5E7EB',\n              background: 'white'\n            }}>\n              <input\n                type=\"text\"\n                value={chatInput}\n                onChange={(e) => setChatInput(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Type your question...\"\n                style={{\n                  flex: 1,\n                  padding: '0.5rem 0.75rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '1.25rem',\n                  outline: 'none'\n                }}\n              />\n              <button\n                onClick={handleChatSend}\n                style={{\n                  marginLeft: '0.5rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '1.25rem',\n                  padding: '0.5rem 1rem',\n                  cursor: 'pointer'\n                }}\n              >\n                <FiSend />\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Add Course Modal */}\n      {showAddCourseModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '500px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Add New Course\n              </h2>\n              <button\n                onClick={() => setShowAddCourseModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Course Title *</label>\n                <input\n                  type=\"text\"\n                  value={newCourse.title}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, title: e.target.value }))}\n                  placeholder=\"e.g., Data Structures & Algorithms\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Subject *</label>\n                <input\n                  type=\"text\"\n                  value={newCourse.subject}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, subject: e.target.value }))}\n                  placeholder=\"e.g., Computer Science\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Professor *</label>\n                <input\n                  type=\"text\"\n                  value={newCourse.professor}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, professor: e.target.value }))}\n                  placeholder=\"e.g., Prof. Smith\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Description</label>\n                <textarea\n                  value={newCourse.description}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, description: e.target.value }))}\n                  placeholder=\"Course description...\"\n                  rows={3}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem',\n                    resize: 'vertical'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Duration</label>\n                <input\n                  type=\"text\"\n                  value={newCourse.duration}\n                  onChange={(e) => setNewCourse(prev => ({ ...prev, duration: e.target.value }))}\n                  placeholder=\"e.g., 12 Weeks\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Color Theme</label>\n                <div style={{ display: 'flex', gap: '0.5rem' }}>\n                  {['#DC2626', '#7C3AED', '#EA580C', '#6B7280', '#059669', '#0891B2'].map(color => (\n                    <button\n                      key={color}\n                      onClick={() => setNewCourse(prev => ({ ...prev, color }))}\n                      style={{\n                        width: '2rem',\n                        height: '2rem',\n                        borderRadius: '50%',\n                        background: color,\n                        border: newCourse.color === color ? '3px solid #000' : '1px solid #E5E7EB',\n                        cursor: 'pointer'\n                      }}\n                    />\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>\n              <button\n                onClick={() => setShowAddCourseModal(false)}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: 'white',\n                  color: '#6B7280',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleAddCourse}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Add Course\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Add Exam Modal */}\n      {showAddExamModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '500px'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Add Exam Reminder\n              </h2>\n              <button\n                onClick={() => setShowAddExamModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Exam Title *</label>\n                <input\n                  type=\"text\"\n                  value={newExam.title}\n                  onChange={(e) => setNewExam(prev => ({ ...prev, title: e.target.value }))}\n                  placeholder=\"e.g., Midterm Exam - Data Structures\"\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Course *</label>\n                <select\n                  value={newExam.courseId}\n                  onChange={(e) => setNewExam(prev => ({ ...prev, courseId: e.target.value }))}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                >\n                  <option value=\"\">Select a course</option>\n                  {courses.map(course => (\n                    <option key={course.id} value={course.id}>{course.title}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Exam Date *</label>\n                <input\n                  type=\"date\"\n                  value={newExam.date}\n                  onChange={(e) => setNewExam(prev => ({ ...prev, date: e.target.value }))}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>Description</label>\n                <textarea\n                  value={newExam.description}\n                  onChange={(e) => setNewExam(prev => ({ ...prev, description: e.target.value }))}\n                  placeholder=\"Exam coverage and details...\"\n                  rows={3}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem',\n                    resize: 'vertical'\n                  }}\n                />\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>\n              <button\n                onClick={() => setShowAddExamModal(false)}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: 'white',\n                  color: '#6B7280',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleAddExam}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Add Exam\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Settings Modal */}\n      {showSettingsModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '600px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Dashboard Settings\n              </h2>\n              <button\n                onClick={() => setShowSettingsModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n              <div>\n                <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '1rem', color: '#374151' }}>\n                  Preferences\n                </h3>\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <span>Email Notifications</span>\n                    <button style={{\n                      background: '#DC2626',\n                      color: 'white',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '1rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem'\n                    }}>\n                      Enabled\n                    </button>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <span>Dark Mode</span>\n                    <button style={{\n                      background: '#E5E7EB',\n                      color: '#6B7280',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '1rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem'\n                    }}>\n                      Disabled\n                    </button>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <span>Auto-save Progress</span>\n                    <button style={{\n                      background: '#DC2626',\n                      color: 'white',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '1rem',\n                      border: 'none',\n                      cursor: 'pointer',\n                      fontSize: '0.875rem'\n                    }}>\n                      Enabled\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '1rem', color: '#374151' }}>\n                  Data Management\n                </h3>\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n                  <button style={{\n                    padding: '0.75rem',\n                    background: '#F3F4F6',\n                    color: '#374151',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    textAlign: 'left'\n                  }}>\n                    Export Data\n                  </button>\n                  <button style={{\n                    padding: '0.75rem',\n                    background: '#F3F4F6',\n                    color: '#374151',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    textAlign: 'left'\n                  }}>\n                    Import Data\n                  </button>\n                  <button style={{\n                    padding: '0.75rem',\n                    background: '#FEE2E2',\n                    color: '#DC2626',\n                    border: '1px solid #FECACA',\n                    borderRadius: '0.5rem',\n                    cursor: 'pointer',\n                    textAlign: 'left'\n                  }}>\n                    Reset All Data\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '2rem' }}>\n              <button\n                onClick={() => setShowSettingsModal(false)}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  background: '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: 'pointer'\n                }}\n              >\n                Close\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Study Materials Modal */}\n      {showStudyMaterialsModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '700px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Study Materials\n              </h2>\n              <button\n                onClick={() => setShowStudyMaterialsModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ marginBottom: '1.5rem' }}>\n              <button\n                onClick={() => setShowUploadModal(true)}\n                style={{\n                  background: '#DC2626',\n                  color: 'white',\n                  padding: '0.75rem 1.5rem',\n                  borderRadius: '0.5rem',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  transition: 'all 0.3s ease'\n                }}\n                onMouseEnter={(e) => e.target.style.background = '#B91C1C'}\n                onMouseLeave={(e) => e.target.style.background = '#DC2626'}\n              >\n                <FiPlus /> Upload PDF Material\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              {courses.map(course => (\n                <div key={course.id} style={{\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  padding: '1rem'\n                }}>\n                  <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '0.5rem', color: course.color }}>\n                    {course.title}\n                  </h3>\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    {course.materials && course.materials.length > 0 ? course.materials.map((material, idx) => (\n                      <div key={idx} style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.75rem',\n                        background: '#F9FAFB',\n                        borderRadius: '0.5rem',\n                        border: '1px solid #E5E7EB'\n                      }}>\n                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', flex: 1 }}>\n                          <div style={{\n                            background: '#DC2626',\n                            color: 'white',\n                            padding: '0.5rem',\n                            borderRadius: '0.25rem',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          }}>\n                            <FiFileText size={16} />\n                          </div>\n                          <div>\n                            <div style={{ fontWeight: 500, fontSize: '0.9rem' }}>\n                              {typeof material === 'string' ? material : material.name}\n                            </div>\n                            {typeof material === 'object' && (\n                              <div style={{ fontSize: '0.75rem', color: '#6B7280' }}>\n                                {material.size} • Uploaded on {material.uploadDate}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => typeof material === 'object' ? handleDownloadFile(material) : alert('Download not available')}\n                            style={{\n                              background: '#059669',\n                              color: 'white',\n                              border: 'none',\n                              padding: '0.5rem',\n                              borderRadius: '0.25rem',\n                              cursor: 'pointer',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}\n                            title=\"Download PDF\"\n                          >\n                            <FiDownload size={14} />\n                          </button>\n                          <button\n                            onClick={() => handleDeleteFile(course.id, idx)}\n                            style={{\n                              background: '#EF4444',\n                              color: 'white',\n                              border: 'none',\n                              padding: '0.5rem',\n                              borderRadius: '0.25rem',\n                              cursor: 'pointer',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}\n                            title=\"Delete PDF\"\n                          >\n                            <FiTrash2 size={14} />\n                          </button>\n                        </div>\n                      </div>\n                    )) : (\n                      <div style={{\n                        textAlign: 'center',\n                        padding: '2rem',\n                        color: '#6B7280',\n                        background: '#F9FAFB',\n                        borderRadius: '0.5rem',\n                        border: '2px dashed #D1D5DB'\n                      }}>\n                        <FiFileText size={48} style={{ margin: '0 auto 1rem', opacity: 0.5 }} />\n                        <p style={{ margin: 0, fontStyle: 'italic' }}>No PDF materials uploaded yet</p>\n                        <p style={{ margin: '0.5rem 0 0', fontSize: '0.875rem' }}>\n                          Click \"Upload PDF Material\" to add study materials\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Assignments Modal */}\n      {showAssignmentsModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '700px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Assignments\n              </h2>\n              <button\n                onClick={() => setShowAssignmentsModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ marginBottom: '1.5rem' }}>\n              <button style={{\n                background: '#7C3AED',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <FiPlus /> Create Assignment\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              {courses.map(course => (\n                <div key={course.id} style={{\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  padding: '1rem'\n                }}>\n                  <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '0.5rem', color: course.color }}>\n                    {course.title}\n                  </h3>\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                    {course.assignments && course.assignments.length > 0 ? course.assignments.map((assignment, idx) => (\n                      <div key={idx} style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.75rem',\n                        background: '#F9FAFB',\n                        borderRadius: '0.25rem',\n                        border: '1px solid #E5E7EB'\n                      }}>\n                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                          <FiClipboard size={16} color=\"#7C3AED\" />\n                          <span>{assignment}</span>\n                        </div>\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button style={{\n                            background: '#7C3AED',\n                            color: 'white',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '0.25rem',\n                            border: 'none',\n                            cursor: 'pointer',\n                            fontSize: '0.75rem'\n                          }}>\n                            View\n                          </button>\n                          <button style={{\n                            background: '#059669',\n                            color: 'white',\n                            padding: '0.25rem 0.5rem',\n                            borderRadius: '0.25rem',\n                            border: 'none',\n                            cursor: 'pointer',\n                            fontSize: '0.75rem'\n                          }}>\n                            Submit\n                          </button>\n                        </div>\n                      </div>\n                    )) : (\n                      <p style={{ color: '#6B7280', fontStyle: 'italic' }}>No assignments yet</p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Grades Modal */}\n      {showGradesModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '600px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Grades Overview\n              </h2>\n              <button\n                onClick={() => setShowGradesModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n              {courses.map(course => (\n                <div key={course.id} style={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  padding: '1rem',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  background: course.bgColor\n                }}>\n                  <div>\n                    <h3 style={{ fontSize: '1.1rem', fontWeight: 600, margin: 0, color: course.color }}>\n                      {course.title}\n                    </h3>\n                    <p style={{ fontSize: '0.875rem', color: '#6B7280', margin: 0 }}>\n                      Progress: {course.progress}%\n                    </p>\n                  </div>\n                  <div style={{ textAlign: 'right' }}>\n                    <div style={{\n                      fontSize: '1.5rem',\n                      fontWeight: 'bold',\n                      color: course.color\n                    }}>\n                      {course.grade || 'N/A'}\n                    </div>\n                    <div style={{ fontSize: '0.75rem', color: '#6B7280' }}>\n                      Current Grade\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div style={{ marginTop: '1.5rem', padding: '1rem', background: '#F9FAFB', borderRadius: '0.5rem' }}>\n              <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '0.5rem' }}>Overall GPA</h3>\n              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#DC2626' }}>3.7</div>\n              <p style={{ fontSize: '0.875rem', color: '#6B7280', margin: 0 }}>\n                Based on completed courses\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Schedule Modal */}\n      {showScheduleModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '800px',\n            maxHeight: '90vh',\n            overflowY: 'auto'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Academic Schedule\n              </h2>\n              <button\n                onClick={() => setShowScheduleModal(false)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ marginBottom: '1.5rem' }}>\n              <button style={{\n                background: '#6B7280',\n                color: 'white',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '0.5rem',\n                border: 'none',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <FiPlus /> Add Event\n              </button>\n            </div>\n\n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '1px', background: '#E5E7EB', borderRadius: '0.5rem', overflow: 'hidden' }}>\n              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (\n                <div key={day} style={{\n                  background: '#F9FAFB',\n                  padding: '0.75rem',\n                  textAlign: 'center',\n                  fontWeight: 600,\n                  fontSize: '0.875rem'\n                }}>\n                  {day}\n                </div>\n              ))}\n\n              {Array.from({ length: 35 }, (_, i) => (\n                <div key={i} style={{\n                  background: 'white',\n                  padding: '0.75rem',\n                  minHeight: '60px',\n                  fontSize: '0.875rem',\n                  position: 'relative'\n                }}>\n                  <div style={{ color: '#6B7280' }}>{((i % 31) + 1)}</div>\n                  {i === 14 && (\n                    <div style={{\n                      background: '#DC2626',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      padding: '0.25rem',\n                      borderRadius: '0.25rem',\n                      marginTop: '0.25rem'\n                    }}>\n                      Midterm\n                    </div>\n                  )}\n                  {i === 21 && (\n                    <div style={{\n                      background: '#7C3AED',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      padding: '0.25rem',\n                      borderRadius: '0.25rem',\n                      marginTop: '0.25rem'\n                    }}>\n                      Quiz\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n\n            <div style={{ marginTop: '1.5rem' }}>\n              <h3 style={{ fontSize: '1.1rem', fontWeight: 600, marginBottom: '1rem' }}>Upcoming Events</h3>\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n                {exams.slice(0, 3).map(exam => (\n                  <div key={exam.id} style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '0.75rem',\n                    background: exam.bgColor,\n                    borderRadius: '0.5rem'\n                  }}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                      <exam.icon size={16} color={exam.color} />\n                      <span style={{ fontWeight: 500 }}>{exam.title}</span>\n                    </div>\n                    <span style={{ fontSize: '0.875rem', color: exam.color, fontWeight: 500 }}>\n                      {exam.timeLeft}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Upload PDF Modal */}\n      {showUploadModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '1rem',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '500px'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>\n              <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#DC2626', margin: 0 }}>\n                Upload PDF Material\n              </h2>\n              <button\n                onClick={() => {\n                  setShowUploadModal(false);\n                  setSelectedFile(null);\n                  setUploadCourseId('');\n                  setUploadProgress(0);\n                }}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  color: '#6B7280',\n                  cursor: 'pointer',\n                  fontSize: '1.5rem'\n                }}\n              >\n                <FiX />\n              </button>\n            </div>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                  Select Course *\n                </label>\n                <select\n                  value={uploadCourseId}\n                  onChange={(e) => setUploadCourseId(e.target.value)}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #E5E7EB',\n                    borderRadius: '0.5rem',\n                    fontSize: '1rem'\n                  }}\n                >\n                  <option value=\"\">Choose a course...</option>\n                  {courses.map(course => (\n                    <option key={course.id} value={course.id}>\n                      {course.title}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>\n                  Select PDF File *\n                </label>\n                <div style={{\n                  border: '2px dashed #D1D5DB',\n                  borderRadius: '0.5rem',\n                  padding: '2rem',\n                  textAlign: 'center',\n                  background: selectedFile ? '#F0FDF4' : '#F9FAFB',\n                  borderColor: selectedFile ? '#10B981' : '#D1D5DB'\n                }}>\n                  <input\n                    type=\"file\"\n                    accept=\".pdf\"\n                    onChange={handleFileSelect}\n                    style={{ display: 'none' }}\n                    id=\"pdf-upload\"\n                  />\n                  <label\n                    htmlFor=\"pdf-upload\"\n                    style={{\n                      cursor: 'pointer',\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      gap: '0.5rem'\n                    }}\n                  >\n                    <FiUpload size={32} color={selectedFile ? '#10B981' : '#6B7280'} />\n                    {selectedFile ? (\n                      <div>\n                        <div style={{ fontWeight: 500, color: '#10B981' }}>\n                          {selectedFile.name}\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>\n                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                        </div>\n                      </div>\n                    ) : (\n                      <div>\n                        <div style={{ fontWeight: 500, color: '#6B7280' }}>\n                          Click to select PDF file\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#9CA3AF' }}>\n                          Maximum file size: 10MB\n                        </div>\n                      </div>\n                    )}\n                  </label>\n                </div>\n              </div>\n\n              {isUploading && (\n                <div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>\n                    <span style={{ fontSize: '0.875rem', fontWeight: 500 }}>Uploading...</span>\n                    <span style={{ fontSize: '0.875rem', color: '#6B7280' }}>{uploadProgress}%</span>\n                  </div>\n                  <div style={{\n                    width: '100%',\n                    height: '0.5rem',\n                    background: '#E5E7EB',\n                    borderRadius: '0.25rem',\n                    overflow: 'hidden'\n                  }}>\n                    <div style={{\n                      width: `${uploadProgress}%`,\n                      height: '100%',\n                      background: '#DC2626',\n                      transition: 'width 0.3s ease'\n                    }} />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>\n              <button\n                onClick={() => {\n                  setShowUploadModal(false);\n                  setSelectedFile(null);\n                  setUploadCourseId('');\n                  setUploadProgress(0);\n                }}\n                disabled={isUploading}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: 'white',\n                  color: '#6B7280',\n                  border: '1px solid #E5E7EB',\n                  borderRadius: '0.5rem',\n                  cursor: isUploading ? 'not-allowed' : 'pointer',\n                  opacity: isUploading ? 0.5 : 1\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleFileUpload}\n                disabled={!selectedFile || !uploadCourseId || isUploading}\n                style={{\n                  flex: 1,\n                  padding: '0.75rem',\n                  background: (!selectedFile || !uploadCourseId || isUploading) ? '#D1D5DB' : '#DC2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '0.5rem',\n                  cursor: (!selectedFile || !uploadCourseId || isUploading) ? 'not-allowed' : 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem'\n                }}\n              >\n                {isUploading ? (\n                  <>\n                    <FiLoader className=\"animate-spin\" size={16} />\n                    Uploading...\n                  </>\n                ) : (\n                  <>\n                    <FiUpload size={16} />\n                    Upload PDF\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* CSS Animation */}\n      <style>\n        {`\n          @keyframes float {\n            0% { transform: translateY(0px); }\n            50% { transform: translateY(-10px); }\n            100% { transform: translateY(0px); }\n          }\n\n          .animate-spin {\n            animation: spin 1s linear infinite;\n          }\n\n          @keyframes spin {\n            from { transform: rotate(0deg); }\n            to { transform: rotate(360deg); }\n          }\n        `}\n      </style>\n    </div>\n  );\n};\n\nexport default AcademicDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7Q,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,CAC/C;IAAEmC,IAAI,EAAE,gFAAgF;IAAEC,MAAM,EAAE;EAAM,CAAC,CAC1G,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+C,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACiD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,CACrC;IACE6D,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,kBAAkB;IAC3BC,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,8DAA8D;IAC3EC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;IAC1EC,WAAW,EAAE,CAAC,sBAAsB,EAAE,4BAA4B;EACpE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,+DAA+D;IAC5EC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,yBAAyB,EAAE,qBAAqB,CAAC;IAC7DC,WAAW,EAAE,CAAC,wBAAwB,EAAE,yBAAyB;EACnE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,qDAAqD;IAClEC,SAAS,EAAE,gBAAgB;IAC3BC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,CAAC,0BAA0B,EAAE,YAAY,CAAC;IACrDC,WAAW,EAAE,CAAC,wBAAwB,EAAE,wBAAwB;EAClE,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,uDAAuD;IACpEC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;IAClDC,WAAW,EAAE,CAAC,uBAAuB,EAAE,6BAA6B;EACtE,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC,CACjC;IACE6D,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,gCAAgC;IACvCC,WAAW,EAAE,gDAAgD;IAC7DY,IAAI,EAAE,mCAAmC;IACzCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAEnE,UAAU;IAChByD,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,2CAA2C;IACxDY,IAAI,EAAE,kCAAkC;IACxCC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE1D,QAAQ;IACdgD,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLE,KAAK,EAAE,gCAAgC;IACvCC,WAAW,EAAE,2BAA2B;IACxCY,IAAI,EAAE,mCAAmC;IACzCC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAEzD,KAAK;IACX+C,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE;EACZ,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC;IACzC8D,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC;IACrC+D,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfY,IAAI,EAAE,EAAE;IACRG,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACK,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwF,cAAc,EAAEC,iBAAiB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0F,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC4F,WAAW,EAAEC,cAAc,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6F,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7B,MAAMC,KAAK,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,eAAe,CAAC;MACxDF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC7BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;QACxBR,UAAU,CAAC,MAAM;UACfK,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;UACxBH,IAAI,CAACE,KAAK,CAACE,UAAU,GAAG,wCAAwC;QAClE,CAAC,EAAE,GAAG,GAAGH,KAAK,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMI,YAAY,CAACX,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrE,SAAS,CAACsE,IAAI,CAAC,CAAC,EAAE;MACpBzE,eAAe,CAAC0E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAEzE,IAAI,EAAEE,SAAS;QAAED,MAAM,EAAE;MAAK,CAAC,CAAC,CAAC;MACrEE,YAAY,CAAC,EAAE,CAAC;;MAEhB;MACAyD,UAAU,CAAC,MAAM;QACf,MAAMc,SAAS,GAAG,CAChB,6EAA6E,EAC7E,mEAAmE,EACnE,mEAAmE,EACnE,kEAAkE,EAClE,2DAA2D,CAC5D;QACD,MAAMC,cAAc,GAAGD,SAAS,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,SAAS,CAACK,MAAM,CAAC,CAAC;QAC9EhF,eAAe,CAAC0E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAEzE,IAAI,EAAE2E,cAAc;UAAE1E,MAAM,EAAE;QAAM,CAAC,CAAC,CAAC;MAC7E,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAM+E,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBX,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAItC,SAAS,CAACjB,KAAK,IAAIiB,SAAS,CAAClB,OAAO,IAAIkB,SAAS,CAACf,SAAS,EAAE;MAC/D,MAAMsD,MAAM,GAAG;QACb1D,EAAE,EAAE2D,IAAI,CAACC,GAAG,CAAC,CAAC;QACd,GAAGzC,SAAS;QACZb,MAAM,EAAE,CAAC;QACTE,OAAO,EAAEW,SAAS,CAACZ,KAAK,GAAG,IAAI;QAC/BE,MAAM,EAAE,QAAQ;QAChBC,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,EAAE;QACfiD,KAAK,EAAE;MACT,CAAC;MACD9D,UAAU,CAACgD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEW,MAAM,CAAC,CAAC;MACrCtC,YAAY,CAAC;QACXnB,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZE,KAAK,EAAE;MACT,CAAC,CAAC;MACF5B,qBAAqB,CAAC,KAAK,CAAC;MAC5BmF,KAAK,CAAC,4BAA4B,CAAC;IACrC,CAAC,MAAM;MACLA,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI1C,OAAO,CAACnB,KAAK,IAAImB,OAAO,CAACN,IAAI,IAAIM,OAAO,CAACH,QAAQ,EAAE;MACrD,MAAM8C,IAAI,GAAG;QACXhE,EAAE,EAAE2D,IAAI,CAACC,GAAG,CAAC,CAAC;QACd,GAAGvC,OAAO;QACVL,QAAQ,EAAEiD,iBAAiB,CAAC5C,OAAO,CAACN,IAAI,CAAC;QACzCE,IAAI,EAAEnE,UAAU;QAChByD,KAAK,EAAE,SAAS;QAChBC,OAAO,EAAE;MACX,CAAC;MACDM,QAAQ,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEiB,IAAI,CAAC,CAAC;MACjC1C,UAAU,CAAC;QACTpB,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfY,IAAI,EAAE,EAAE;QACRG,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFrC,mBAAmB,CAAC,KAAK,CAAC;MAC1BiF,KAAK,CAAC,0BAA0B,CAAC;IACnC,CAAC,MAAM;MACLA,KAAK,CAAC,oCAAoC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAIC,QAAQ,IAAK;IACtC,MAAMC,KAAK,GAAG,IAAIR,IAAI,CAAC,CAAC;IACxB,MAAMK,IAAI,GAAG,IAAIL,IAAI,CAACO,QAAQ,CAAC;IAC/B,MAAME,QAAQ,GAAGJ,IAAI,GAAGG,KAAK;IAC7B,MAAME,QAAQ,GAAGnB,IAAI,CAACoB,IAAI,CAACF,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,UAAU;IACnC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,YAAY;IACvC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,aAAa;IACvC,OAAO,GAAGnB,IAAI,CAACoB,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,aAAa;EAChD,CAAC;EAED,MAAME,iBAAiB,GAAIb,MAAM,IAAK;IACpC/D,iBAAiB,CAAC+D,MAAM,CAAC;IACzB3E,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMyF,kBAAkB,GAAIC,MAAM,IAAK;IACrC5E,eAAe,CAAC4E,MAAM,CAAC;EACzB,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI9E,YAAY,KAAK,KAAK,EAAE,OAAOE,OAAO;IAC1C,OAAOA,OAAO,CAAC2E,MAAM,CAACf,MAAM,IAAIA,MAAM,CAACjD,MAAM,KAAKb,YAAY,CAAC;EACjE,CAAC;EAED,MAAM+E,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAG9E,OAAO,CAACuD,MAAM;IAC5B,MAAMwB,SAAS,GAAG/E,OAAO,CAAC2E,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACrE,MAAM,KAAK,WAAW,CAAC,CAAC4C,MAAM;IACtE,MAAM0B,MAAM,GAAGjF,OAAO,CAAC2E,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACrE,MAAM,KAAK,QAAQ,CAAC,CAAC4C,MAAM;IAChE,MAAM2B,OAAO,GAAGlF,OAAO,CAAC2E,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACpE,QAAQ,KAAK,CAAC,CAAC,CAAC2C,MAAM;IAE5D,OAAO;MAAEuB,KAAK;MAAEC,SAAS;MAAEE,MAAM;MAAEC;IAAQ,CAAC;EAC9C,CAAC;EAED,MAAMC,KAAK,GAAGN,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMO,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR;MACA,IAAIA,IAAI,CAACG,IAAI,KAAK,iBAAiB,EAAE;QACnCzB,KAAK,CAAC,+BAA+B,CAAC;QACtC;MACF;;MAEA;MACA,IAAIsB,IAAI,CAACI,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChC1B,KAAK,CAAC,oCAAoC,CAAC;QAC3C;MACF;MAEApC,eAAe,CAAC0D,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMK,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAChE,YAAY,IAAI,CAACE,cAAc,EAAE;MACpCmC,KAAK,CAAC,iCAAiC,CAAC;MACxC;IACF;IAEA9B,cAAc,CAAC,IAAI,CAAC;IACpBF,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAM4D,cAAc,GAAGC,WAAW,CAAC,MAAM;QACvC7D,iBAAiB,CAACiB,IAAI,IAAI;UACxB,IAAIA,IAAI,IAAI,EAAE,EAAE;YACd6C,aAAa,CAACF,cAAc,CAAC;YAC7B,OAAO,EAAE;UACX;UACA,OAAO3C,IAAI,GAAG,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;;MAEP;MACA,MAAM8C,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACtE,YAAY,CAAC;;MAEjD;MACAS,UAAU,CAAC,MAAM;QACfnC,UAAU,CAACgD,IAAI,IAAIA,IAAI,CAACiD,GAAG,CAACtC,MAAM,IAAI;UACpC,IAAIA,MAAM,CAAC1D,EAAE,CAACiG,QAAQ,CAAC,CAAC,KAAKtE,cAAc,EAAE;YAC3C,OAAO;cACL,GAAG+B,MAAM;cACT/C,SAAS,EAAE,CAAC,IAAI+C,MAAM,CAAC/C,SAAS,IAAI,EAAE,CAAC,EAAE;gBACvCuF,IAAI,EAAEzE,YAAY,CAACyE,IAAI;gBACvBX,IAAI,EAAE,KAAK;gBACXC,IAAI,EAAE,CAAC/D,YAAY,CAAC+D,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEW,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;gBAC1DC,UAAU,EAAE,IAAIzC,IAAI,CAAC,CAAC,CAAC0C,kBAAkB,CAAC,CAAC;gBAC3CC,GAAG,EAAET;cACP,CAAC;YACH,CAAC;UACH;UACA,OAAOnC,MAAM;QACf,CAAC,CAAC,CAAC;QAEH5B,iBAAiB,CAAC,GAAG,CAAC;QACtBI,UAAU,CAAC,MAAM;UACfF,cAAc,CAAC,KAAK,CAAC;UACrBR,kBAAkB,CAAC,KAAK,CAAC;UACzBE,eAAe,CAAC,IAAI,CAAC;UACrBE,iBAAiB,CAAC,EAAE,CAAC;UACrBE,iBAAiB,CAAC,CAAC,CAAC;UACpBgC,KAAK,CAAC,4BAA4B,CAAC;QACrC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCzC,KAAK,CAAC,kCAAkC,CAAC;MACzC9B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMyE,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAIA,QAAQ,CAACJ,GAAG,EAAE;MAChB;MACA,MAAMK,IAAI,GAAGvE,QAAQ,CAACwE,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGH,QAAQ,CAACJ,GAAG;MACxBK,IAAI,CAACG,QAAQ,GAAGJ,QAAQ,CAACR,IAAI;MAC7B9D,QAAQ,CAAC2E,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;MAC/BA,IAAI,CAACM,KAAK,CAAC,CAAC;MACZ7E,QAAQ,CAAC2E,IAAI,CAACG,WAAW,CAACP,IAAI,CAAC;IACjC,CAAC,MAAM;MACL7C,KAAK,CAAC,iCAAiC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMqD,gBAAgB,GAAGA,CAACjG,QAAQ,EAAEkG,aAAa,KAAK;IACpD,IAAIC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChEvH,UAAU,CAACgD,IAAI,IAAIA,IAAI,CAACiD,GAAG,CAACtC,MAAM,IAAI;QACpC,IAAIA,MAAM,CAAC1D,EAAE,KAAKkB,QAAQ,EAAE;UAC1B,MAAMqG,YAAY,GAAG,CAAC,GAAG7D,MAAM,CAAC/C,SAAS,CAAC;UAC1C4G,YAAY,CAACC,MAAM,CAACJ,aAAa,EAAE,CAAC,CAAC;UACrC,OAAO;YAAE,GAAG1D,MAAM;YAAE/C,SAAS,EAAE4G;UAAa,CAAC;QAC/C;QACA,OAAO7D,MAAM;MACf,CAAC,CAAC,CAAC;MACHI,KAAK,CAAC,4BAA4B,CAAC;IACrC;EACF,CAAC;EAED,oBACEjG,OAAA;IAAK4E,KAAK,EAAE;MACVgF,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,gBACA/J,OAAA;MAAK4E,KAAK,EAAE;QAAEoF,QAAQ,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAAF,QAAA,gBAEnD/J,OAAA;QAAK4E,KAAK,EAAE;UACVsF,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE,MAAM;UACpBC,QAAQ,EAAE,MAAM;UAChBC,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,gBACA/J,OAAA;UAAA+J,QAAA,gBACE/J,OAAA;YAAI4E,KAAK,EAAE;cACT4F,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,MAAM;cAClB/H,KAAK,EAAE,SAAS;cAChBuH,MAAM,EAAE,CAAC;cACTI,YAAY,EAAE;YAChB,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YAAG4E,KAAK,EAAE;cAAElC,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAE3C;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBAC3C/J,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMhK,qBAAqB,CAAC,IAAI,CAAE;YAC3C8D,KAAK,EAAE;cACLiF,UAAU,EAAE,SAAS;cACrBnH,KAAK,EAAE,OAAO;cACdoH,OAAO,EAAE,gBAAgB;cACzBiB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBf,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbC,QAAQ,EAAE,MAAM;cAChB1F,UAAU,EAAE;YACd,CAAE;YACFoG,YAAY,EAAGxF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;YAC3DsB,YAAY,EAAGzF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;YAAAE,QAAA,gBAE3D/J,OAAA,CAACpB,MAAM;cAAA8L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM1J,oBAAoB,CAAC,IAAI,CAAE;YAC1CwD,KAAK,EAAE;cACLiF,UAAU,EAAE,OAAO;cACnBnH,KAAK,EAAE,SAAS;cAChBsI,MAAM,EAAE,mBAAmB;cAC3BlB,OAAO,EAAE,gBAAgB;cACzBiB,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE,SAAS;cACjBf,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbC,QAAQ,EAAE,MAAM;cAChB1F,UAAU,EAAE;YACd,CAAE;YACFoG,YAAY,EAAGxF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;YAC3DsB,YAAY,EAAGzF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,OAAQ;YAAAE,QAAA,gBAEzD/J,OAAA,CAACnB,UAAU;cAAA6L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAChB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7K,OAAA;QAAK4E,KAAK,EAAE;UACVsF,OAAO,EAAE,MAAM;UACfkB,mBAAmB,EAAE,sCAAsC;UAC3Db,GAAG,EAAE,QAAQ;UACbF,YAAY,EAAE;QAChB,CAAE;QAAAN,QAAA,EACC,CACC;UAAE3G,IAAI,EAAE5E,MAAM;UAAE6M,KAAK,EAAE,eAAe;UAAEC,KAAK,EAAElE,KAAK,CAACL,KAAK,CAACqB,QAAQ,CAAC,CAAC;UAAE1F,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EAC7G;UAAES,IAAI,EAAE3E,aAAa;UAAE4M,KAAK,EAAE,WAAW;UAAEC,KAAK,EAAElE,KAAK,CAACJ,SAAS,CAACoB,QAAQ,CAAC,CAAC;UAAE1F,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EACpH;UAAES,IAAI,EAAE1E,QAAQ;UAAE2M,KAAK,EAAE,aAAa;UAAEC,KAAK,EAAElE,KAAK,CAACF,MAAM,CAACkB,QAAQ,CAAC,CAAC;UAAE1F,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,EAC9G;UAAES,IAAI,EAAEzE,aAAa;UAAE0M,KAAK,EAAE,SAAS;UAAEC,KAAK,EAAElE,KAAK,CAACD,OAAO,CAACiB,QAAQ,CAAC,CAAC;UAAE1F,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAU,CAAC,CACjH,CAACwF,GAAG,CAAC,CAACoD,IAAI,EAAE5G,KAAK,kBAChB3E,OAAA;UAAiB4E,KAAK,EAAE;YACtBiF,UAAU,EAAE,OAAO;YACnBkB,YAAY,EAAE,MAAM;YACpBS,SAAS,EAAE,mCAAmC;YAC9C1B,OAAO,EAAE,QAAQ;YACjBI,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBtF,UAAU,EAAE,qBAAqB;YACjCmG,MAAM,EAAE;UACV,CAAE;UACFC,YAAY,EAAGxF,CAAC,IAAKA,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC8G,SAAS,GAAG,kBAAmB;UAC1EP,YAAY,EAAGzF,CAAC,IAAKA,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC8G,SAAS,GAAG,eAAgB;UAAA3B,QAAA,gBAErE/J,OAAA;YAAK4E,KAAK,EAAE;cACViF,UAAU,EAAE0B,IAAI,CAAC5I,OAAO;cACxBmH,OAAO,EAAE,SAAS;cAClBiB,YAAY,EAAE,KAAK;cACnBY,WAAW,EAAE;YACf,CAAE;YAAA5B,QAAA,eACA/J,OAAA,CAACuL,IAAI,CAACnI,IAAI;cAACuE,IAAI,EAAE,EAAG;cAACjF,KAAK,EAAE6I,IAAI,CAAC7I;YAAM;cAAAgI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAG4E,KAAK,EAAE;gBAAElC,KAAK,EAAE,SAAS;gBAAE8H,QAAQ,EAAE,UAAU;gBAAEP,MAAM,EAAE;cAAE,CAAE;cAAAF,QAAA,EAAEwB,IAAI,CAACF;YAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF7K,OAAA;cAAI4E,KAAK,EAAE;gBAAE4F,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAE/H,KAAK,EAAE6I,IAAI,CAAC7I,KAAK;gBAAEuH,MAAM,EAAE;cAAE,CAAE;cAAAF,QAAA,EAC/EwB,IAAI,CAACD;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GA1BElG,KAAK;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN7K,OAAA;QAAK4E,KAAK,EAAE;UACVsF,OAAO,EAAE,MAAM;UACfkB,mBAAmB,EAAE,SAAS;UAC9Bb,GAAG,EAAE,MAAM;UACX,4BAA4B,EAAE;YAC5Ba,mBAAmB,EAAE;UACvB;QACF,CAAE;QAAArB,QAAA,gBAEA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBAEtE/J,OAAA;YAAK4E,KAAK,EAAE;cACViF,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9CK,QAAQ,EAAE;YACZ,CAAE;YAAA9B,QAAA,gBACA/J,OAAA;cAAK4E,KAAK,EAAE;gBACViF,UAAU,EAAE,SAAS;gBACrBnH,KAAK,EAAE,OAAO;gBACdoH,OAAO,EAAE,QAAQ;gBACjBI,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBE,QAAQ,EAAE,MAAM;gBAChBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBACA/J,OAAA;gBAAI4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAER,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAAY;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpF7K,OAAA;gBAAK4E,KAAK,EAAE;kBAAEsF,OAAO,EAAE,MAAM;kBAAEK,GAAG,EAAE;gBAAS,CAAE;gBAAAR,QAAA,EAC5C,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC5B,GAAG,CAAC,CAACvB,MAAM,EAAEjC,KAAK,kBAChD3E,OAAA;kBAEE8K,OAAO,EAAEA,CAAA,KAAMnE,kBAAkB,CAACC,MAAM,CAAE;kBAC1ChC,KAAK,EAAE;oBACLiF,UAAU,EAAE9H,YAAY,KAAK6E,MAAM,GAAG,OAAO,GAAG,SAAS;oBACzDlE,KAAK,EAAEX,YAAY,KAAK6E,MAAM,GAAG,SAAS,GAAG,OAAO;oBACpDkD,OAAO,EAAE,aAAa;oBACtBiB,YAAY,EAAE,UAAU;oBACxBC,MAAM,EAAE,MAAM;oBACdR,QAAQ,EAAE,UAAU;oBACpBS,MAAM,EAAE,SAAS;oBACjBnG,UAAU,EAAE;kBACd,CAAE;kBAAAiF,QAAA,EAEDnD;gBAAM,GAbFjC,KAAK;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEkF,OAAO,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAChC/J,OAAA;gBAAK4E,KAAK,EAAE;kBACVsF,OAAO,EAAE,MAAM;kBACfkB,mBAAmB,EAAE,sCAAsC;kBAC3Db,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,EACClD,kBAAkB,CAAC,CAAC,CAACsB,GAAG,CAAC,CAACtC,MAAM,EAAElB,KAAK,kBACtC3E,OAAA;kBAEE8K,OAAO,EAAEA,CAAA,KAAMpE,iBAAiB,CAACb,MAAM,CAAE;kBACzCjB,KAAK,EAAE;oBACLoG,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,QAAQ;oBACtBjB,OAAO,EAAE,MAAM;oBACfhF,UAAU,EAAE,eAAe;oBAC3BmG,MAAM,EAAE;kBACV,CAAE;kBACFC,YAAY,EAAGxF,CAAC,IAAK;oBACnBA,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAACkH,WAAW,GAAGjG,MAAM,CAACnD,KAAK;oBAChDgD,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC8G,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACFP,YAAY,EAAGzF,CAAC,IAAK;oBACnBA,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAACkH,WAAW,GAAG,SAAS;oBAC7CpG,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC8G,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAA3B,QAAA,gBAEF/J,OAAA;oBAAK4E,KAAK,EAAE;sBAAEsF,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEE,YAAY,EAAE;oBAAU,CAAE;oBAAAN,QAAA,gBACxF/J,OAAA;sBAAM4E,KAAK,EAAE;wBACXiF,UAAU,EAAEhE,MAAM,CAAClD,OAAO;wBAC1BD,KAAK,EAAEmD,MAAM,CAACnD,KAAK;wBACnB8H,QAAQ,EAAE,SAAS;wBACnBV,OAAO,EAAE,gBAAgB;wBACzBiB,YAAY,EAAE;sBAChB,CAAE;sBAAAhB,QAAA,EACClE,MAAM,CAACzD;oBAAO;sBAAAsI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACP7K,OAAA;sBAAM4E,KAAK,EAAE;wBAAElC,KAAK,EAAE,SAAS;wBAAE8H,QAAQ,EAAE,UAAU;wBAAEN,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEG,GAAG,EAAE;sBAAU,CAAE;sBAAAR,QAAA,gBAC7G/J,OAAA,CAAClB,MAAM;wBAAC6I,IAAI,EAAE;sBAAG;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,KAAC,EAAChF,MAAM,CAACpD,MAAM;oBAAA;sBAAAiI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7K,OAAA;oBAAI4E,KAAK,EAAE;sBAAE6F,UAAU,EAAE,MAAM;sBAAED,QAAQ,EAAE,UAAU;sBAAEH,YAAY,EAAE;oBAAS,CAAE;oBAAAN,QAAA,EAC7ElE,MAAM,CAACxD;kBAAK;oBAAAqI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACL7K,OAAA;oBAAG4E,KAAK,EAAE;sBAAElC,KAAK,EAAE,SAAS;sBAAE8H,QAAQ,EAAE,UAAU;sBAAEH,YAAY,EAAE;oBAAO,CAAE;oBAAAN,QAAA,EACxElE,MAAM,CAACvD;kBAAW;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACJ7K,OAAA;oBAAK4E,KAAK,EAAE;sBAAEsF,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAAL,QAAA,gBACrF/J,OAAA;sBAAK4E,KAAK,EAAE;wBAAEsF,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAAL,QAAA,gBACpD/J,OAAA;wBAAK4E,KAAK,EAAE;0BACVmH,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdjB,YAAY,EAAE,KAAK;0BACnBlB,UAAU,EAAEhE,MAAM,CAAClD,OAAO;0BAC1BuH,OAAO,EAAE,MAAM;0BACfE,UAAU,EAAE,QAAQ;0BACpBD,cAAc,EAAE,QAAQ;0BACxBwB,WAAW,EAAE;wBACf,CAAE;wBAAA5B,QAAA,eACA/J,OAAA,CAACjB,MAAM;0BAAC4I,IAAI,EAAE,EAAG;0BAACjF,KAAK,EAAEmD,MAAM,CAACnD;wBAAM;0BAAAgI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,eACN7K,OAAA;wBAAM4E,KAAK,EAAE;0BAAE4F,QAAQ,EAAE;wBAAW,CAAE;wBAAAT,QAAA,EAAElE,MAAM,CAACtD;sBAAS;wBAAAmI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC,eACN7K,OAAA;sBAAK4E,KAAK,EAAE;wBAAE4F,QAAQ,EAAE,UAAU;wBAAE9H,KAAK,EAAE;sBAAU,CAAE;sBAAAqH,QAAA,EAAElE,MAAM,CAACrD;oBAAQ;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA,GAvDDlG,KAAK;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwDP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7K,OAAA;gBACE8K,OAAO,EAAEA,CAAA,KAAMhK,qBAAqB,CAAC,IAAI,CAAE;gBAC3C8D,KAAK,EAAE;kBACLqH,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE,MAAM;kBACbjC,OAAO,EAAE,SAAS;kBAClBkB,MAAM,EAAE,oBAAoB;kBAC5BD,YAAY,EAAE,QAAQ;kBACtBlB,UAAU,EAAE,aAAa;kBACzBnH,KAAK,EAAE,SAAS;kBAChBuI,MAAM,EAAE,SAAS;kBACjBnG,UAAU,EAAE,eAAe;kBAC3BoF,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBI,GAAG,EAAE;gBACP,CAAE;gBACFW,YAAY,EAAGxF,CAAC,IAAK;kBACnBA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACkH,WAAW,GAAG,SAAS;kBACtCpG,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAAClC,KAAK,GAAG,SAAS;gBAClC,CAAE;gBACFyI,YAAY,EAAGzF,CAAC,IAAK;kBACnBA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACkH,WAAW,GAAG,SAAS;kBACtCpG,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAAClC,KAAK,GAAG,SAAS;gBAClC,CAAE;gBAAAqH,QAAA,gBAEF/J,OAAA,CAACpB,MAAM;kBAAA8L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7K,OAAA;YAAK4E,KAAK,EAAE;cACViF,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9CK,QAAQ,EAAE;YACZ,CAAE;YAAA9B,QAAA,gBACA/J,OAAA;cAAK4E,KAAK,EAAE;gBACViF,UAAU,EAAE,SAAS;gBACrBnH,KAAK,EAAE,OAAO;gBACdoH,OAAO,EAAE;cACX,CAAE;cAAAC,QAAA,eACA/J,OAAA;gBAAI4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAER,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAAc;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACN7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEkF,OAAO,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAChC/J,OAAA;gBAAK4E,KAAK,EAAE;kBAAEsF,OAAO,EAAE,MAAM;kBAAE0B,aAAa,EAAE,QAAQ;kBAAErB,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EACnE,CACC;kBACE1H,KAAK,EAAE,gCAAgC;kBACvCC,WAAW,EAAE,gDAAgD;kBAC7DY,IAAI,EAAE,mCAAmC;kBACzCC,QAAQ,EAAE,aAAa;kBACvBC,IAAI,EAAEnE,UAAU;kBAChByD,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,EACD;kBACEN,KAAK,EAAE,yBAAyB;kBAChCC,WAAW,EAAE,2CAA2C;kBACxDY,IAAI,EAAE,kCAAkC;kBACxCC,QAAQ,EAAE,aAAa;kBACvBC,IAAI,EAAE1D,QAAQ;kBACdgD,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,EACD;kBACEN,KAAK,EAAE,gCAAgC;kBACvCC,WAAW,EAAE,2BAA2B;kBACxCY,IAAI,EAAE,mCAAmC;kBACzCC,QAAQ,EAAE,cAAc;kBACxBC,IAAI,EAAEzD,KAAK;kBACX+C,KAAK,EAAE,SAAS;kBAChBC,OAAO,EAAE;gBACX,CAAC,CACF,CAACwF,GAAG,CAAC,CAAChC,IAAI,EAAExB,KAAK,kBAChB3E,OAAA;kBAAiB4E,KAAK,EAAE;oBACtBsF,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,YAAY;oBACxBN,OAAO,EAAE,MAAM;oBACfkB,MAAM,EAAE,mBAAmB;oBAC3BD,YAAY,EAAE,QAAQ;oBACtBjG,UAAU,EAAE,eAAe;oBAC3BmG,MAAM,EAAE;kBACV,CAAE;kBACFC,YAAY,EAAGxF,CAAC,IAAK;oBACnBA,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAACiF,UAAU,GAAG1D,IAAI,CAACxD,OAAO;oBAC/C+C,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC8G,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACFP,YAAY,EAAGzF,CAAC,IAAK;oBACnBA,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAACiF,UAAU,GAAG,aAAa;oBAChDnE,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC8G,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAA3B,QAAA,gBAEA/J,OAAA;oBAAK4E,KAAK,EAAE;sBACViF,UAAU,EAAE1D,IAAI,CAACxD,OAAO;sBACxBD,KAAK,EAAEyD,IAAI,CAACzD,KAAK;sBACjBoH,OAAO,EAAE,SAAS;sBAClBiB,YAAY,EAAE,QAAQ;sBACtBY,WAAW,EAAE;oBACf,CAAE;oBAAA5B,QAAA,eACA/J,OAAA,CAACmG,IAAI,CAAC/C,IAAI;sBAACuE,IAAI,EAAE;oBAAG;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACN7K,OAAA;oBAAK4E,KAAK,EAAE;sBAAEsH,IAAI,EAAE;oBAAE,CAAE;oBAAAnC,QAAA,gBACtB/J,OAAA;sBAAK4E,KAAK,EAAE;wBAAEsF,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEC,UAAU,EAAE,YAAY;wBAAEC,YAAY,EAAE;sBAAS,CAAE;sBAAAN,QAAA,gBACjH/J,OAAA;wBAAA+J,QAAA,gBACE/J,OAAA;0BAAI4E,KAAK,EAAE;4BAAE6F,UAAU,EAAE,MAAM;4BAAER,MAAM,EAAE,CAAC;4BAAEI,YAAY,EAAE;0BAAU,CAAE;0BAAAN,QAAA,EAAE5D,IAAI,CAAC9D;wBAAK;0BAAAqI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxF7K,OAAA;0BAAG4E,KAAK,EAAE;4BAAElC,KAAK,EAAE,SAAS;4BAAE8H,QAAQ,EAAE,UAAU;4BAAEP,MAAM,EAAE;0BAAE,CAAE;0BAAAF,QAAA,EAAE5D,IAAI,CAAC7D;wBAAW;0BAAAoI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpF,CAAC,eACN7K,OAAA;wBAAM4E,KAAK,EAAE;0BACXiF,UAAU,EAAE1D,IAAI,CAACxD,OAAO;0BACxBD,KAAK,EAAEyD,IAAI,CAACzD,KAAK;0BACjB8H,QAAQ,EAAE,SAAS;0BACnBV,OAAO,EAAE,gBAAgB;0BACzBiB,YAAY,EAAE;wBAChB,CAAE;wBAAAhB,QAAA,EACC5D,IAAI,CAAChD;sBAAQ;wBAAAuH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN7K,OAAA;sBAAK4E,KAAK,EAAE;wBAAEsF,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEI,QAAQ,EAAE,UAAU;wBAAE9H,KAAK,EAAE;sBAAU,CAAE;sBAAAqH,QAAA,gBAC5F/J,OAAA,CAAChB,UAAU;wBAAC2I,IAAI,EAAE,EAAG;wBAAC/C,KAAK,EAAE;0BAAE+G,WAAW,EAAE;wBAAS;sBAAE;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC1D7K,OAAA;wBAAA+J,QAAA,EAAO5D,IAAI,CAACjD;sBAAI;wBAAAwH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA/CElG,KAAK;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgDV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN7K,OAAA;gBACE8K,OAAO,EAAEA,CAAA,KAAM9J,mBAAmB,CAAC,IAAI,CAAE;gBACzC4D,KAAK,EAAE;kBACLqH,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE,MAAM;kBACbjC,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBnH,KAAK,EAAE,OAAO;kBACdsI,MAAM,EAAE,MAAM;kBACdD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjBnG,UAAU,EAAE,eAAe;kBAC3BoF,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBI,GAAG,EAAE;gBACP,CAAE;gBACFW,YAAY,EAAGxF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;gBAC3DsB,YAAY,EAAGzF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;gBAAAE,QAAA,gBAE3D/J,OAAA,CAACpB,MAAM;kBAAA8L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBAEtE/J,OAAA;YAAK4E,KAAK,EAAE;cACViF,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9C1B,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA/J,OAAA;cAAI4E,KAAK,EAAE;gBAAE4F,QAAQ,EAAE,SAAS;gBAAEC,UAAU,EAAE,MAAM;gBAAE/H,KAAK,EAAE,SAAS;gBAAE2H,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,EAAC;YAEhG;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAEE,YAAY,EAAE;cAAS,CAAE;cAAAN,QAAA,eAChF/J,OAAA;gBAAK4E,KAAK,EAAE;kBAAEuH,QAAQ,EAAE,UAAU;kBAAEJ,KAAK,EAAE,OAAO;kBAAEC,MAAM,EAAE;gBAAQ,CAAE;gBAAAjC,QAAA,gBACpE/J,OAAA;kBAAK4E,KAAK,EAAE;oBAAEmH,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE;kBAAO,CAAE;kBAACI,OAAO,EAAC,aAAa;kBAAArC,QAAA,gBAClE/J,OAAA;oBACEqM,EAAE,EAAC,IAAI;oBACPC,EAAE,EAAC,IAAI;oBACPC,CAAC,EAAC,IAAI;oBACNC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,IAAI,EAAC;kBAAa;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACF7K,OAAA;oBACEqM,EAAE,EAAC,IAAI;oBACPC,EAAE,EAAC,IAAI;oBACPC,CAAC,EAAC,IAAI;oBACNC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAC,GAAG;oBACfC,IAAI,EAAC,aAAa;oBAClBC,eAAe,EAAC,OAAO;oBACvBC,gBAAgB,EAAC,MAAM;oBACvBC,aAAa,EAAC,OAAO;oBACrBjI,KAAK,EAAE;sBAAE8G,SAAS,EAAE,gBAAgB;sBAAEoB,eAAe,EAAE;oBAAU;kBAAE;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN7K,OAAA;kBAAK4E,KAAK,EAAE;oBACVuH,QAAQ,EAAE,UAAU;oBACpBY,KAAK,EAAE,CAAC;oBACR7C,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxByB,aAAa,EAAE;kBACjB,CAAE;kBAAA7B,QAAA,gBACA/J,OAAA;oBAAM4E,KAAK,EAAE;sBAAE4F,QAAQ,EAAE,MAAM;sBAAEC,UAAU,EAAE,MAAM;sBAAE/H,KAAK,EAAE;oBAAU,CAAE;oBAAAqH,QAAA,EAAC;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnF7K,OAAA;oBAAM4E,KAAK,EAAE;sBAAElC,KAAK,EAAE,SAAS;sBAAE8H,QAAQ,EAAE;oBAAW,CAAE;oBAAAT,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAE0B,aAAa,EAAE,QAAQ;gBAAErB,GAAG,EAAE;cAAU,CAAE;cAAAR,QAAA,EACtE,CACC;gBAAE1B,IAAI,EAAE,iBAAiB;gBAAExF,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAC9D;gBAAE2F,IAAI,EAAE,gBAAgB;gBAAExF,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAC7D;gBAAE2F,IAAI,EAAE,mBAAmB;gBAAExF,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,EAChE;gBAAE2F,IAAI,EAAE,eAAe;gBAAExF,QAAQ,EAAE,KAAK;gBAAEH,KAAK,EAAE;cAAU,CAAC,CAC7D,CAACyF,GAAG,CAAC,CAAC/F,OAAO,EAAEuC,KAAK,kBACnB3E,OAAA;gBAAiB4E,KAAK,EAAE;kBAAEsF,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAED,cAAc,EAAE;gBAAgB,CAAE;gBAAAJ,QAAA,gBACjG/J,OAAA;kBAAK4E,KAAK,EAAE;oBAAEsF,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAL,QAAA,gBACpD/J,OAAA;oBAAK4E,KAAK,EAAE;sBACVmH,KAAK,EAAE,SAAS;sBAChBC,MAAM,EAAE,SAAS;sBACjBjB,YAAY,EAAE,KAAK;sBACnBlB,UAAU,EAAEzH,OAAO,CAACM,KAAK;sBACzBiJ,WAAW,EAAE;oBACf;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACL7K,OAAA;oBAAA+J,QAAA,EAAO3H,OAAO,CAACiG;kBAAI;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN7K,OAAA;kBAAM4E,KAAK,EAAE;oBAAE6F,UAAU,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAE3H,OAAO,CAACS;gBAAQ;kBAAA6H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAXtDlG,KAAK;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7K,OAAA;YAAK4E,KAAK,EAAE;cACViF,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9C1B,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA/J,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,gBAC3G/J,OAAA;gBAAI4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,SAAS;kBAAEC,UAAU,EAAE,MAAM;kBAAE/H,KAAK,EAAE,SAAS;kBAAEuH,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EAAC;cAErF;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7K,OAAA;gBAAQ4E,KAAK,EAAE;kBACbiF,UAAU,EAAE,MAAM;kBAClBmB,MAAM,EAAE,MAAM;kBACdtI,KAAK,EAAE,SAAS;kBAChBuI,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eACA/J,OAAA,CAACX,WAAW;kBAAAqL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7K,OAAA;cAAK4E,KAAK,EAAE;gBACVoH,MAAM,EAAE,OAAO;gBACfnC,UAAU,EAAE,mDAAmD;gBAC/DkB,YAAY,EAAE,MAAM;gBACpBoB,QAAQ,EAAE,UAAU;gBACpBN,QAAQ,EAAE;cACZ,CAAE;cAAA9B,QAAA,gBAEA/J,OAAA;gBACEgN,SAAS,EAAC,cAAc;gBACxBpI,KAAK,EAAE;kBACLuH,QAAQ,EAAE,UAAU;kBACpBc,GAAG,EAAE,KAAK;kBACVC,IAAI,EAAE,KAAK;kBACXxB,SAAS,EAAE,uBAAuB;kBAClC7B,UAAU,EAAE,SAAS;kBACrBnH,KAAK,EAAE,OAAO;kBACdqI,YAAY,EAAE,QAAQ;kBACtBjB,OAAO,EAAE,aAAa;kBACtB0B,SAAS,EAAE,mCAAmC;kBAC9Cf,UAAU,EAAE,KAAK;kBACjBQ,MAAM,EAAE,SAAS;kBACjBnG,UAAU,EAAE,eAAe;kBAC3BkG,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,EACH;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAGL,CACC;gBAAEpK,IAAI,EAAE,iBAAiB;gBAAEwM,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EACpD;gBAAEzM,IAAI,EAAE,YAAY;gBAAEwM,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC/C;gBAAEzM,IAAI,EAAE,WAAW;gBAAEwM,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC9C;gBAAEzM,IAAI,EAAE,OAAO;gBAAEwM,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC1C;gBAAEzM,IAAI,EAAE,YAAY;gBAAEwM,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,EAC/C;gBAAEzM,IAAI,EAAE,eAAe;gBAAEwM,GAAG,EAAE,KAAK;gBAAEC,IAAI,EAAE;cAAM,CAAC,CACnD,CAAC/E,GAAG,CAAC,CAACzD,IAAI,EAAEC,KAAK,kBAChB3E,OAAA;gBAAA+J,QAAA,gBACE/J,OAAA;kBACEgN,SAAS,EAAC,cAAc;kBACxBpI,KAAK,EAAE;oBACLuH,QAAQ,EAAE,UAAU;oBACpBc,GAAG,EAAEvI,IAAI,CAACuI,GAAG;oBACbC,IAAI,EAAExI,IAAI,CAACwI,IAAI;oBACfrD,UAAU,EAAE,OAAO;oBACnBkB,YAAY,EAAE,QAAQ;oBACtBjB,OAAO,EAAE,aAAa;oBACtB0B,SAAS,EAAE,mCAAmC;oBAC9Cf,UAAU,EAAE,KAAK;oBACjBQ,MAAM,EAAE,SAAS;oBACjBnG,UAAU,EAAE,eAAe;oBAC3BkG,MAAM,EAAE,uBAAuB;oBAC/BU,SAAS,EAAE;kBACb,CAAE;kBACFR,YAAY,EAAGxF,CAAC,IAAK;oBACnBA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAAC8G,SAAS,GAAG,mCAAmC;oBAC9DhG,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACkH,WAAW,GAAG,SAAS;kBACxC,CAAE;kBACFX,YAAY,EAAGzF,CAAC,IAAK;oBACnBA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAAC8G,SAAS,GAAG,gCAAgC;oBAC3DhG,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACkH,WAAW,GAAG,aAAa;kBAC5C,CAAE;kBAAA/B,QAAA,EAEDrF,IAAI,CAACjE;gBAAI;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eAEN7K,OAAA;kBAAK4E,KAAK,EAAE;oBACVuH,QAAQ,EAAE,UAAU;oBACpBtC,UAAU,EAAE,SAAS;oBACrBmC,MAAM,EAAE,KAAK;oBACbD,KAAK,EAAE,OAAO;oBACdkB,GAAG,EAAEvI,IAAI,CAACuI,GAAG;oBACbC,IAAI,EAAExI,IAAI,CAACwI,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;oBACzCJ,eAAe,EAAE,aAAa;oBAC9BpB,SAAS,EAAEhH,IAAI,CAACwI,IAAI,KAAK,KAAK,GAC3BxI,IAAI,CAACuI,GAAG,KAAK,KAAK,GAAG,iCAAiC,GACtDvI,IAAI,CAACuI,GAAG,KAAK,KAAK,GAAG,kBAAkB,GACvC,gCAAgC,GAChCvI,IAAI,CAACuI,GAAG,KAAK,KAAK,GAAG,gCAAgC,GACrDvI,IAAI,CAACuI,GAAG,KAAK,KAAK,GAAG,kBAAkB,GACvC;kBACL;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GA5CGlG,KAAK;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6CV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEqH,SAAS,EAAE,MAAM;gBAAE/B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAgB,CAAE;cAAAJ,QAAA,gBAClF/J,OAAA;gBAAQ4E,KAAK,EAAE;kBACbiF,UAAU,EAAE,MAAM;kBAClBmB,MAAM,EAAE,MAAM;kBACdtI,KAAK,EAAE,SAAS;kBAChBuI,MAAM,EAAE,SAAS;kBACjBf,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBACA/J,OAAA,CAACpB,MAAM;kBAAC+I,IAAI,EAAE;gBAAG;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aACtB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7K,OAAA;gBAAQ4E,KAAK,EAAE;kBACbiF,UAAU,EAAE,MAAM;kBAClBmB,MAAM,EAAE,MAAM;kBACdtI,KAAK,EAAE,SAAS;kBAChBuI,MAAM,EAAE,SAAS;kBACjBf,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBACA/J,OAAA,CAACV,MAAM;kBAACqI,IAAI,EAAE;gBAAG;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SACtB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7K,OAAA;YAAK4E,KAAK,EAAE;cACViF,UAAU,EAAE,OAAO;cACnBkB,YAAY,EAAE,MAAM;cACpBS,SAAS,EAAE,mCAAmC;cAC9C1B,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA/J,OAAA;cAAI4E,KAAK,EAAE;gBAAE4F,QAAQ,EAAE,SAAS;gBAAEC,UAAU,EAAE,MAAM;gBAAE/H,KAAK,EAAE,SAAS;gBAAE2H,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,EAAC;YAEhG;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7K,OAAA;cAAK4E,KAAK,EAAE;gBACVsF,OAAO,EAAE,MAAM;gBACfkB,mBAAmB,EAAE,gBAAgB;gBACrCb,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,EACC,CACC;gBAAE3G,IAAI,EAAElE,UAAU;gBAAEmM,KAAK,EAAE,iBAAiB;gBAAE3I,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE,SAAS;gBAAEwK,MAAM,EAAEA,CAAA,KAAM7L,0BAA0B,CAAC,IAAI;cAAE,CAAC,EACpI;gBAAE8B,IAAI,EAAEjE,WAAW;gBAAEkM,KAAK,EAAE,aAAa;gBAAE3I,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE,SAAS;gBAAEwK,MAAM,EAAEA,CAAA,KAAM3L,uBAAuB,CAAC,IAAI;cAAE,CAAC,EAC9H;gBAAE4B,IAAI,EAAEhE,YAAY;gBAAEiM,KAAK,EAAE,QAAQ;gBAAE3I,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE,SAAS;gBAAEwK,MAAM,EAAEA,CAAA,KAAMzL,kBAAkB,CAAC,IAAI;cAAE,CAAC,EACrH;gBAAE0B,IAAI,EAAEpE,UAAU;gBAAEqM,KAAK,EAAE,UAAU;gBAAE3I,KAAK,EAAE,SAAS;gBAAEC,OAAO,EAAE,SAAS;gBAAEwK,MAAM,EAAEA,CAAA,KAAMvL,oBAAoB,CAAC,IAAI;cAAE,CAAC,CACxH,CAACuG,GAAG,CAAC,CAACgF,MAAM,EAAExI,KAAK,kBAClB3E,OAAA;gBAEE8K,OAAO,EAAEqC,MAAM,CAACA,MAAO;gBACvBvI,KAAK,EAAE;kBACLiF,UAAU,EAAEsD,MAAM,CAACxK,OAAO;kBAC1BD,KAAK,EAAEyK,MAAM,CAACzK,KAAK;kBACnBoH,OAAO,EAAE,SAAS;kBAClBiB,YAAY,EAAE,QAAQ;kBACtBC,MAAM,EAAE,MAAM;kBACdC,MAAM,EAAE,SAAS;kBACjBf,OAAO,EAAE,MAAM;kBACf0B,aAAa,EAAE,QAAQ;kBACvBxB,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE,QAAQ;kBACbzF,UAAU,EAAE;gBACd,CAAE;gBACFoG,YAAY,EAAGxF,CAAC,IAAK;kBACnBA,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC8G,SAAS,GAAG,kBAAkB;kBACpDhG,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC4G,SAAS,GAAG,mCAAmC;gBACvE,CAAE;gBACFL,YAAY,EAAGzF,CAAC,IAAK;kBACnBA,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC8G,SAAS,GAAG,eAAe;kBACjDhG,CAAC,CAAC+F,aAAa,CAAC7G,KAAK,CAAC4G,SAAS,GAAG,MAAM;gBAC1C,CAAE;gBAAAzB,QAAA,gBAEF/J,OAAA,CAACmN,MAAM,CAAC/J,IAAI;kBAACuE,IAAI,EAAE;gBAAG;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzB7K,OAAA;kBAAM4E,KAAK,EAAE;oBAAE4F,QAAQ,EAAE;kBAAW,CAAE;kBAAAT,QAAA,EAAEoD,MAAM,CAAC9B;gBAAK;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAzBvDlG,KAAK;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7K,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBiB,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbnD,OAAO,EAAE,MAAM;QACf0B,aAAa,EAAE,QAAQ;QACvBxB,UAAU,EAAE,UAAU;QACtBG,GAAG,EAAE,MAAM;QACX+C,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,gBAEA/J,OAAA;QACE8K,OAAO,EAAEA,CAAA,KAAMxK,cAAc,CAAC,CAACD,WAAW,CAAE;QAC5CuE,KAAK,EAAE;UACLmH,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE,QAAQ;UAChBjB,YAAY,EAAE,KAAK;UACnBlB,UAAU,EAAE,SAAS;UACrBnH,KAAK,EAAE,OAAO;UACdsI,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,SAAS;UACjBf,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBqB,SAAS,EAAE,mCAAmC;UAC9C1G,UAAU,EAAE,eAAe;UAC3ByI,SAAS,EAAE;QACb,CAAE;QACFrC,YAAY,EAAGxF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;QAC3DsB,YAAY,EAAGzF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;QAAAE,QAAA,eAE3D/J,OAAA,CAACT,KAAK;UAACoI,IAAI,EAAE;QAAG;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,EAGRxK,WAAW,iBACVL,OAAA;QAAK4E,KAAK,EAAE;UACVmH,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,OAAO;UACfnC,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,SAAS;UACvBS,SAAS,EAAE,gCAAgC;UAC3CK,QAAQ,EAAE,QAAQ;UAClB3B,OAAO,EAAE,MAAM;UACf0B,aAAa,EAAE;QACjB,CAAE;QAAA7B,QAAA,gBAEA/J,OAAA;UAAK4E,KAAK,EAAE;YACViF,UAAU,EAAE,SAAS;YACrBnH,KAAK,EAAE,OAAO;YACdoH,OAAO,EAAE,cAAc;YACvBW,UAAU,EAAE,MAAM;YAClBP,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE;UACd,CAAE;UAAAL,QAAA,gBACA/J,OAAA;YAAA+J,QAAA,EAAM;UAAe;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5B7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMxK,cAAc,CAAC,KAAK,CAAE;YACrCsE,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,OAAO;cACduI,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN7K,OAAA;UAAK4E,KAAK,EAAE;YACVsH,IAAI,EAAE,CAAC;YACPpC,OAAO,EAAE,MAAM;YACf0D,SAAS,EAAE,MAAM;YACjB3D,UAAU,EAAE;UACd,CAAE;UAAAE,QAAA,EACCxJ,YAAY,CAAC4H,GAAG,CAAC,CAACsF,OAAO,EAAE9I,KAAK,kBAC/B3E,OAAA;YAAiB4E,KAAK,EAAE;cACtByF,YAAY,EAAE,MAAM;cACpBH,OAAO,EAAE,MAAM;cACfC,cAAc,EAAEsD,OAAO,CAAC/M,MAAM,GAAG,UAAU,GAAG;YAChD,CAAE;YAAAqJ,QAAA,eACA/J,OAAA;cAAK4E,KAAK,EAAE;gBACViF,UAAU,EAAE4D,OAAO,CAAC/M,MAAM,GAAG,SAAS,GAAG,SAAS;gBAClDgC,KAAK,EAAE+K,OAAO,CAAC/M,MAAM,GAAG,OAAO,GAAG,SAAS;gBAC3CoJ,OAAO,EAAE,SAAS;gBAClBiB,YAAY,EAAE,QAAQ;gBACtBf,QAAQ,EAAE;cACZ,CAAE;cAAAD,QAAA,EACC0D,OAAO,CAAChN;YAAI;cAAAiK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAbElG,KAAK;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN7K,OAAA;UAAK4E,KAAK,EAAE;YACVsF,OAAO,EAAE,MAAM;YACfJ,OAAO,EAAE,SAAS;YAClB4D,SAAS,EAAE,mBAAmB;YAC9B7D,UAAU,EAAE;UACd,CAAE;UAAAE,QAAA,gBACA/J,OAAA;YACE0H,IAAI,EAAC,MAAM;YACX4D,KAAK,EAAE3K,SAAU;YACjBgN,QAAQ,EAAGjI,CAAC,IAAK9E,YAAY,CAAC8E,CAAC,CAAC8B,MAAM,CAAC8D,KAAK,CAAE;YAC9CsC,UAAU,EAAEnI,cAAe;YAC3BoI,WAAW,EAAC,uBAAuB;YACnCjJ,KAAK,EAAE;cACLsH,IAAI,EAAE,CAAC;cACPpC,OAAO,EAAE,gBAAgB;cACzBkB,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,SAAS;cACvB+C,OAAO,EAAE;YACX;UAAE;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF7K,OAAA;YACE8K,OAAO,EAAE9F,cAAe;YACxBJ,KAAK,EAAE;cACLmJ,UAAU,EAAE,QAAQ;cACpBlE,UAAU,EAAE,SAAS;cACrBnH,KAAK,EAAE,OAAO;cACdsI,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,SAAS;cACvBjB,OAAO,EAAE,aAAa;cACtBmB,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEF/J,OAAA,CAACP,MAAM;cAAAiL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLhK,kBAAkB,iBACjBb,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTvD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,eACA/J,OAAA;QAAK4E,KAAK,EAAE;UACViF,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,MAAM;UACpBjB,OAAO,EAAE,MAAM;UACfiC,KAAK,EAAE,KAAK;UACZ/B,QAAQ,EAAE,OAAO;UACjBgE,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAzD,QAAA,gBACA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMhK,qBAAqB,CAAC,KAAK,CAAE;YAC5C8D,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,SAAS;cAChBuI,MAAM,EAAE,SAAS;cACjBT,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACpE/J,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAc;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnG7K,OAAA;cACE0H,IAAI,EAAC,MAAM;cACX4D,KAAK,EAAEhI,SAAS,CAACjB,KAAM;cACvBsL,QAAQ,EAAGjI,CAAC,IAAKnC,YAAY,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7C,KAAK,EAAEqD,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cAC5EuC,WAAW,EAAC,oCAAoC;cAChDjJ,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE;cACZ;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9F7K,OAAA;cACE0H,IAAI,EAAC,MAAM;cACX4D,KAAK,EAAEhI,SAAS,CAAClB,OAAQ;cACzBuL,QAAQ,EAAGjI,CAAC,IAAKnC,YAAY,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE9C,OAAO,EAAEsD,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cAC9EuC,WAAW,EAAC,wBAAwB;cACpCjJ,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE;cACZ;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG7K,OAAA;cACE0H,IAAI,EAAC,MAAM;cACX4D,KAAK,EAAEhI,SAAS,CAACf,SAAU;cAC3BoL,QAAQ,EAAGjI,CAAC,IAAKnC,YAAY,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE3C,SAAS,EAAEmD,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cAChFuC,WAAW,EAAC,mBAAmB;cAC/BjJ,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE;cACZ;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG7K,OAAA;cACEsL,KAAK,EAAEhI,SAAS,CAAChB,WAAY;cAC7BqL,QAAQ,EAAGjI,CAAC,IAAKnC,YAAY,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5C,WAAW,EAAEoD,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cAClFuC,WAAW,EAAC,uBAAuB;cACnCI,IAAI,EAAE,CAAE;cACRrJ,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE,MAAM;gBAChB0D,MAAM,EAAE;cACV;YAAE;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7F7K,OAAA;cACE0H,IAAI,EAAC,MAAM;cACX4D,KAAK,EAAEhI,SAAS,CAACd,QAAS;cAC1BmL,QAAQ,EAAGjI,CAAC,IAAKnC,YAAY,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1C,QAAQ,EAAEkD,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cAC/EuC,WAAW,EAAC,gBAAgB;cAC5BjJ,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE;cACZ;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAEK,GAAG,EAAE;cAAS,CAAE;cAAAR,QAAA,EAC5C,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC5B,GAAG,CAACzF,KAAK,iBAC3E1C,OAAA;gBAEE8K,OAAO,EAAEA,CAAA,KAAMvH,YAAY,CAAC2B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAExC;gBAAM,CAAC,CAAC,CAAE;gBAC1DkC,KAAK,EAAE;kBACLmH,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdjB,YAAY,EAAE,KAAK;kBACnBlB,UAAU,EAAEnH,KAAK;kBACjBsI,MAAM,EAAE1H,SAAS,CAACZ,KAAK,KAAKA,KAAK,GAAG,gBAAgB,GAAG,mBAAmB;kBAC1EuI,MAAM,EAAE;gBACV;cAAE,GATGvI,KAAK;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE,MAAM;YAAE0B,SAAS,EAAE;UAAO,CAAE;UAAAlC,QAAA,gBAC9D/J,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMhK,qBAAqB,CAAC,KAAK,CAAE;YAC5C8D,KAAK,EAAE;cACLsH,IAAI,EAAE,CAAC;cACPpC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,OAAO;cACnBnH,KAAK,EAAE,SAAS;cAChBsI,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7K,OAAA;YACE8K,OAAO,EAAElF,eAAgB;YACzBhB,KAAK,EAAE;cACLsH,IAAI,EAAE,CAAC;cACPpC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,SAAS;cACrBnH,KAAK,EAAE,OAAO;cACdsI,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA9J,gBAAgB,iBACff,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTvD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,eACA/J,OAAA;QAAK4E,KAAK,EAAE;UACViF,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,MAAM;UACpBjB,OAAO,EAAE,MAAM;UACfiC,KAAK,EAAE,KAAK;UACZ/B,QAAQ,EAAE;QACZ,CAAE;QAAAD,QAAA,gBACA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM9J,mBAAmB,CAAC,KAAK,CAAE;YAC1C4D,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,SAAS;cAChBuI,MAAM,EAAE,SAAS;cACjBT,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACpE/J,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjG7K,OAAA;cACE0H,IAAI,EAAC,MAAM;cACX4D,KAAK,EAAE9H,OAAO,CAACnB,KAAM;cACrBsL,QAAQ,EAAGjI,CAAC,IAAKjC,UAAU,CAACyB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7C,KAAK,EAAEqD,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cAC1EuC,WAAW,EAAC,sCAAsC;cAClDjJ,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE;cACZ;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7F7K,OAAA;cACEsL,KAAK,EAAE9H,OAAO,CAACH,QAAS;cACxBsK,QAAQ,EAAGjI,CAAC,IAAKjC,UAAU,CAACyB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7B,QAAQ,EAAEqC,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cAC7E1G,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE;cACZ,CAAE;cAAAT,QAAA,gBAEF/J,OAAA;gBAAQsL,KAAK,EAAC,EAAE;gBAAAvB,QAAA,EAAC;cAAe;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxC5I,OAAO,CAACkG,GAAG,CAACtC,MAAM,iBACjB7F,OAAA;gBAAwBsL,KAAK,EAAEzF,MAAM,CAAC1D,EAAG;gBAAA4H,QAAA,EAAElE,MAAM,CAACxD;cAAK,GAA1CwD,MAAM,CAAC1D,EAAE;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA0C,CACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG7K,OAAA;cACE0H,IAAI,EAAC,MAAM;cACX4D,KAAK,EAAE9H,OAAO,CAACN,IAAK;cACpByK,QAAQ,EAAGjI,CAAC,IAAKjC,UAAU,CAACyB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEhC,IAAI,EAAEwC,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cACzE1G,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE;cACZ;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAAW;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChG7K,OAAA;cACEsL,KAAK,EAAE9H,OAAO,CAAClB,WAAY;cAC3BqL,QAAQ,EAAGjI,CAAC,IAAKjC,UAAU,CAACyB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5C,WAAW,EAAEoD,CAAC,CAAC8B,MAAM,CAAC8D;cAAM,CAAC,CAAC,CAAE;cAChFuC,WAAW,EAAC,8BAA8B;cAC1CI,IAAI,EAAE,CAAE;cACRrJ,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE,MAAM;gBAChB0D,MAAM,EAAE;cACV;YAAE;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE,MAAM;YAAE0B,SAAS,EAAE;UAAO,CAAE;UAAAlC,QAAA,gBAC9D/J,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM9J,mBAAmB,CAAC,KAAK,CAAE;YAC1C4D,KAAK,EAAE;cACLsH,IAAI,EAAE,CAAC;cACPpC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,OAAO;cACnBnH,KAAK,EAAE,SAAS;cAChBsI,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7K,OAAA;YACE8K,OAAO,EAAE5E,aAAc;YACvBtB,KAAK,EAAE;cACLsH,IAAI,EAAE,CAAC;cACPpC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,SAAS;cACrBnH,KAAK,EAAE,OAAO;cACdsI,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA1J,iBAAiB,iBAChBnB,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTvD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,eACA/J,OAAA;QAAK4E,KAAK,EAAE;UACViF,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,MAAM;UACpBjB,OAAO,EAAE,MAAM;UACfiC,KAAK,EAAE,KAAK;UACZ/B,QAAQ,EAAE,OAAO;UACjBgE,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAzD,QAAA,gBACA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM1J,oBAAoB,CAAC,KAAK,CAAE;YAC3CwD,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,SAAS;cAChBuI,MAAM,EAAE,SAAS;cACjBT,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACpE/J,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAI4E,KAAK,EAAE;gBAAE4F,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,MAAM;gBAAE3H,KAAK,EAAE;cAAU,CAAE;cAAAqH,QAAA,EAAC;YAE5F;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAE0B,aAAa,EAAE,QAAQ;gBAAErB,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,gBACpE/J,OAAA;gBAAK4E,KAAK,EAAE;kBAAEsF,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACrF/J,OAAA;kBAAA+J,QAAA,EAAM;gBAAmB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChC7K,OAAA;kBAAQ4E,KAAK,EAAE;oBACbiF,UAAU,EAAE,SAAS;oBACrBnH,KAAK,EAAE,OAAO;oBACdoH,OAAO,EAAE,iBAAiB;oBAC1BiB,YAAY,EAAE,MAAM;oBACpBC,MAAM,EAAE,MAAM;oBACdC,MAAM,EAAE,SAAS;oBACjBT,QAAQ,EAAE;kBACZ,CAAE;kBAAAT,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN7K,OAAA;gBAAK4E,KAAK,EAAE;kBAAEsF,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACrF/J,OAAA;kBAAA+J,QAAA,EAAM;gBAAS;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtB7K,OAAA;kBAAQ4E,KAAK,EAAE;oBACbiF,UAAU,EAAE,SAAS;oBACrBnH,KAAK,EAAE,SAAS;oBAChBoH,OAAO,EAAE,iBAAiB;oBAC1BiB,YAAY,EAAE,MAAM;oBACpBC,MAAM,EAAE,MAAM;oBACdC,MAAM,EAAE,SAAS;oBACjBT,QAAQ,EAAE;kBACZ,CAAE;kBAAAT,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN7K,OAAA;gBAAK4E,KAAK,EAAE;kBAAEsF,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACrF/J,OAAA;kBAAA+J,QAAA,EAAM;gBAAkB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/B7K,OAAA;kBAAQ4E,KAAK,EAAE;oBACbiF,UAAU,EAAE,SAAS;oBACrBnH,KAAK,EAAE,OAAO;oBACdoH,OAAO,EAAE,iBAAiB;oBAC1BiB,YAAY,EAAE,MAAM;oBACpBC,MAAM,EAAE,MAAM;oBACdC,MAAM,EAAE,SAAS;oBACjBT,QAAQ,EAAE;kBACZ,CAAE;kBAAAT,QAAA,EAAC;gBAEH;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAI4E,KAAK,EAAE;gBAAE4F,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,MAAM;gBAAE3H,KAAK,EAAE;cAAU,CAAE;cAAAqH,QAAA,EAAC;YAE5F;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAE0B,aAAa,EAAE,QAAQ;gBAAErB,GAAG,EAAE;cAAU,CAAE;cAAAR,QAAA,gBACvE/J,OAAA;gBAAQ4E,KAAK,EAAE;kBACbkF,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBnH,KAAK,EAAE,SAAS;kBAChBsI,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjBkD,SAAS,EAAE;gBACb,CAAE;gBAAApE,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7K,OAAA;gBAAQ4E,KAAK,EAAE;kBACbkF,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBnH,KAAK,EAAE,SAAS;kBAChBsI,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjBkD,SAAS,EAAE;gBACb,CAAE;gBAAApE,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7K,OAAA;gBAAQ4E,KAAK,EAAE;kBACbkF,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBnH,KAAK,EAAE,SAAS;kBAChBsI,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,QAAQ;kBACtBE,MAAM,EAAE,SAAS;kBACjBkD,SAAS,EAAE;gBACb,CAAE;gBAAApE,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,UAAU;YAAE8B,SAAS,EAAE;UAAO,CAAE;UAAAlC,QAAA,eAC7E/J,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM1J,oBAAoB,CAAC,KAAK,CAAE;YAC3CwD,KAAK,EAAE;cACLkF,OAAO,EAAE,gBAAgB;cACzBD,UAAU,EAAE,SAAS;cACrBnH,KAAK,EAAE,OAAO;cACdsI,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAxJ,uBAAuB,iBACtBrB,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTvD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,eACA/J,OAAA;QAAK4E,KAAK,EAAE;UACViF,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,MAAM;UACpBjB,OAAO,EAAE,MAAM;UACfiC,KAAK,EAAE,KAAK;UACZ/B,QAAQ,EAAE,OAAO;UACjBgE,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAzD,QAAA,gBACA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMxJ,0BAA0B,CAAC,KAAK,CAAE;YACjDsD,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,SAAS;cAChBuI,MAAM,EAAE,SAAS;cACjBT,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEyF,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,eACrC/J,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMnH,kBAAkB,CAAC,IAAI,CAAE;YACxCiB,KAAK,EAAE;cACLiF,UAAU,EAAE,SAAS;cACrBnH,KAAK,EAAE,OAAO;cACdoH,OAAO,EAAE,gBAAgB;cACzBiB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBf,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE,QAAQ;cACbzF,UAAU,EAAE;YACd,CAAE;YACFoG,YAAY,EAAGxF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;YAC3DsB,YAAY,EAAGzF,CAAC,IAAKA,CAAC,CAAC8B,MAAM,CAAC5C,KAAK,CAACiF,UAAU,GAAG,SAAU;YAAAE,QAAA,gBAE3D/J,OAAA,CAACpB,MAAM;cAAA8L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,EACnE9H,OAAO,CAACkG,GAAG,CAACtC,MAAM,iBACjB7F,OAAA;YAAqB4E,KAAK,EAAE;cAC1BoG,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBjB,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA/J,OAAA;cAAI4E,KAAK,EAAE;gBAAE4F,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,QAAQ;gBAAE3H,KAAK,EAAEmD,MAAM,CAACnD;cAAM,CAAE;cAAAqH,QAAA,EAC7FlE,MAAM,CAACxD;YAAK;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACL7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAE0B,aAAa,EAAE,QAAQ;gBAAErB,GAAG,EAAE;cAAS,CAAE;cAAAR,QAAA,EACrElE,MAAM,CAAC/C,SAAS,IAAI+C,MAAM,CAAC/C,SAAS,CAAC0C,MAAM,GAAG,CAAC,GAAGK,MAAM,CAAC/C,SAAS,CAACqF,GAAG,CAAC,CAACU,QAAQ,EAAEuF,GAAG,kBACpFpO,OAAA;gBAAe4E,KAAK,EAAE;kBACpBsF,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBN,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBkB,YAAY,EAAE,QAAQ;kBACtBC,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,gBACA/J,OAAA;kBAAK4E,KAAK,EAAE;oBAAEsF,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEG,GAAG,EAAE,SAAS;oBAAE2B,IAAI,EAAE;kBAAE,CAAE;kBAAAnC,QAAA,gBAC7E/J,OAAA;oBAAK4E,KAAK,EAAE;sBACViF,UAAU,EAAE,SAAS;sBACrBnH,KAAK,EAAE,OAAO;sBACdoH,OAAO,EAAE,QAAQ;sBACjBiB,YAAY,EAAE,SAAS;sBACvBb,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE;oBAClB,CAAE;oBAAAJ,QAAA,eACA/J,OAAA,CAACf,UAAU;sBAAC0I,IAAI,EAAE;oBAAG;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACN7K,OAAA;oBAAA+J,QAAA,gBACE/J,OAAA;sBAAK4E,KAAK,EAAE;wBAAE6F,UAAU,EAAE,GAAG;wBAAED,QAAQ,EAAE;sBAAS,CAAE;sBAAAT,QAAA,EACjD,OAAOlB,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAGA,QAAQ,CAACR;oBAAI;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,EACL,OAAOhC,QAAQ,KAAK,QAAQ,iBAC3B7I,OAAA;sBAAK4E,KAAK,EAAE;wBAAE4F,QAAQ,EAAE,SAAS;wBAAE9H,KAAK,EAAE;sBAAU,CAAE;sBAAAqH,QAAA,GACnDlB,QAAQ,CAAClB,IAAI,EAAC,sBAAe,EAACkB,QAAQ,CAACN,UAAU;oBAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7K,OAAA;kBAAK4E,KAAK,EAAE;oBAAEsF,OAAO,EAAE,MAAM;oBAAEK,GAAG,EAAE;kBAAS,CAAE;kBAAAR,QAAA,gBAC7C/J,OAAA;oBACE8K,OAAO,EAAEA,CAAA,KAAM,OAAOjC,QAAQ,KAAK,QAAQ,GAAGD,kBAAkB,CAACC,QAAQ,CAAC,GAAG5C,KAAK,CAAC,wBAAwB,CAAE;oBAC7GrB,KAAK,EAAE;sBACLiF,UAAU,EAAE,SAAS;sBACrBnH,KAAK,EAAE,OAAO;sBACdsI,MAAM,EAAE,MAAM;sBACdlB,OAAO,EAAE,QAAQ;sBACjBiB,YAAY,EAAE,SAAS;sBACvBE,MAAM,EAAE,SAAS;sBACjBf,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE;oBAClB,CAAE;oBACF9H,KAAK,EAAC,cAAc;oBAAA0H,QAAA,eAEpB/J,OAAA,CAACJ,UAAU;sBAAC+H,IAAI,EAAE;oBAAG;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACT7K,OAAA;oBACE8K,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAACzD,MAAM,CAAC1D,EAAE,EAAEiM,GAAG,CAAE;oBAChDxJ,KAAK,EAAE;sBACLiF,UAAU,EAAE,SAAS;sBACrBnH,KAAK,EAAE,OAAO;sBACdsI,MAAM,EAAE,MAAM;sBACdlB,OAAO,EAAE,QAAQ;sBACjBiB,YAAY,EAAE,SAAS;sBACvBE,MAAM,EAAE,SAAS;sBACjBf,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE;oBAClB,CAAE;oBACF9H,KAAK,EAAC,YAAY;oBAAA0H,QAAA,eAElB/J,OAAA,CAACH,QAAQ;sBAAC8H,IAAI,EAAE;oBAAG;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAnEEuD,GAAG;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoER,CACN,CAAC,gBACA7K,OAAA;gBAAK4E,KAAK,EAAE;kBACVuJ,SAAS,EAAE,QAAQ;kBACnBrE,OAAO,EAAE,MAAM;kBACfpH,KAAK,EAAE,SAAS;kBAChBmH,UAAU,EAAE,SAAS;kBACrBkB,YAAY,EAAE,QAAQ;kBACtBC,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,gBACA/J,OAAA,CAACf,UAAU;kBAAC0I,IAAI,EAAE,EAAG;kBAAC/C,KAAK,EAAE;oBAAEqF,MAAM,EAAE,aAAa;oBAAEpF,OAAO,EAAE;kBAAI;gBAAE;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxE7K,OAAA;kBAAG4E,KAAK,EAAE;oBAAEqF,MAAM,EAAE,CAAC;oBAAEoE,SAAS,EAAE;kBAAS,CAAE;kBAAAtE,QAAA,EAAC;gBAA6B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/E7K,OAAA;kBAAG4E,KAAK,EAAE;oBAAEqF,MAAM,EAAE,YAAY;oBAAEO,QAAQ,EAAE;kBAAW,CAAE;kBAAAT,QAAA,EAAC;gBAE1D;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GA/FEhF,MAAM,CAAC1D,EAAE;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgGd,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAtJ,oBAAoB,iBACnBvB,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTvD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,eACA/J,OAAA;QAAK4E,KAAK,EAAE;UACViF,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,MAAM;UACpBjB,OAAO,EAAE,MAAM;UACfiC,KAAK,EAAE,KAAK;UACZ/B,QAAQ,EAAE,OAAO;UACjBgE,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAzD,QAAA,gBACA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMtJ,uBAAuB,CAAC,KAAK,CAAE;YAC9CoD,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,SAAS;cAChBuI,MAAM,EAAE,SAAS;cACjBT,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEyF,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,eACrC/J,OAAA;YAAQ4E,KAAK,EAAE;cACbiF,UAAU,EAAE,SAAS;cACrBnH,KAAK,EAAE,OAAO;cACdoH,OAAO,EAAE,gBAAgB;cACzBiB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBf,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBACA/J,OAAA,CAACpB,MAAM;cAAA8L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,EACnE9H,OAAO,CAACkG,GAAG,CAACtC,MAAM,iBACjB7F,OAAA;YAAqB4E,KAAK,EAAE;cAC1BoG,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBjB,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,gBACA/J,OAAA;cAAI4E,KAAK,EAAE;gBAAE4F,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE,GAAG;gBAAEJ,YAAY,EAAE,QAAQ;gBAAE3H,KAAK,EAAEmD,MAAM,CAACnD;cAAM,CAAE;cAAAqH,QAAA,EAC7FlE,MAAM,CAACxD;YAAK;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACL7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAE0B,aAAa,EAAE,QAAQ;gBAAErB,GAAG,EAAE;cAAS,CAAE;cAAAR,QAAA,EACrElE,MAAM,CAAC9C,WAAW,IAAI8C,MAAM,CAAC9C,WAAW,CAACyC,MAAM,GAAG,CAAC,GAAGK,MAAM,CAAC9C,WAAW,CAACoF,GAAG,CAAC,CAACmG,UAAU,EAAEF,GAAG,kBAC5FpO,OAAA;gBAAe4E,KAAK,EAAE;kBACpBsF,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBN,OAAO,EAAE,SAAS;kBAClBD,UAAU,EAAE,SAAS;kBACrBkB,YAAY,EAAE,SAAS;kBACvBC,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,gBACA/J,OAAA;kBAAK4E,KAAK,EAAE;oBAAEsF,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEG,GAAG,EAAE;kBAAS,CAAE;kBAAAR,QAAA,gBACnE/J,OAAA,CAACb,WAAW;oBAACwI,IAAI,EAAE,EAAG;oBAACjF,KAAK,EAAC;kBAAS;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzC7K,OAAA;oBAAA+J,QAAA,EAAOuE;kBAAU;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACN7K,OAAA;kBAAK4E,KAAK,EAAE;oBAAEsF,OAAO,EAAE,MAAM;oBAAEK,GAAG,EAAE;kBAAS,CAAE;kBAAAR,QAAA,gBAC7C/J,OAAA;oBAAQ4E,KAAK,EAAE;sBACbiF,UAAU,EAAE,SAAS;sBACrBnH,KAAK,EAAE,OAAO;sBACdoH,OAAO,EAAE,gBAAgB;sBACzBiB,YAAY,EAAE,SAAS;sBACvBC,MAAM,EAAE,MAAM;sBACdC,MAAM,EAAE,SAAS;sBACjBT,QAAQ,EAAE;oBACZ,CAAE;oBAAAT,QAAA,EAAC;kBAEH;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT7K,OAAA;oBAAQ4E,KAAK,EAAE;sBACbiF,UAAU,EAAE,SAAS;sBACrBnH,KAAK,EAAE,OAAO;sBACdoH,OAAO,EAAE,gBAAgB;sBACzBiB,YAAY,EAAE,SAAS;sBACvBC,MAAM,EAAE,MAAM;sBACdC,MAAM,EAAE,SAAS;sBACjBT,QAAQ,EAAE;oBACZ,CAAE;oBAAAT,QAAA,EAAC;kBAEH;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GApCEuD,GAAG;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqCR,CACN,CAAC,gBACA7K,OAAA;gBAAG4E,KAAK,EAAE;kBAAElC,KAAK,EAAE,SAAS;kBAAE2L,SAAS,EAAE;gBAAS,CAAE;gBAAAtE,QAAA,EAAC;cAAkB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAC3E;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAnDEhF,MAAM,CAAC1D,EAAE;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoDd,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGApJ,eAAe,iBACdzB,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTvD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,eACA/J,OAAA;QAAK4E,KAAK,EAAE;UACViF,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,MAAM;UACpBjB,OAAO,EAAE,MAAM;UACfiC,KAAK,EAAE,KAAK;UACZ/B,QAAQ,EAAE,OAAO;UACjBgE,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAzD,QAAA,gBACA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMpJ,kBAAkB,CAAC,KAAK,CAAE;YACzCkD,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,SAAS;cAChBuI,MAAM,EAAE,SAAS;cACjBT,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,EACnE9H,OAAO,CAACkG,GAAG,CAACtC,MAAM,iBACjB7F,OAAA;YAAqB4E,KAAK,EAAE;cAC1BsF,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE,QAAQ;cACpBN,OAAO,EAAE,MAAM;cACfkB,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBlB,UAAU,EAAEhE,MAAM,CAAClD;YACrB,CAAE;YAAAoH,QAAA,gBACA/J,OAAA;cAAA+J,QAAA,gBACE/J,OAAA;gBAAI4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,QAAQ;kBAAEC,UAAU,EAAE,GAAG;kBAAER,MAAM,EAAE,CAAC;kBAAEvH,KAAK,EAAEmD,MAAM,CAACnD;gBAAM,CAAE;gBAAAqH,QAAA,EAChFlE,MAAM,CAACxD;cAAK;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACL7K,OAAA;gBAAG4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,UAAU;kBAAE9H,KAAK,EAAE,SAAS;kBAAEuH,MAAM,EAAE;gBAAE,CAAE;gBAAAF,QAAA,GAAC,YACrD,EAAClE,MAAM,CAAChD,QAAQ,EAAC,GAC7B;cAAA;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN7K,OAAA;cAAK4E,KAAK,EAAE;gBAAEuJ,SAAS,EAAE;cAAQ,CAAE;cAAApE,QAAA,gBACjC/J,OAAA;gBAAK4E,KAAK,EAAE;kBACV4F,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE,MAAM;kBAClB/H,KAAK,EAAEmD,MAAM,CAACnD;gBAChB,CAAE;gBAAAqH,QAAA,EACClE,MAAM,CAACG,KAAK,IAAI;cAAK;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACN7K,OAAA;gBAAK4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,SAAS;kBAAE9H,KAAK,EAAE;gBAAU,CAAE;gBAAAqH,QAAA,EAAC;cAEvD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA5BEhF,MAAM,CAAC1D,EAAE;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Bd,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEqH,SAAS,EAAE,QAAQ;YAAEnC,OAAO,EAAE,MAAM;YAAED,UAAU,EAAE,SAAS;YAAEkB,YAAY,EAAE;UAAS,CAAE;UAAAhB,QAAA,gBAClG/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,GAAG;cAAEJ,YAAY,EAAE;YAAS,CAAE;YAAAN,QAAA,EAAC;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5F7K,OAAA;YAAK4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE;YAAU,CAAE;YAAAqH,QAAA,EAAC;UAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjF7K,OAAA;YAAG4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,UAAU;cAAE9H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEjE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAlJ,iBAAiB,iBAChB3B,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTvD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,eACA/J,OAAA;QAAK4E,KAAK,EAAE;UACViF,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,MAAM;UACpBjB,OAAO,EAAE,MAAM;UACfiC,KAAK,EAAE,KAAK;UACZ/B,QAAQ,EAAE,OAAO;UACjBgE,SAAS,EAAE,MAAM;UACjBR,SAAS,EAAE;QACb,CAAE;QAAAzD,QAAA,gBACA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAMlJ,oBAAoB,CAAC,KAAK,CAAE;YAC3CgD,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,SAAS;cAChBuI,MAAM,EAAE,SAAS;cACjBT,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEyF,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,eACrC/J,OAAA;YAAQ4E,KAAK,EAAE;cACbiF,UAAU,EAAE,SAAS;cACrBnH,KAAK,EAAE,OAAO;cACdoH,OAAO,EAAE,gBAAgB;cACzBiB,YAAY,EAAE,QAAQ;cACtBC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,SAAS;cACjBf,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBG,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBACA/J,OAAA,CAACpB,MAAM;cAAA8L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEkB,mBAAmB,EAAE,gBAAgB;YAAEb,GAAG,EAAE,KAAK;YAAEV,UAAU,EAAE,SAAS;YAAEkB,YAAY,EAAE,QAAQ;YAAEc,QAAQ,EAAE;UAAS,CAAE;UAAA9B,QAAA,GACnJ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC5B,GAAG,CAACoG,GAAG,iBACxDvO,OAAA;YAAe4E,KAAK,EAAE;cACpBiF,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,SAAS;cAClBqE,SAAS,EAAE,QAAQ;cACnB1D,UAAU,EAAE,GAAG;cACfD,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,EACCwE;UAAG,GAPIA,GAAG;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQR,CACN,CAAC,EAED2D,KAAK,CAACC,IAAI,CAAC;YAAEjJ,MAAM,EAAE;UAAG,CAAC,EAAE,CAACkJ,CAAC,EAAEC,CAAC,kBAC/B3O,OAAA;YAAa4E,KAAK,EAAE;cAClBiF,UAAU,EAAE,OAAO;cACnBC,OAAO,EAAE,SAAS;cAClBF,SAAS,EAAE,MAAM;cACjBY,QAAQ,EAAE,UAAU;cACpB2B,QAAQ,EAAE;YACZ,CAAE;YAAApC,QAAA,gBACA/J,OAAA;cAAK4E,KAAK,EAAE;gBAAElC,KAAK,EAAE;cAAU,CAAE;cAAAqH,QAAA,EAAI4E,CAAC,GAAG,EAAE,GAAI;YAAC;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACvD8D,CAAC,KAAK,EAAE,iBACP3O,OAAA;cAAK4E,KAAK,EAAE;gBACViF,UAAU,EAAE,SAAS;gBACrBnH,KAAK,EAAE,OAAO;gBACd8H,QAAQ,EAAE,SAAS;gBACnBV,OAAO,EAAE,SAAS;gBAClBiB,YAAY,EAAE,SAAS;gBACvBkB,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,EACA8D,CAAC,KAAK,EAAE,iBACP3O,OAAA;cAAK4E,KAAK,EAAE;gBACViF,UAAU,EAAE,SAAS;gBACrBnH,KAAK,EAAE,OAAO;gBACd8H,QAAQ,EAAE,SAAS;gBACnBV,OAAO,EAAE,SAAS;gBAClBiB,YAAY,EAAE,SAAS;gBACvBkB,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,GA/BO8D,CAAC;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCN,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEqH,SAAS,EAAE;UAAS,CAAE;UAAAlC,QAAA,gBAClC/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,GAAG;cAAEJ,YAAY,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAC;UAAe;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9F7K,OAAA;YAAK4E,KAAK,EAAE;cAAEsF,OAAO,EAAE,MAAM;cAAE0B,aAAa,EAAE,QAAQ;cAAErB,GAAG,EAAE;YAAS,CAAE;YAAAR,QAAA,EACrE/G,KAAK,CAAC4L,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACzG,GAAG,CAAChC,IAAI,iBACzBnG,OAAA;cAAmB4E,KAAK,EAAE;gBACxBsF,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBN,OAAO,EAAE,SAAS;gBAClBD,UAAU,EAAE1D,IAAI,CAACxD,OAAO;gBACxBoI,YAAY,EAAE;cAChB,CAAE;cAAAhB,QAAA,gBACA/J,OAAA;gBAAK4E,KAAK,EAAE;kBAAEsF,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEG,GAAG,EAAE;gBAAS,CAAE;gBAAAR,QAAA,gBACnE/J,OAAA,CAACmG,IAAI,CAAC/C,IAAI;kBAACuE,IAAI,EAAE,EAAG;kBAACjF,KAAK,EAAEyD,IAAI,CAACzD;gBAAM;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1C7K,OAAA;kBAAM4E,KAAK,EAAE;oBAAE6F,UAAU,EAAE;kBAAI,CAAE;kBAAAV,QAAA,EAAE5D,IAAI,CAAC9D;gBAAK;kBAAAqI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN7K,OAAA;gBAAM4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,UAAU;kBAAE9H,KAAK,EAAEyD,IAAI,CAACzD,KAAK;kBAAE+H,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EACvE5D,IAAI,CAAChD;cAAQ;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA,GAdC1E,IAAI,CAAChE,EAAE;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnH,eAAe,iBACd1D,OAAA;MAAK4E,KAAK,EAAE;QACVuH,QAAQ,EAAE,OAAO;QACjBc,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTvD,UAAU,EAAE,oBAAoB;QAChCK,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,MAAM,EAAE;MACV,CAAE;MAAAvD,QAAA,eACA/J,OAAA;QAAK4E,KAAK,EAAE;UACViF,UAAU,EAAE,OAAO;UACnBkB,YAAY,EAAE,MAAM;UACpBjB,OAAO,EAAE,MAAM;UACfiC,KAAK,EAAE,KAAK;UACZ/B,QAAQ,EAAE;QACZ,CAAE;QAAAD,QAAA,gBACA/J,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC7G/J,OAAA;YAAI4E,KAAK,EAAE;cAAE4F,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAE/H,KAAK,EAAE,SAAS;cAAEuH,MAAM,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAEpF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7K,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM;cACbnH,kBAAkB,CAAC,KAAK,CAAC;cACzBE,eAAe,CAAC,IAAI,CAAC;cACrBE,iBAAiB,CAAC,EAAE,CAAC;cACrBE,iBAAiB,CAAC,CAAC,CAAC;YACtB,CAAE;YACFW,KAAK,EAAE;cACLiF,UAAU,EAAE,MAAM;cAClBmB,MAAM,EAAE,MAAM;cACdtI,KAAK,EAAE,SAAS;cAChBuI,MAAM,EAAE,SAAS;cACjBT,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,eAEF/J,OAAA,CAACR,GAAG;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAE0B,aAAa,EAAE,QAAQ;YAAErB,GAAG,EAAE;UAAS,CAAE;UAAAR,QAAA,gBACtE/J,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7K,OAAA;cACEsL,KAAK,EAAExH,cAAe;cACtB6J,QAAQ,EAAGjI,CAAC,IAAK3B,iBAAiB,CAAC2B,CAAC,CAAC8B,MAAM,CAAC8D,KAAK,CAAE;cACnD1G,KAAK,EAAE;gBACLmH,KAAK,EAAE,MAAM;gBACbjC,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,QAAQ;gBACtBP,QAAQ,EAAE;cACZ,CAAE;cAAAT,QAAA,gBAEF/J,OAAA;gBAAQsL,KAAK,EAAC,EAAE;gBAAAvB,QAAA,EAAC;cAAkB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC3C5I,OAAO,CAACkG,GAAG,CAACtC,MAAM,iBACjB7F,OAAA;gBAAwBsL,KAAK,EAAEzF,MAAM,CAAC1D,EAAG;gBAAA4H,QAAA,EACtClE,MAAM,CAACxD;cAAK,GADFwD,MAAM,CAAC1D,EAAE;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7K,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAO4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,OAAO;gBAAEG,YAAY,EAAE,QAAQ;gBAAEI,UAAU,EAAE;cAAI,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7K,OAAA;cAAK4E,KAAK,EAAE;gBACVoG,MAAM,EAAE,oBAAoB;gBAC5BD,YAAY,EAAE,QAAQ;gBACtBjB,OAAO,EAAE,MAAM;gBACfqE,SAAS,EAAE,QAAQ;gBACnBtE,UAAU,EAAEjG,YAAY,GAAG,SAAS,GAAG,SAAS;gBAChDkI,WAAW,EAAElI,YAAY,GAAG,SAAS,GAAG;cAC1C,CAAE;cAAAmG,QAAA,gBACA/J,OAAA;gBACE0H,IAAI,EAAC,MAAM;gBACXmH,MAAM,EAAC,MAAM;gBACblB,QAAQ,EAAEtG,gBAAiB;gBAC3BzC,KAAK,EAAE;kBAAEsF,OAAO,EAAE;gBAAO,CAAE;gBAC3B/H,EAAE,EAAC;cAAY;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACF7K,OAAA;gBACE8O,OAAO,EAAC,YAAY;gBACpBlK,KAAK,EAAE;kBACLqG,MAAM,EAAE,SAAS;kBACjBf,OAAO,EAAE,MAAM;kBACf0B,aAAa,EAAE,QAAQ;kBACvBxB,UAAU,EAAE,QAAQ;kBACpBG,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBAEF/J,OAAA,CAACF,QAAQ;kBAAC6H,IAAI,EAAE,EAAG;kBAACjF,KAAK,EAAEkB,YAAY,GAAG,SAAS,GAAG;gBAAU;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAClEjH,YAAY,gBACX5D,OAAA;kBAAA+J,QAAA,gBACE/J,OAAA;oBAAK4E,KAAK,EAAE;sBAAE6F,UAAU,EAAE,GAAG;sBAAE/H,KAAK,EAAE;oBAAU,CAAE;oBAAAqH,QAAA,EAC/CnG,YAAY,CAACyE;kBAAI;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACN7K,OAAA;oBAAK4E,KAAK,EAAE;sBAAE4F,QAAQ,EAAE,UAAU;sBAAE9H,KAAK,EAAE;oBAAU,CAAE;oBAAAqH,QAAA,GACpD,CAACnG,YAAY,CAAC+D,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEW,OAAO,CAAC,CAAC,CAAC,EAAC,KAChD;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEN7K,OAAA;kBAAA+J,QAAA,gBACE/J,OAAA;oBAAK4E,KAAK,EAAE;sBAAE6F,UAAU,EAAE,GAAG;sBAAE/H,KAAK,EAAE;oBAAU,CAAE;oBAAAqH,QAAA,EAAC;kBAEnD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7K,OAAA;oBAAK4E,KAAK,EAAE;sBAAE4F,QAAQ,EAAE,UAAU;sBAAE9H,KAAK,EAAE;oBAAU,CAAE;oBAAAqH,QAAA,EAAC;kBAExD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL3G,WAAW,iBACVlE,OAAA;YAAA+J,QAAA,gBACE/J,OAAA;cAAK4E,KAAK,EAAE;gBAAEsF,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEE,YAAY,EAAE;cAAS,CAAE;cAAAN,QAAA,gBACvF/J,OAAA;gBAAM4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAV,QAAA,EAAC;cAAY;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3E7K,OAAA;gBAAM4E,KAAK,EAAE;kBAAE4F,QAAQ,EAAE,UAAU;kBAAE9H,KAAK,EAAE;gBAAU,CAAE;gBAAAqH,QAAA,GAAE/F,cAAc,EAAC,GAAC;cAAA;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACN7K,OAAA;cAAK4E,KAAK,EAAE;gBACVmH,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,QAAQ;gBAChBnC,UAAU,EAAE,SAAS;gBACrBkB,YAAY,EAAE,SAAS;gBACvBc,QAAQ,EAAE;cACZ,CAAE;cAAA9B,QAAA,eACA/J,OAAA;gBAAK4E,KAAK,EAAE;kBACVmH,KAAK,EAAE,GAAG/H,cAAc,GAAG;kBAC3BgI,MAAM,EAAE,MAAM;kBACdnC,UAAU,EAAE,SAAS;kBACrB/E,UAAU,EAAE;gBACd;cAAE;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN7K,OAAA;UAAK4E,KAAK,EAAE;YAAEsF,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE,MAAM;YAAE0B,SAAS,EAAE;UAAO,CAAE;UAAAlC,QAAA,gBAC9D/J,OAAA;YACE8K,OAAO,EAAEA,CAAA,KAAM;cACbnH,kBAAkB,CAAC,KAAK,CAAC;cACzBE,eAAe,CAAC,IAAI,CAAC;cACrBE,iBAAiB,CAAC,EAAE,CAAC;cACrBE,iBAAiB,CAAC,CAAC,CAAC;YACtB,CAAE;YACF8K,QAAQ,EAAE7K,WAAY;YACtBU,KAAK,EAAE;cACLsH,IAAI,EAAE,CAAC;cACPpC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAE,OAAO;cACnBnH,KAAK,EAAE,SAAS;cAChBsI,MAAM,EAAE,mBAAmB;cAC3BD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAE/G,WAAW,GAAG,aAAa,GAAG,SAAS;cAC/CW,OAAO,EAAEX,WAAW,GAAG,GAAG,GAAG;YAC/B,CAAE;YAAA6F,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7K,OAAA;YACE8K,OAAO,EAAElD,gBAAiB;YAC1BmH,QAAQ,EAAE,CAACnL,YAAY,IAAI,CAACE,cAAc,IAAII,WAAY;YAC1DU,KAAK,EAAE;cACLsH,IAAI,EAAE,CAAC;cACPpC,OAAO,EAAE,SAAS;cAClBD,UAAU,EAAG,CAACjG,YAAY,IAAI,CAACE,cAAc,IAAII,WAAW,GAAI,SAAS,GAAG,SAAS;cACrFxB,KAAK,EAAE,OAAO;cACdsI,MAAM,EAAE,MAAM;cACdD,YAAY,EAAE,QAAQ;cACtBE,MAAM,EAAG,CAACrH,YAAY,IAAI,CAACE,cAAc,IAAII,WAAW,GAAI,aAAa,GAAG,SAAS;cACrFgG,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxBI,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,EAED7F,WAAW,gBACVlE,OAAA,CAAAE,SAAA;cAAA6J,QAAA,gBACE/J,OAAA,CAACtB,QAAQ;gBAACsO,SAAS,EAAC,cAAc;gBAACrF,IAAI,EAAE;cAAG;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjD;YAAA,eAAE,CAAC,gBAEH7K,OAAA,CAAAE,SAAA;cAAA6J,QAAA,gBACE/J,OAAA,CAACF,QAAQ;gBAAC6H,IAAI,EAAE;cAAG;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAExB;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7K,OAAA;MAAA+J,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzK,EAAA,CAr1EID,iBAAiB;AAAA6O,EAAA,GAAjB7O,iBAAiB;AAu1EvB,eAAeA,iBAAiB;AAAC,IAAA6O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}