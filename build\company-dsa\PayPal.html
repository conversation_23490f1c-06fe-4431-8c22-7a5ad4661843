
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal DSA Questions</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #818cf8;
            --secondary: #f1f5f9;
            --easy: #22c55e;
            --medium: #f59e0b;
            --hard: #ef4444;
            --text: #0f172a;
            --text-light: #64748b;
            --card-bg: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--secondary);
            color: var(--text);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        h1 {
            color: var(--primary-dark);
            text-align: center;
            margin: 40px 0;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
            }
            to {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
            }
        }

        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 20px;
            border: 2px solid var(--primary-light);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: var(--card-bg);
            color: var(--text);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .difficulty-filter {
            display: flex;
            gap: 12px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            opacity: 0.8;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            opacity: 1;
        }

        .filter-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: var(--shadow);
        }

        .filter-btn.easy {
            background-color: var(--easy);
            color: white;
        }

        .filter-btn.medium {
            background-color: var(--medium);
            color: white;
        }

        .filter-btn.hard {
            background-color: var(--hard);
            color: white;
        }

        .topics-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 20px 0 30px;
        }

        .topic-tag {
            padding: 8px 16px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.8;
            transition: all 0.3s ease;
            user-select: none;
        }

        .topic-tag:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        .topic-tag.active {
            opacity: 1;
            background-color: var(--primary-dark);
            box-shadow: var(--shadow);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 16px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .difficulty {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .difficulty-easy { background-color: var(--easy); color: white; }
        .difficulty-medium { background-color: var(--medium); color: white; }
        .difficulty-hard { background-color: var(--hard); color: white; }

        .question-title {
            font-size: 1.2em;
            margin: 10px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        .question-link {
            color: var(--primary-dark);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .question-link:hover {
            color: var(--primary);
            text-decoration: underline;
        }

        .question-stats {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            font-size: 0.95em;
            color: var(--text-light);
            padding-top: 15px;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
        }

        .topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .topic-badge:hover {
            transform: translateY(-2px);
            background-color: var(--primary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            h1 {
                font-size: 2rem;
                margin: 30px 0;
            }

            .filters {
                flex-direction: column;
                gap: 15px;
            }

            .difficulty-filter {
                justify-content: center;
                width: 100%;
            }

            .search-box {
                width: 100%;
            }

            .stat-card {
                padding: 20px;
            }

            .question-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 2em;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 1.75rem;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .topic-tag {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .question-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling and selection styles */
        html {
            scroll-behavior: smooth;
        }

        ::selection {
            background-color: var(--primary-light);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PayPal DSA Questions</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">211</div>
                <div>Total Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">38</div>
                <div>Easy Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">142</div>
                <div>Medium Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">31</div>
                <div>Hard Questions</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" placeholder="Search questions..." id="searchInput">
            <div class="difficulty-filter">
                <button class="filter-btn easy active" data-difficulty="EASY">Easy</button>
                <button class="filter-btn medium active" data-difficulty="MEDIUM">Medium</button>
                <button class="filter-btn hard active" data-difficulty="HARD">Hard</button>
            </div>
        </div>

        <div class="topics-filter">
            <div class="topic-tag" data-topic="Array">Array</div><div class="topic-tag" data-topic="Backtracking">Backtracking</div><div class="topic-tag" data-topic="Binary Search">Binary Search</div><div class="topic-tag" data-topic="Binary Tree">Binary Tree</div><div class="topic-tag" data-topic="Breadth-First Search">Breadth-First Search</div><div class="topic-tag" data-topic="Bucket Sort">Bucket Sort</div><div class="topic-tag" data-topic="Counting">Counting</div><div class="topic-tag" data-topic="Data Stream">Data Stream</div><div class="topic-tag" data-topic="Depth-First Search">Depth-First Search</div><div class="topic-tag" data-topic="Design">Design</div><div class="topic-tag" data-topic="Divide and Conquer">Divide and Conquer</div><div class="topic-tag" data-topic="Doubly-Linked List">Doubly-Linked List</div><div class="topic-tag" data-topic="Dynamic Programming">Dynamic Programming</div><div class="topic-tag" data-topic="Enumeration">Enumeration</div><div class="topic-tag" data-topic="Graph">Graph</div><div class="topic-tag" data-topic="Greedy">Greedy</div><div class="topic-tag" data-topic="Hash Table">Hash Table</div><div class="topic-tag" data-topic="Heap (Priority Queue)">Heap (Priority Queue)</div><div class="topic-tag" data-topic="Linked List">Linked List</div><div class="topic-tag" data-topic="Math">Math</div><div class="topic-tag" data-topic="Matrix">Matrix</div><div class="topic-tag" data-topic="Monotonic Stack">Monotonic Stack</div><div class="topic-tag" data-topic="Number Theory">Number Theory</div><div class="topic-tag" data-topic="Ordered Set">Ordered Set</div><div class="topic-tag" data-topic="Prefix Sum">Prefix Sum</div><div class="topic-tag" data-topic="Queue">Queue</div><div class="topic-tag" data-topic="Quickselect">Quickselect</div><div class="topic-tag" data-topic="Randomized">Randomized</div><div class="topic-tag" data-topic="Recursion">Recursion</div><div class="topic-tag" data-topic="Simulation">Simulation</div><div class="topic-tag" data-topic="Sliding Window">Sliding Window</div><div class="topic-tag" data-topic="Sorting">Sorting</div><div class="topic-tag" data-topic="Stack">Stack</div><div class="topic-tag" data-topic="String">String</div><div class="topic-tag" data-topic="Topological Sort">Topological Sort</div><div class="topic-tag" data-topic="Tree">Tree</div><div class="topic-tag" data-topic="Trie">Trie</div><div class="topic-tag" data-topic="Two Pointers">Two Pointers</div><div class="topic-tag" data-topic="Union Find">Union Find</div>
        </div>

        <div class="question-grid">

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximal-square" class="question-link" target="_blank">Maximal Square</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/zigzag-conversion" class="question-link" target="_blank">Zigzag Conversion</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 50.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/loud-and-rich" class="question-link" target="_blank">Loud and Rich</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/coin-change" class="question-link" target="_blank">Coin Change</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 94.8%</span>
                    <span>Acceptance: 45.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-absolute-difference" class="question-link" target="_blank">Minimum Absolute Difference</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 94.8%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/coin-change" class="question-link" target="_blank">Coin Change</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 93.8%</span>
                    <span>Acceptance: 45.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/zigzag-conversion" class="question-link" target="_blank">Zigzag Conversion</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 88.4%</span>
                    <span>Acceptance: 50.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Binary Search", "Prefix Sum", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/random-pick-with-weight" class="question-link" target="_blank">Random Pick with Weight</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Prefix Sum</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 88.4%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 88.4%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 88.4%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/product-of-array-except-self" class="question-link" target="_blank">Product of Array Except Self</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 88.4%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 88.4%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/loud-and-rich" class="question-link" target="_blank">Loud and Rich</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 85.9%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 85.9%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/pairs-of-songs-with-total-durations-divisible-by-60" class="question-link" target="_blank">Pairs of Songs With Total Durations Divisible by 60</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 80.2%</span>
                    <span>Acceptance: 53.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/loud-and-rich" class="question-link" target="_blank">Loud and Rich</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 80.2%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 80.2%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-increment-to-make-array-unique" class="question-link" target="_blank">Minimum Increment to Make Array Unique</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 80.2%</span>
                    <span>Acceptance: 60.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-increasing-subsequence" class="question-link" target="_blank">Longest Increasing Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 80.2%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "String", "Counting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-the-number-of-vowel-strings-in-range" class="question-link" target="_blank">Count the Number of Vowel Strings in Range</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.7%</span>
                    <span>Acceptance: 74.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-vowel-strings-in-ranges" class="question-link" target="_blank">Count Vowel Strings in Ranges</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.7%</span>
                    <span>Acceptance: 67.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Greedy"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-replacements-to-sort-the-array" class="question-link" target="_blank">Minimum Replacements to Sort the Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.4%</span>
                    <span>Acceptance: 53.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/time-needed-to-rearrange-a-binary-string" class="question-link" target="_blank">Time Needed to Rearrange a Binary String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 51.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Binary Search", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-integers-to-choose-from-a-range-i" class="question-link" target="_blank">Maximum Number of Integers to Choose From a Range I</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 68.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Greedy"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-replacements-to-sort-the-array" class="question-link" target="_blank">Minimum Replacements to Sort the Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 53.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Backtracking", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-valid-move-combinations-on-chessboard" class="question-link" target="_blank">Number of Valid Move Combinations On Chessboard</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 51.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Stack", "Sliding Window"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-frequency-score-of-a-subarray" class="question-link" target="_blank">Maximum Frequency Score of a Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Stack</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 34.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/meeting-scheduler" class="question-link" target="_blank">Meeting Scheduler</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 55.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Number Theory"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-array-pairs-divisible-by-k" class="question-link" target="_blank">Count Array Pairs Divisible by K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Number Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.8%</span>
                    <span>Acceptance: 29.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-increasing-subsequence" class="question-link" target="_blank">Longest Increasing Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.8%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/last-stone-weight" class="question-link" target="_blank">Last Stone Weight</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.8%</span>
                    <span>Acceptance: 65.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-sudoku" class="question-link" target="_blank">Valid Sudoku</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.8%</span>
                    <span>Acceptance: 61.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-events-that-can-be-attended" class="question-link" target="_blank">Maximum Number of Events That Can Be Attended</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.8%</span>
                    <span>Acceptance: 32.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.8%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Enumeration"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/smallest-greater-multiple-made-of-two-digits" class="question-link" target="_blank">Smallest Greater Multiple Made of Two Digits</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.6%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-integers-to-choose-from-a-range-ii" class="question-link" target="_blank">Maximum Number of Integers to Choose From a Range II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.6%</span>
                    <span>Acceptance: 35.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Greedy", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-non-zero-product-of-the-array-elements" class="question-link" target="_blank">Minimum Non-Zero Product of the Array Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Greedy</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.6%</span>
                    <span>Acceptance: 36.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-sum-obtained-of-any-permutation" class="question-link" target="_blank">Maximum Sum Obtained of Any Permutation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.6%</span>
                    <span>Acceptance: 38.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/paint-house-iii" class="question-link" target="_blank">Paint House III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.6%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.6%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.5%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 72.3%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/pairs-of-songs-with-total-durations-divisible-by-60" class="question-link" target="_blank">Pairs of Songs With Total Durations Divisible by 60</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 72.3%</span>
                    <span>Acceptance: 53.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.6%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.6%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.6%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/last-stone-weight" class="question-link" target="_blank">Last Stone Weight</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 65.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/degree-of-an-array" class="question-link" target="_blank">Degree of an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 57.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-sudoku" class="question-link" target="_blank">Valid Sudoku</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 61.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/palindromic-substrings" class="question-link" target="_blank">Palindromic Substrings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 71.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "Design", "Sorting", "Heap (Priority Queue)", "Data Stream"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-median-from-data-stream" class="question-link" target="_blank">Find Median from Data Stream</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Design</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Data Stream</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 53.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-peak-element" class="question-link" target="_blank">Find Peak Element</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 46.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-events-that-can-be-attended" class="question-link" target="_blank">Maximum Number of Events That Can Be Attended</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 32.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-largest-element-in-an-array" class="question-link" target="_blank">Kth Largest Element in an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/first-missing-positive" class="question-link" target="_blank">First Missing Positive</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 40.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Matrix", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/spiral-matrix" class="question-link" target="_blank">Spiral Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Matrix</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.6%</span>
                    <span>Acceptance: 52.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/pairs-of-songs-with-total-durations-divisible-by-60" class="question-link" target="_blank">Pairs of Songs With Total Durations Divisible by 60</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 67.2%</span>
                    <span>Acceptance: 53.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-sorted-array" class="question-link" target="_blank">Merge Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 67.2%</span>
                    <span>Acceptance: 52.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 67.2%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 67.2%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-sum-equals-k" class="question-link" target="_blank">Subarray Sum Equals K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.3%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Greedy", "Sorting", "Heap (Priority Queue)", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reorganize-string" class="question-link" target="_blank">Reorganize String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.3%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/coin-change" class="question-link" target="_blank">Coin Change</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.2%</span>
                    <span>Acceptance: 45.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/product-of-array-except-self" class="question-link" target="_blank">Product of Array Except Self</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.2%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximal-square" class="question-link" target="_blank">Maximal Square</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.2%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.2%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-sorted-array" class="question-link" target="_blank">Merge Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.2%</span>
                    <span>Acceptance: 52.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-increasing-subsequence" class="question-link" target="_blank">Longest Increasing Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.2%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.2%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-sum-equals-k" class="question-link" target="_blank">Subarray Sum Equals K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.2%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String", "Backtracking", "Depth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-search" class="question-link" target="_blank">Word Search</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.1%</span>
                    <span>Acceptance: 44.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-intervals" class="question-link" target="_blank">Merge Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.1%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-absolute-difference" class="question-link" target="_blank">Minimum Absolute Difference</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.1%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-the-smallest-divisor-given-a-threshold" class="question-link" target="_blank">Find the Smallest Divisor Given a Threshold</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.1%</span>
                    <span>Acceptance: 62.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Bucket Sort", "Counting", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/top-k-frequent-elements" class="question-link" target="_blank">Top K Frequent Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Bucket Sort</span><span class="topic-badge">Counting</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.1%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/loud-and-rich" class="question-link" target="_blank">Loud and Rich</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.1%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Greedy", "Sorting", "Heap (Priority Queue)", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reorganize-string" class="question-link" target="_blank">Reorganize String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.1%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-vowel-substrings-of-a-string" class="question-link" target="_blank">Count Vowel Substrings of a String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 69.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-intervals" class="question-link" target="_blank">Merge Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Bucket Sort", "Counting", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/top-k-frequent-elements" class="question-link" target="_blank">Top K Frequent Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Bucket Sort</span><span class="topic-badge">Counting</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String", "Backtracking", "Depth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-search" class="question-link" target="_blank">Word Search</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 44.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "String", "Counting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-the-number-of-vowel-strings-in-range" class="question-link" target="_blank">Count the Number of Vowel Strings in Range</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 74.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Sorting", "Heap (Priority Queue)", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/meeting-rooms-iii" class="question-link" target="_blank">Meeting Rooms III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 44.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-the-smallest-divisor-given-a-threshold" class="question-link" target="_blank">Find the Smallest Divisor Given a Threshold</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 62.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sort-colors" class="question-link" target="_blank">Sort Colors</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.1%</span>
                    <span>Acceptance: 66.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Sorting", "Heap (Priority Queue)", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/meeting-rooms-iii" class="question-link" target="_blank">Meeting Rooms III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.1%</span>
                    <span>Acceptance: 44.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.1%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Binary Search", "Prefix Sum", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/random-pick-with-weight" class="question-link" target="_blank">Random Pick with Weight</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Prefix Sum</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.1%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array" class="question-link" target="_blank">Find First and Last Position of Element in Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.1%</span>
                    <span>Acceptance: 46.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-vowel-substrings-of-a-string" class="question-link" target="_blank">Count Vowel Substrings of a String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.1%</span>
                    <span>Acceptance: 69.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-vowel-strings-in-ranges" class="question-link" target="_blank">Count Vowel Strings in Ranges</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 67.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Number Theory"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-array-pairs-divisible-by-k" class="question-link" target="_blank">Count Array Pairs Divisible by K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Number Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 29.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Backtracking", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-valid-move-combinations-on-chessboard" class="question-link" target="_blank">Number of Valid Move Combinations On Chessboard</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 51.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-linked-list" class="question-link" target="_blank">Reverse Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 78.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "Stack", "Design", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-frequency-stack" class="question-link" target="_blank">Maximum Frequency Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Stack</span><span class="topic-badge">Design</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 66.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array" class="question-link" target="_blank">Find First and Last Position of Element in Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 46.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/time-needed-to-rearrange-a-binary-string" class="question-link" target="_blank">Time Needed to Rearrange a Binary String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 51.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sort-colors" class="question-link" target="_blank">Sort Colors</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 66.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-increasing-subsequence" class="question-link" target="_blank">Longest Increasing Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/min-stack" class="question-link" target="_blank">Min Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 55.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-product-subarray" class="question-link" target="_blank">Maximum Product Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.2%</span>
                    <span>Acceptance: 34.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Matrix", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/spiral-matrix" class="question-link" target="_blank">Spiral Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Matrix</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 52.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/first-missing-positive" class="question-link" target="_blank">First Missing Positive</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 40.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "Stack", "Design", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-frequency-stack" class="question-link" target="_blank">Maximum Frequency Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Stack</span><span class="topic-badge">Design</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 66.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/min-stack" class="question-link" target="_blank">Min Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 55.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-events-that-can-be-attended" class="question-link" target="_blank">Maximum Number of Events That Can Be Attended</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 32.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-and-say" class="question-link" target="_blank">Count and Say</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 57.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Sliding Window", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-product-less-than-k" class="question-link" target="_blank">Subarray Product Less Than K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 52.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-increment-to-make-array-unique" class="question-link" target="_blank">Minimum Increment to Make Array Unique</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 60.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Divide and Conquer"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/median-of-two-sorted-arrays" class="question-link" target="_blank">Median of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Divide and Conquer</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-linked-list" class="question-link" target="_blank">Reverse Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 78.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/degree-of-an-array" class="question-link" target="_blank">Degree of an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 57.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-product-subarray" class="question-link" target="_blank">Maximum Product Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 34.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/delete-node-in-a-linked-list" class="question-link" target="_blank">Delete Node in a Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 81.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-largest-element-in-an-array" class="question-link" target="_blank">Kth Largest Element in an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-twitter" class="question-link" target="_blank">Design Twitter</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 41.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-anagram" class="question-link" target="_blank">Valid Anagram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 66.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Counting", "Prefix Sum"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-population-year" class="question-link" target="_blank">Maximum Population Year</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Counting</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 62.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 65.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/koko-eating-bananas" class="question-link" target="_blank">Koko Eating Bananas</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy", "Sorting", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-sum-obtained-of-any-permutation" class="question-link" target="_blank">Maximum Sum Obtained of Any Permutation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 38.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/meeting-scheduler" class="question-link" target="_blank">Meeting Scheduler</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 55.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/amount-of-time-for-binary-tree-to-be-infected" class="question-link" target="_blank">Amount of Time for Binary Tree to Be Infected</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 63.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-twitter" class="question-link" target="_blank">Design Twitter</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 41.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-string" class="question-link" target="_blank">Reverse String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 79.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-image" class="question-link" target="_blank">Rotate Image</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 77.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Enumeration"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/smallest-greater-multiple-made-of-two-digits" class="question-link" target="_blank">Smallest Greater Multiple Made of Two Digits</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/product-of-array-except-self" class="question-link" target="_blank">Product of Array Except Self</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Divide and Conquer"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/median-of-two-sorted-arrays" class="question-link" target="_blank">Median of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Divide and Conquer</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Backtracking", "Trie", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-search-ii" class="question-link" target="_blank">Word Search II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Trie</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 37.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-anagram" class="question-link" target="_blank">Valid Anagram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 66.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/loud-and-rich" class="question-link" target="_blank">Loud and Rich</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Greedy", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-non-zero-product-of-the-array-elements" class="question-link" target="_blank">Minimum Non-Zero Product of the Array Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Greedy</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 36.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-nth-node-from-end-of-list" class="question-link" target="_blank">Remove Nth Node From End of List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 48.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-integers-to-choose-from-a-range-ii" class="question-link" target="_blank">Maximum Number of Integers to Choose From a Range II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 35.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/combination-sum" class="question-link" target="_blank">Combination Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 73.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-palindrome" class="question-link" target="_blank">Valid Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 50.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber" class="question-link" target="_blank">House Robber</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/squares-of-a-sorted-array" class="question-link" target="_blank">Squares of a Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 73.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Queue", "Counting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/first-unique-character-in-a-string" class="question-link" target="_blank">First Unique Character in a String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Queue</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 63.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/delete-node-in-a-linked-list" class="question-link" target="_blank">Delete Node in a Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 81.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/paint-house-iii" class="question-link" target="_blank">Paint House III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/break-a-palindrome" class="question-link" target="_blank">Break a Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 51.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/largest-rectangle-in-histogram" class="question-link" target="_blank">Largest Rectangle in Histogram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 46.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Two Pointers", "Binary Search", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/intersection-of-two-arrays" class="question-link" target="_blank">Intersection of Two Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 76.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/process-tasks-using-servers" class="question-link" target="_blank">Process Tasks Using Servers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 40.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/invalid-transactions" class="question-link" target="_blank">Invalid Transactions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 31.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-minimum-in-rotated-sorted-array" class="question-link" target="_blank">Find Minimum in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 52.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Binary Search", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-integers-to-choose-from-a-range-i" class="question-link" target="_blank">Maximum Number of Integers to Choose From a Range I</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 68.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Sliding Window", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-closest-elements" class="question-link" target="_blank">Find K Closest Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 48.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-and-say" class="question-link" target="_blank">Count and Say</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 57.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/container-with-most-water" class="question-link" target="_blank">Container With Most Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Stack", "Sliding Window"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-frequency-score-of-a-subarray" class="question-link" target="_blank">Maximum Frequency Score of a Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Stack</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 34.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-window-substring" class="question-link" target="_blank">Minimum Window Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 44.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/text-justification" class="question-link" target="_blank">Text Justification</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 47.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/all-nodes-distance-k-in-binary-tree" class="question-link" target="_blank">All Nodes Distance K in Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 65.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-sums-divisible-by-k" class="question-link" target="_blank">Subarray Sums Divisible by K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 55.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Binary Search", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-value-at-a-given-index-in-a-bounded-array" class="question-link" target="_blank">Maximum Value at a Given Index in a Bounded Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 38.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Sliding Window", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-product-less-than-k" class="question-link" target="_blank">Subarray Product Less Than K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 43.5%</span>
                    <span>Acceptance: 52.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Queue", "Counting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/first-unique-character-in-a-string" class="question-link" target="_blank">First Unique Character in a String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Queue</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 63.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/process-tasks-using-servers" class="question-link" target="_blank">Process Tasks Using Servers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 40.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/text-justification" class="question-link" target="_blank">Text Justification</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 47.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/jump-game-ii" class="question-link" target="_blank">Jump Game II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 41.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lexicographically-smallest-palindrome" class="question-link" target="_blank">Lexicographically Smallest Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 81.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/all-nodes-distance-k-in-binary-tree" class="question-link" target="_blank">All Nodes Distance K in Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 65.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/invalid-transactions" class="question-link" target="_blank">Invalid Transactions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 31.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "Design", "Sorting", "Heap (Priority Queue)", "Data Stream"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-median-from-data-stream" class="question-link" target="_blank">Find Median from Data Stream</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Design</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Data Stream</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 53.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-palindrome" class="question-link" target="_blank">Valid Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 50.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Counting", "Prefix Sum"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-population-year" class="question-link" target="_blank">Maximum Population Year</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Counting</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 62.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Binary Search", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-value-at-a-given-index-in-a-bounded-array" class="question-link" target="_blank">Maximum Value at a Given Index in a Bounded Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 38.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-peak-element" class="question-link" target="_blank">Find Peak Element</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 46.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Stack", "Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-binary-tree-to-linked-list" class="question-link" target="_blank">Flatten Binary Tree to Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 67.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-string" class="question-link" target="_blank">Reverse String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 79.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/combination-sum" class="question-link" target="_blank">Combination Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 73.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-sums-divisible-by-k" class="question-link" target="_blank">Subarray Sums Divisible by K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 55.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-in-between-linked-lists" class="question-link" target="_blank">Merge In Between Linked Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 82.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Backtracking", "Trie", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-search-ii" class="question-link" target="_blank">Word Search II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Trie</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 37.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Two Pointers", "Binary Search", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/intersection-of-two-arrays" class="question-link" target="_blank">Intersection of Two Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 76.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/container-with-most-water" class="question-link" target="_blank">Container With Most Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/jump-game" class="question-link" target="_blank">Jump Game</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 39.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-substrings-containing-all-three-characters" class="question-link" target="_blank">Number of Substrings Containing All Three Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 68.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/daily-temperatures" class="question-link" target="_blank">Daily Temperatures</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 67.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/divide-players-into-teams-of-equal-skill" class="question-link" target="_blank">Divide Players Into Teams of Equal Skill</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 69.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-minimum-in-rotated-sorted-array" class="question-link" target="_blank">Find Minimum in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 52.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-image" class="question-link" target="_blank">Rotate Image</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 77.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Greedy"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-number-of-removals-to-make-mountain-array" class="question-link" target="_blank">Minimum Number of Removals to Make Mountain Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 55.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/largest-rectangle-in-histogram" class="question-link" target="_blank">Largest Rectangle in Histogram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 46.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-window-substring" class="question-link" target="_blank">Minimum Window Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 44.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Stack", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-number-of-swaps-to-make-the-string-balanced" class="question-link" target="_blank">Minimum Number of Swaps to Make the String Balanced</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Stack</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 78.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-substring" class="question-link" target="_blank">Longest Palindromic Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 35.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 65.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-nth-node-from-end-of-list" class="question-link" target="_blank">Remove Nth Node From End of List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 48.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/amount-of-time-for-binary-tree-to-be-infected" class="question-link" target="_blank">Amount of Time for Binary Tree to Be Infected</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 63.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/break-a-palindrome" class="question-link" target="_blank">Break a Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 51.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber" class="question-link" target="_blank">House Robber</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Sliding Window", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-closest-elements" class="question-link" target="_blank">Find K Closest Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 38.8%</span>
                    <span>Acceptance: 48.3%</span>
                </div>
            </div>

        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const questionCards = document.querySelectorAll('.question-card');
        const difficultyButtons = document.querySelectorAll('.filter-btn');
        const topicTags = document.querySelectorAll('.topic-tag');
        
        function filterQuestions() {
            const searchTerm = searchInput.value.toLowerCase();
            const activeDifficulties = Array.from(difficultyButtons)
                .filter(btn => btn.classList.contains('active'))
                .map(btn => btn.dataset.difficulty);
            const activeTopics = Array.from(topicTags)
                .filter(tag => tag.classList.contains('active'))
                .map(tag => tag.dataset.topic);
            
            questionCards.forEach(card => {
                const title = card.querySelector('.question-title').textContent.toLowerCase();
                const difficulty = card.dataset.difficulty;
                const topics = JSON.parse(card.dataset.topics);
                
                const matchesSearch = title.includes(searchTerm);
                const matchesDifficulty = activeDifficulties.includes(difficulty);
                const matchesTopics = activeTopics.length === 0 || 
                    topics.some(topic => activeTopics.includes(topic));
                
                if (matchesSearch && matchesDifficulty && matchesTopics) {
                    card.style.display = 'flex';
                    card.style.opacity = '1';
                } else {
                    card.style.display = 'none';
                    card.style.opacity = '0';
                }
            });
        }
        
        // Add smooth transitions for filtering
        questionCards.forEach(card => {
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        });
        
        searchInput.addEventListener('input', filterQuestions);
        
        difficultyButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                btn.classList.toggle('active');
                filterQuestions();
            });
        });
        
        topicTags.forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('active');
                filterQuestions();
            });
        });

        // Add smooth scrolling for all links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
