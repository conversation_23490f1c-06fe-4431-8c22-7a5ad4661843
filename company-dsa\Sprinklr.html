
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sprinklr DSA Questions</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #818cf8;
            --secondary: #f1f5f9;
            --easy: #22c55e;
            --medium: #f59e0b;
            --hard: #ef4444;
            --text: #0f172a;
            --text-light: #64748b;
            --card-bg: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--secondary);
            color: var(--text);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        h1 {
            color: var(--primary-dark);
            text-align: center;
            margin: 40px 0;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
            }
            to {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
            }
        }

        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 20px;
            border: 2px solid var(--primary-light);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: var(--card-bg);
            color: var(--text);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .difficulty-filter {
            display: flex;
            gap: 12px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            opacity: 0.8;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            opacity: 1;
        }

        .filter-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: var(--shadow);
        }

        .filter-btn.easy {
            background-color: var(--easy);
            color: white;
        }

        .filter-btn.medium {
            background-color: var(--medium);
            color: white;
        }

        .filter-btn.hard {
            background-color: var(--hard);
            color: white;
        }

        .topics-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 20px 0 30px;
        }

        .topic-tag {
            padding: 8px 16px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.8;
            transition: all 0.3s ease;
            user-select: none;
        }

        .topic-tag:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        .topic-tag.active {
            opacity: 1;
            background-color: var(--primary-dark);
            box-shadow: var(--shadow);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 16px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .difficulty {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .difficulty-easy { background-color: var(--easy); color: white; }
        .difficulty-medium { background-color: var(--medium); color: white; }
        .difficulty-hard { background-color: var(--hard); color: white; }

        .question-title {
            font-size: 1.2em;
            margin: 10px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        .question-link {
            color: var(--primary-dark);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .question-link:hover {
            color: var(--primary);
            text-decoration: underline;
        }

        .question-stats {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            font-size: 0.95em;
            color: var(--text-light);
            padding-top: 15px;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
        }

        .topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .topic-badge:hover {
            transform: translateY(-2px);
            background-color: var(--primary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            h1 {
                font-size: 2rem;
                margin: 30px 0;
            }

            .filters {
                flex-direction: column;
                gap: 15px;
            }

            .difficulty-filter {
                justify-content: center;
                width: 100%;
            }

            .search-box {
                width: 100%;
            }

            .stat-card {
                padding: 20px;
            }

            .question-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 2em;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 1.75rem;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .topic-tag {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .question-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling and selection styles */
        html {
            scroll-behavior: smooth;
        }

        ::selection {
            background-color: var(--primary-light);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sprinklr DSA Questions</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">89</div>
                <div>Total Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">2</div>
                <div>Easy Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">46</div>
                <div>Medium Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">41</div>
                <div>Hard Questions</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" placeholder="Search questions..." id="searchInput">
            <div class="difficulty-filter">
                <button class="filter-btn easy active" data-difficulty="EASY">Easy</button>
                <button class="filter-btn medium active" data-difficulty="MEDIUM">Medium</button>
                <button class="filter-btn hard active" data-difficulty="HARD">Hard</button>
            </div>
        </div>

        <div class="topics-filter">
            <div class="topic-tag" data-topic="Array">Array</div><div class="topic-tag" data-topic="Backtracking">Backtracking</div><div class="topic-tag" data-topic="Binary Indexed Tree">Binary Indexed Tree</div><div class="topic-tag" data-topic="Binary Search">Binary Search</div><div class="topic-tag" data-topic="Binary Tree">Binary Tree</div><div class="topic-tag" data-topic="Bit Manipulation">Bit Manipulation</div><div class="topic-tag" data-topic="Bitmask">Bitmask</div><div class="topic-tag" data-topic="Breadth-First Search">Breadth-First Search</div><div class="topic-tag" data-topic="Counting">Counting</div><div class="topic-tag" data-topic="Depth-First Search">Depth-First Search</div><div class="topic-tag" data-topic="Design">Design</div><div class="topic-tag" data-topic="Divide and Conquer">Divide and Conquer</div><div class="topic-tag" data-topic="Doubly-Linked List">Doubly-Linked List</div><div class="topic-tag" data-topic="Dynamic Programming">Dynamic Programming</div><div class="topic-tag" data-topic="Enumeration">Enumeration</div><div class="topic-tag" data-topic="Geometry">Geometry</div><div class="topic-tag" data-topic="Graph">Graph</div><div class="topic-tag" data-topic="Greedy">Greedy</div><div class="topic-tag" data-topic="Hash Function">Hash Function</div><div class="topic-tag" data-topic="Hash Table">Hash Table</div><div class="topic-tag" data-topic="Linked List">Linked List</div><div class="topic-tag" data-topic="Math">Math</div><div class="topic-tag" data-topic="Matrix">Matrix</div><div class="topic-tag" data-topic="Memoization">Memoization</div><div class="topic-tag" data-topic="Merge Sort">Merge Sort</div><div class="topic-tag" data-topic="Monotonic Stack">Monotonic Stack</div><div class="topic-tag" data-topic="Number Theory">Number Theory</div><div class="topic-tag" data-topic="Ordered Set">Ordered Set</div><div class="topic-tag" data-topic="Prefix Sum">Prefix Sum</div><div class="topic-tag" data-topic="Randomized">Randomized</div><div class="topic-tag" data-topic="Rolling Hash">Rolling Hash</div><div class="topic-tag" data-topic="Segment Tree">Segment Tree</div><div class="topic-tag" data-topic="Simulation">Simulation</div><div class="topic-tag" data-topic="Sorting">Sorting</div><div class="topic-tag" data-topic="Stack">Stack</div><div class="topic-tag" data-topic="String">String</div><div class="topic-tag" data-topic="String Matching">String Matching</div><div class="topic-tag" data-topic="Strongly Connected Component">Strongly Connected Component</div><div class="topic-tag" data-topic="Tree">Tree</div><div class="topic-tag" data-topic="Two Pointers">Two Pointers</div><div class="topic-tag" data-topic="Union Find">Union Find</div>
        </div>

        <div class="question-grid">

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Tree", "Graph", "Strongly Connected Component"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-edge-weight-equilibrium-queries-in-a-tree" class="question-link" target="_blank">Minimum Edge Weight Equilibrium Queries in a Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Tree</span><span class="topic-badge">Graph</span><span class="topic-badge">Strongly Connected Component</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 43.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-score-of-non-overlapping-intervals" class="question-link" target="_blank">Maximum Score of Non-overlapping Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 32.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/asteroid-collision" class="question-link" target="_blank">Asteroid Collision</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 93.3%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Greedy", "Bit Manipulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/apply-operations-on-array-to-maximize-sum-of-squares" class="question-link" target="_blank">Apply Operations on Array to Maximize Sum of Squares</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Greedy</span><span class="topic-badge">Bit Manipulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.6%</span>
                    <span>Acceptance: 45.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Dynamic Programming", "Backtracking", "Bit Manipulation", "Number Theory", "Bitmask"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximize-score-after-n-operations" class="question-link" target="_blank">Maximize Score After N Operations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Number Theory</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.6%</span>
                    <span>Acceptance: 57.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Backtracking", "Enumeration"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/next-greater-numerically-balanced-number" class="question-link" target="_blank">Next Greater Numerically Balanced Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.6%</span>
                    <span>Acceptance: 49.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Bit Manipulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/apply-bitwise-operations-to-make-strings-equal" class="question-link" target="_blank">Apply Bitwise Operations to Make Strings Equal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Bit Manipulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 41.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-score-of-non-overlapping-intervals" class="question-link" target="_blank">Maximum Score of Non-overlapping Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 32.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Bit Manipulation", "Sorting", "Bitmask"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-subsequence-sum" class="question-link" target="_blank">Closest Subsequence Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Sorting</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 41.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Counting", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-same-end-substrings" class="question-link" target="_blank">Number of Same-End Substrings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 61.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Rolling Hash", "String Matching", "Hash Function"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-to-revert-word-to-initial-state-i" class="question-link" target="_blank">Minimum Time to Revert Word to Initial State I</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Rolling Hash</span><span class="topic-badge">String Matching</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 41.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Tree", "Depth-First Search", "Enumeration"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/create-components-with-same-value" class="question-link" target="_blank">Create Components With Same Value</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 53.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-the-number-of-powerful-integers" class="question-link" target="_blank">Count the Number of Powerful Integers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 26.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Rolling Hash", "String Matching", "Hash Function"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-to-revert-word-to-initial-state-ii" class="question-link" target="_blank">Minimum Time to Revert Word to Initial State II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Rolling Hash</span><span class="topic-badge">String Matching</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 35.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray-sum-after-one-operation" class="question-link" target="_blank">Maximum Subarray Sum After One Operation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Dynamic Programming", "Tree", "Depth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/choose-edges-to-maximize-score-in-a-tree" class="question-link" target="_blank">Choose Edges to Maximize Score in a Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 56.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-great-partitions" class="question-link" target="_blank">Number of Great Partitions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.3%</span>
                    <span>Acceptance: 32.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Stack", "Sorting", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/robot-collisions" class="question-link" target="_blank">Robot Collisions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Sorting</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 84.3%</span>
                    <span>Acceptance: 56.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/asteroid-collision" class="question-link" target="_blank">Asteroid Collision</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.7%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimize-the-difference-between-target-and-chosen-elements" class="question-link" target="_blank">Minimize the Difference Between Target and Chosen Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.4%</span>
                    <span>Acceptance: 35.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Greedy", "Bit Manipulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/apply-operations-on-array-to-maximize-sum-of-squares" class="question-link" target="_blank">Apply Operations on Array to Maximize Sum of Squares</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Greedy</span><span class="topic-badge">Bit Manipulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.4%</span>
                    <span>Acceptance: 45.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-number-of-food-buckets-to-feed-the-hamsters" class="question-link" target="_blank">Minimum Number of Food Buckets to Feed the Hamsters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.4%</span>
                    <span>Acceptance: 47.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.4%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Tree", "Depth-First Search", "Breadth-First Search", "Design", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/serialize-and-deserialize-binary-tree" class="question-link" target="_blank">Serialize and Deserialize Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.4%</span>
                    <span>Acceptance: 58.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-provinces" class="question-link" target="_blank">Number of Provinces</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.4%</span>
                    <span>Acceptance: 68.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-profit-in-job-scheduling" class="question-link" target="_blank">Maximum Profit in Job Scheduling</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 54.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Dynamic Programming", "Backtracking", "Bit Manipulation", "Number Theory", "Bitmask"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximize-score-after-n-operations" class="question-link" target="_blank">Maximize Score After N Operations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Number Theory</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 57.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Backtracking", "Enumeration"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/next-greater-numerically-balanced-number" class="question-link" target="_blank">Next Greater Numerically Balanced Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 49.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Two Pointers", "String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-labels" class="question-link" target="_blank">Partition Labels</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 80.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Divide and Conquer", "Binary Indexed Tree", "Segment Tree", "Merge Sort", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-of-smaller-numbers-after-self" class="question-link" target="_blank">Count of Smaller Numbers After Self</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Binary Indexed Tree</span><span class="topic-badge">Segment Tree</span><span class="topic-badge">Merge Sort</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 42.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "Dynamic Programming", "Tree", "Depth-First Search", "Number Theory"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-valid-paths-in-a-tree" class="question-link" target="_blank">Count Valid Paths in a Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Number Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 35.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/set-matrix-zeroes" class="question-link" target="_blank">Set Matrix Zeroes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 58.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/validate-ip-address" class="question-link" target="_blank">Validate IP Address</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 27.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Geometry"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-points-on-a-line" class="question-link" target="_blank">Max Points on a Line</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Geometry</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.7%</span>
                    <span>Acceptance: 28.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Stack", "Sorting", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/robot-collisions" class="question-link" target="_blank">Robot Collisions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Sorting</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.6%</span>
                    <span>Acceptance: 56.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.3%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-provinces" class="question-link" target="_blank">Number of Provinces</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.3%</span>
                    <span>Acceptance: 68.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimize-the-difference-between-target-and-chosen-elements" class="question-link" target="_blank">Minimize the Difference Between Target and Chosen Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.3%</span>
                    <span>Acceptance: 35.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Tree", "Depth-First Search", "Breadth-First Search", "Design", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/serialize-and-deserialize-binary-tree" class="question-link" target="_blank">Serialize and Deserialize Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.3%</span>
                    <span>Acceptance: 58.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-number-of-food-buckets-to-feed-the-hamsters" class="question-link" target="_blank">Minimum Number of Food Buckets to Feed the Hamsters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.3%</span>
                    <span>Acceptance: 47.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Counting", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-same-end-substrings" class="question-link" target="_blank">Number of Same-End Substrings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 61.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Dynamic Programming", "Tree", "Depth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/choose-edges-to-maximize-score-in-a-tree" class="question-link" target="_blank">Choose Edges to Maximize Score in a Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 56.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming", "Memoization"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-boxes" class="question-link" target="_blank">Remove Boxes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Memoization</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/single-element-in-a-sorted-array" class="question-link" target="_blank">Single Element in a Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 59.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Bit Manipulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/apply-bitwise-operations-to-make-strings-equal" class="question-link" target="_blank">Apply Bitwise Operations to Make Strings Equal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Bit Manipulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 41.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/first-missing-positive" class="question-link" target="_blank">First Missing Positive</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 40.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Rolling Hash", "String Matching", "Hash Function"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-to-revert-word-to-initial-state-i" class="question-link" target="_blank">Minimum Time to Revert Word to Initial State I</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Rolling Hash</span><span class="topic-badge">String Matching</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 41.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Tree", "Depth-First Search", "Enumeration"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/create-components-with-same-value" class="question-link" target="_blank">Create Components With Same Value</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 53.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/increasing-triplet-subsequence" class="question-link" target="_blank">Increasing Triplet Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 39.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray-sum-after-one-operation" class="question-link" target="_blank">Maximum Subarray Sum After One Operation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sum-of-matrix-after-queries" class="question-link" target="_blank">Sum of Matrix After Queries</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 31.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-univalue-path" class="question-link" target="_blank">Longest Univalue Path</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sort-colors" class="question-link" target="_blank">Sort Colors</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 66.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reach-a-number" class="question-link" target="_blank">Reach a Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 43.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-great-partitions" class="question-link" target="_blank">Number of Great Partitions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 32.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Rolling Hash", "String Matching", "Hash Function"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-to-revert-word-to-initial-state-ii" class="question-link" target="_blank">Minimum Time to Revert Word to Initial State II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Rolling Hash</span><span class="topic-badge">String Matching</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 35.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-the-number-of-powerful-integers" class="question-link" target="_blank">Count the Number of Powerful Integers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 26.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/russian-doll-envelopes" class="question-link" target="_blank">Russian Doll Envelopes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 37.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Bit Manipulation", "Sorting", "Bitmask"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-subsequence-sum" class="question-link" target="_blank">Closest Subsequence Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Sorting</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 41.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Dynamic Programming", "Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-valid-parentheses" class="question-link" target="_blank">Longest Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 35.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/container-with-most-water" class="question-link" target="_blank">Container With Most Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/largest-rectangle-in-histogram" class="question-link" target="_blank">Largest Rectangle in Histogram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 60.0%</span>
                    <span>Acceptance: 46.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-profit-in-job-scheduling" class="question-link" target="_blank">Maximum Profit in Job Scheduling</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 54.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Geometry"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-points-on-a-line" class="question-link" target="_blank">Max Points on a Line</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Geometry</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 28.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Two Pointers", "String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-labels" class="question-link" target="_blank">Partition Labels</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 80.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/set-matrix-zeroes" class="question-link" target="_blank">Set Matrix Zeroes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 58.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/validate-ip-address" class="question-link" target="_blank">Validate IP Address</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 27.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Divide and Conquer", "Binary Indexed Tree", "Segment Tree", "Merge Sort", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-of-smaller-numbers-after-self" class="question-link" target="_blank">Count of Smaller Numbers After Self</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Binary Indexed Tree</span><span class="topic-badge">Segment Tree</span><span class="topic-badge">Merge Sort</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 42.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "Dynamic Programming", "Tree", "Depth-First Search", "Number Theory"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-valid-paths-in-a-tree" class="question-link" target="_blank">Count Valid Paths in a Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Number Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 35.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reach-a-number" class="question-link" target="_blank">Reach a Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 43.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-univalue-path" class="question-link" target="_blank">Longest Univalue Path</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/first-missing-positive" class="question-link" target="_blank">First Missing Positive</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 40.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sort-colors" class="question-link" target="_blank">Sort Colors</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 66.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/largest-rectangle-in-histogram" class="question-link" target="_blank">Largest Rectangle in Histogram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 46.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/container-with-most-water" class="question-link" target="_blank">Container With Most Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sum-of-matrix-after-queries" class="question-link" target="_blank">Sum of Matrix After Queries</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 31.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/increasing-triplet-subsequence" class="question-link" target="_blank">Increasing Triplet Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 39.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Dynamic Programming", "Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-valid-parentheses" class="question-link" target="_blank">Longest Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 35.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/russian-doll-envelopes" class="question-link" target="_blank">Russian Doll Envelopes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 37.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/single-element-in-a-sorted-array" class="question-link" target="_blank">Single Element in a Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 59.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Dynamic Programming", "Memoization"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-boxes" class="question-link" target="_blank">Remove Boxes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Memoization</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.8%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const questionCards = document.querySelectorAll('.question-card');
        const difficultyButtons = document.querySelectorAll('.filter-btn');
        const topicTags = document.querySelectorAll('.topic-tag');
        
        function filterQuestions() {
            const searchTerm = searchInput.value.toLowerCase();
            const activeDifficulties = Array.from(difficultyButtons)
                .filter(btn => btn.classList.contains('active'))
                .map(btn => btn.dataset.difficulty);
            const activeTopics = Array.from(topicTags)
                .filter(tag => tag.classList.contains('active'))
                .map(tag => tag.dataset.topic);
            
            questionCards.forEach(card => {
                const title = card.querySelector('.question-title').textContent.toLowerCase();
                const difficulty = card.dataset.difficulty;
                const topics = JSON.parse(card.dataset.topics);
                
                const matchesSearch = title.includes(searchTerm);
                const matchesDifficulty = activeDifficulties.includes(difficulty);
                const matchesTopics = activeTopics.length === 0 || 
                    topics.some(topic => activeTopics.includes(topic));
                
                if (matchesSearch && matchesDifficulty && matchesTopics) {
                    card.style.display = 'flex';
                    card.style.opacity = '1';
                } else {
                    card.style.display = 'none';
                    card.style.opacity = '0';
                }
            });
        }
        
        // Add smooth transitions for filtering
        questionCards.forEach(card => {
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        });
        
        searchInput.addEventListener('input', filterQuestions);
        
        difficultyButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                btn.classList.toggle('active');
                filterQuestions();
            });
        });
        
        topicTags.forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('active');
                filterQuestions();
            });
        });

        // Add smooth scrolling for all links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
