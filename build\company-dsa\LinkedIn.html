
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn DSA Questions</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #818cf8;
            --secondary: #f1f5f9;
            --easy: #22c55e;
            --medium: #f59e0b;
            --hard: #ef4444;
            --text: #0f172a;
            --text-light: #64748b;
            --card-bg: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--secondary);
            color: var(--text);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        h1 {
            color: var(--primary-dark);
            text-align: center;
            margin: 40px 0;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
            }
            to {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
            }
        }

        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 20px;
            border: 2px solid var(--primary-light);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: var(--card-bg);
            color: var(--text);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .difficulty-filter {
            display: flex;
            gap: 12px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            opacity: 0.8;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            opacity: 1;
        }

        .filter-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: var(--shadow);
        }

        .filter-btn.easy {
            background-color: var(--easy);
            color: white;
        }

        .filter-btn.medium {
            background-color: var(--medium);
            color: white;
        }

        .filter-btn.hard {
            background-color: var(--hard);
            color: white;
        }

        .topics-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 20px 0 30px;
        }

        .topic-tag {
            padding: 8px 16px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.8;
            transition: all 0.3s ease;
            user-select: none;
        }

        .topic-tag:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        .topic-tag.active {
            opacity: 1;
            background-color: var(--primary-dark);
            box-shadow: var(--shadow);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 16px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .difficulty {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .difficulty-easy { background-color: var(--easy); color: white; }
        .difficulty-medium { background-color: var(--medium); color: white; }
        .difficulty-hard { background-color: var(--hard); color: white; }

        .question-title {
            font-size: 1.2em;
            margin: 10px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        .question-link {
            color: var(--primary-dark);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .question-link:hover {
            color: var(--primary);
            text-decoration: underline;
        }

        .question-stats {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            font-size: 0.95em;
            color: var(--text-light);
            padding-top: 15px;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
        }

        .topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .topic-badge:hover {
            transform: translateY(-2px);
            background-color: var(--primary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            h1 {
                font-size: 2rem;
                margin: 30px 0;
            }

            .filters {
                flex-direction: column;
                gap: 15px;
            }

            .difficulty-filter {
                justify-content: center;
                width: 100%;
            }

            .search-box {
                width: 100%;
            }

            .stat-card {
                padding: 20px;
            }

            .question-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 2em;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 1.75rem;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .topic-tag {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .question-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling and selection styles */
        html {
            scroll-behavior: smooth;
        }

        ::selection {
            background-color: var(--primary-light);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LinkedIn DSA Questions</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">370</div>
                <div>Total Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">70</div>
                <div>Easy Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">248</div>
                <div>Medium Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">52</div>
                <div>Hard Questions</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" placeholder="Search questions..." id="searchInput">
            <div class="difficulty-filter">
                <button class="filter-btn easy active" data-difficulty="EASY">Easy</button>
                <button class="filter-btn medium active" data-difficulty="MEDIUM">Medium</button>
                <button class="filter-btn hard active" data-difficulty="HARD">Hard</button>
            </div>
        </div>

        <div class="topics-filter">
            <div class="topic-tag" data-topic="Array">Array</div><div class="topic-tag" data-topic="Backtracking">Backtracking</div><div class="topic-tag" data-topic="Binary Search">Binary Search</div><div class="topic-tag" data-topic="Binary Search Tree">Binary Search Tree</div><div class="topic-tag" data-topic="Binary Tree">Binary Tree</div><div class="topic-tag" data-topic="Bit Manipulation">Bit Manipulation</div><div class="topic-tag" data-topic="Bitmask">Bitmask</div><div class="topic-tag" data-topic="Brainteaser">Brainteaser</div><div class="topic-tag" data-topic="Breadth-First Search">Breadth-First Search</div><div class="topic-tag" data-topic="Combinatorics">Combinatorics</div><div class="topic-tag" data-topic="Concurrency">Concurrency</div><div class="topic-tag" data-topic="Counting Sort">Counting Sort</div><div class="topic-tag" data-topic="Data Stream">Data Stream</div><div class="topic-tag" data-topic="Depth-First Search">Depth-First Search</div><div class="topic-tag" data-topic="Design">Design</div><div class="topic-tag" data-topic="Divide and Conquer">Divide and Conquer</div><div class="topic-tag" data-topic="Doubly-Linked List">Doubly-Linked List</div><div class="topic-tag" data-topic="Dynamic Programming">Dynamic Programming</div><div class="topic-tag" data-topic="Game Theory">Game Theory</div><div class="topic-tag" data-topic="Geometry">Geometry</div><div class="topic-tag" data-topic="Graph">Graph</div><div class="topic-tag" data-topic="Greedy">Greedy</div><div class="topic-tag" data-topic="Hash Function">Hash Function</div><div class="topic-tag" data-topic="Hash Table">Hash Table</div><div class="topic-tag" data-topic="Heap (Priority Queue)">Heap (Priority Queue)</div><div class="topic-tag" data-topic="Interactive">Interactive</div><div class="topic-tag" data-topic="Iterator">Iterator</div><div class="topic-tag" data-topic="Linked List">Linked List</div><div class="topic-tag" data-topic="Math">Math</div><div class="topic-tag" data-topic="Matrix">Matrix</div><div class="topic-tag" data-topic="Memoization">Memoization</div><div class="topic-tag" data-topic="Merge Sort">Merge Sort</div><div class="topic-tag" data-topic="Number Theory">Number Theory</div><div class="topic-tag" data-topic="Ordered Set">Ordered Set</div><div class="topic-tag" data-topic="Prefix Sum">Prefix Sum</div><div class="topic-tag" data-topic="Probability and Statistics">Probability and Statistics</div><div class="topic-tag" data-topic="Queue">Queue</div><div class="topic-tag" data-topic="Quickselect">Quickselect</div><div class="topic-tag" data-topic="Randomized">Randomized</div><div class="topic-tag" data-topic="Recursion">Recursion</div><div class="topic-tag" data-topic="Rejection Sampling">Rejection Sampling</div><div class="topic-tag" data-topic="Rolling Hash">Rolling Hash</div><div class="topic-tag" data-topic="Segment Tree">Segment Tree</div><div class="topic-tag" data-topic="Simulation">Simulation</div><div class="topic-tag" data-topic="Sliding Window">Sliding Window</div><div class="topic-tag" data-topic="Sorting">Sorting</div><div class="topic-tag" data-topic="Stack">Stack</div><div class="topic-tag" data-topic="String">String</div><div class="topic-tag" data-topic="Topological Sort">Topological Sort</div><div class="topic-tag" data-topic="Tree">Tree</div><div class="topic-tag" data-topic="Trie">Trie</div><div class="topic-tag" data-topic="Two Pointers">Two Pointers</div><div class="topic-tag" data-topic="Union Find">Union Find</div>
        </div>

        <div class="question-grid">

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum-ii" class="question-link" target="_blank">Nested List Weight Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 64.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/all-oone-data-structure" class="question-link" target="_blank">All O`one Data Structure</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 44.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Stack", "Design", "Doubly-Linked List", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-stack" class="question-link" target="_blank">Max Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Stack</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 45.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/all-oone-data-structure" class="question-link" target="_blank">All O`one Data Structure</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 99.5%</span>
                    <span>Acceptance: 44.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum-ii" class="question-link" target="_blank">Nested List Weight Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 97.8%</span>
                    <span>Acceptance: 64.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum-ii" class="question-link" target="_blank">Nested List Weight Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 97.7%</span>
                    <span>Acceptance: 64.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum-ii" class="question-link" target="_blank">Nested List Weight Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 96.6%</span>
                    <span>Acceptance: 64.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Stack", "Design", "Doubly-Linked List", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-stack" class="question-link" target="_blank">Max Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Stack</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 94.6%</span>
                    <span>Acceptance: 45.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/all-oone-data-structure" class="question-link" target="_blank">All O`one Data Structure</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 94.6%</span>
                    <span>Acceptance: 44.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum-ii" class="question-link" target="_blank">Nested List Weight Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 92.2%</span>
                    <span>Acceptance: 64.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/all-oone-data-structure" class="question-link" target="_blank">All O`one Data Structure</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 92.2%</span>
                    <span>Acceptance: 44.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Stack", "Design", "Doubly-Linked List", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-stack" class="question-link" target="_blank">Max Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Stack</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.5%</span>
                    <span>Acceptance: 45.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.9%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum" class="question-link" target="_blank">Nested List Weight Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.9%</span>
                    <span>Acceptance: 85.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Sliding Window", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-consecutive-ones-iii" class="question-link" target="_blank">Max Consecutive Ones III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.0%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 86.0%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 85.2%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-authentication-manager" class="question-link" target="_blank">Design Authentication Manager</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 84.6%</span>
                    <span>Acceptance: 58.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Sliding Window", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-consecutive-ones-iii" class="question-link" target="_blank">Max Consecutive Ones III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 83.2%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Sliding Window", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-consecutive-ones-iii" class="question-link" target="_blank">Max Consecutive Ones III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 82.5%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "Graph", "Interactive"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-the-celebrity" class="question-link" target="_blank">Find the Celebrity</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Graph</span><span class="topic-badge">Interactive</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 80.7%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 79.9%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum" class="question-link" target="_blank">Nested List Weight Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 79.8%</span>
                    <span>Acceptance: 85.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 79.6%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "String", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance-ii" class="question-link" target="_blank">Shortest Word Distance II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 79.1%</span>
                    <span>Acceptance: 61.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 79.1%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.6%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum" class="question-link" target="_blank">Nested List Weight Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.5%</span>
                    <span>Acceptance: 85.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder" class="question-link" target="_blank">Word Ladder</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.5%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.3%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "String", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance-ii" class="question-link" target="_blank">Shortest Word Distance II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.0%</span>
                    <span>Acceptance: 61.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-leaves-of-binary-tree" class="question-link" target="_blank">Find Leaves of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 77.9%</span>
                    <span>Acceptance: 81.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "String", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance-ii" class="question-link" target="_blank">Shortest Word Distance II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 77.3%</span>
                    <span>Acceptance: 61.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum" class="question-link" target="_blank">Nested List Weight Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 77.3%</span>
                    <span>Acceptance: 85.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "Graph", "Interactive"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-the-celebrity" class="question-link" target="_blank">Find the Celebrity</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Graph</span><span class="topic-badge">Interactive</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 77.3%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.1%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.0%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.0%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-leaves-of-binary-tree" class="question-link" target="_blank">Find Leaves of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 75.9%</span>
                    <span>Acceptance: 81.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder" class="question-link" target="_blank">Word Ladder</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 75.9%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "Graph", "Interactive"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-the-celebrity" class="question-link" target="_blank">Find the Celebrity</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Graph</span><span class="topic-badge">Interactive</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 75.6%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-product-subarray" class="question-link" target="_blank">Maximum Product Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 75.4%</span>
                    <span>Acceptance: 34.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.4%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-place-flowers" class="question-link" target="_blank">Can Place Flowers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.0%</span>
                    <span>Acceptance: 28.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder" class="question-link" target="_blank">Word Ladder</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.7%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-place-flowers" class="question-link" target="_blank">Can Place Flowers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.7%</span>
                    <span>Acceptance: 28.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "Graph", "Interactive"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-the-celebrity" class="question-link" target="_blank">Find the Celebrity</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Graph</span><span class="topic-badge">Interactive</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.7%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.2%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 72.7%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-leaves-of-binary-tree" class="question-link" target="_blank">Find Leaves of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 71.2%</span>
                    <span>Acceptance: 81.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 71.0%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "String", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance-ii" class="question-link" target="_blank">Shortest Word Distance II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 71.0%</span>
                    <span>Acceptance: 61.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Tree", "Depth-First Search", "Breadth-First Search", "Design", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/serialize-and-deserialize-binary-tree" class="question-link" target="_blank">Serialize and Deserialize Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 71.0%</span>
                    <span>Acceptance: 58.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder" class="question-link" target="_blank">Word Ladder</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 71.0%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array" class="question-link" target="_blank">Find First and Last Position of Element in Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.9%</span>
                    <span>Acceptance: 46.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-place-flowers" class="question-link" target="_blank">Can Place Flowers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.9%</span>
                    <span>Acceptance: 28.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-upside-down" class="question-link" target="_blank">Binary Tree Upside Down</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.9%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array" class="question-link" target="_blank">Find First and Last Position of Element in Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.9%</span>
                    <span>Acceptance: 46.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder" class="question-link" target="_blank">Word Ladder</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.6%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "String", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance-ii" class="question-link" target="_blank">Shortest Word Distance II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.6%</span>
                    <span>Acceptance: 61.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "Graph", "Interactive"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-the-celebrity" class="question-link" target="_blank">Find the Celebrity</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Graph</span><span class="topic-badge">Interactive</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.6%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-place-flowers" class="question-link" target="_blank">Can Place Flowers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.6%</span>
                    <span>Acceptance: 28.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-area-of-island" class="question-link" target="_blank">Max Area of Island</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.6%</span>
                    <span>Acceptance: 72.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-subarray" class="question-link" target="_blank">Maximum Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.6%</span>
                    <span>Acceptance: 51.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Concurrency"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/building-h2o" class="question-link" target="_blank">Building H2O</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Concurrency</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.2%</span>
                    <span>Acceptance: 57.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-leaves-of-binary-tree" class="question-link" target="_blank">Find Leaves of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.2%</span>
                    <span>Acceptance: 81.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-intervals" class="question-link" target="_blank">Merge Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.2%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/nested-list-weight-sum" class="question-link" target="_blank">Nested List Weight Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.2%</span>
                    <span>Acceptance: 85.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.2%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-largest-element-in-an-array" class="question-link" target="_blank">Kth Largest Element in an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.2%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-product-subarray" class="question-link" target="_blank">Maximum Product Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.2%</span>
                    <span>Acceptance: 34.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Tree", "Depth-First Search", "Breadth-First Search", "Design", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/serialize-and-deserialize-binary-tree" class="question-link" target="_blank">Serialize and Deserialize Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.0%</span>
                    <span>Acceptance: 58.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/paint-house" class="question-link" target="_blank">Paint House</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 69.0%</span>
                    <span>Acceptance: 63.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-product-subarray" class="question-link" target="_blank">Maximum Product Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.9%</span>
                    <span>Acceptance: 34.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.9%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.1%</span>
                    <span>Acceptance: 65.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-intervals" class="question-link" target="_blank">Merge Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.1%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance" class="question-link" target="_blank">Shortest Word Distance</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 67.0%</span>
                    <span>Acceptance: 65.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-search-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.8%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/factor-combinations" class="question-link" target="_blank">Factor Combinations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.0%</span>
                    <span>Acceptance: 50.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-depth-of-binary-tree" class="question-link" target="_blank">Maximum Depth of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.0%</span>
                    <span>Acceptance: 76.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-window-substring" class="question-link" target="_blank">Minimum Window Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.0%</span>
                    <span>Acceptance: 44.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-area-of-island" class="question-link" target="_blank">Max Area of Island</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.1%</span>
                    <span>Acceptance: 72.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.1%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/paint-house" class="question-link" target="_blank">Paint House</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.1%</span>
                    <span>Acceptance: 63.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Backtracking", "Bit Manipulation", "Memoization", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-to-k-equal-sum-subsets" class="question-link" target="_blank">Partition to K Equal Sum Subsets</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.1%</span>
                    <span>Acceptance: 38.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.1%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Prefix Sum"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-pivot-index" class="question-link" target="_blank">Find Pivot Index</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 65.0%</span>
                    <span>Acceptance: 59.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/isomorphic-strings" class="question-link" target="_blank">Isomorphic Strings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.9%</span>
                    <span>Acceptance: 46.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-divisible-triplet-sums" class="question-link" target="_blank">Number of Divisible Triplet Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.7%</span>
                    <span>Acceptance: 66.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/letter-combinations-of-a-phone-number" class="question-link" target="_blank">Letter Combinations of a Phone Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.7%</span>
                    <span>Acceptance: 63.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.7%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/letter-combinations-of-a-phone-number" class="question-link" target="_blank">Letter Combinations of a Phone Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.3%</span>
                    <span>Acceptance: 63.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Backtracking", "Bit Manipulation", "Memoization", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-to-k-equal-sum-subsets" class="question-link" target="_blank">Partition to K Equal Sum Subsets</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.3%</span>
                    <span>Acceptance: 38.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-upside-down" class="question-link" target="_blank">Binary Tree Upside Down</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 64.3%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/powx-n" class="question-link" target="_blank">Pow(x, n)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.7%</span>
                    <span>Acceptance: 36.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-largest-element-in-an-array" class="question-link" target="_blank">Kth Largest Element in an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.7%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-perfect-square" class="question-link" target="_blank">Valid Perfect Square</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.7%</span>
                    <span>Acceptance: 44.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.7%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/second-minimum-node-in-a-binary-tree" class="question-link" target="_blank">Second Minimum Node In a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.7%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-search-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.7%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Backtracking", "Bit Manipulation", "Memoization", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-to-k-equal-sum-subsets" class="question-link" target="_blank">Partition to K Equal Sum Subsets</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.5%</span>
                    <span>Acceptance: 38.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-place-flowers" class="question-link" target="_blank">Can Place Flowers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.5%</span>
                    <span>Acceptance: 28.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-depth-of-binary-tree" class="question-link" target="_blank">Maximum Depth of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.5%</span>
                    <span>Acceptance: 76.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Geometry"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-points-on-a-line" class="question-link" target="_blank">Max Points on a Line</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Geometry</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.4%</span>
                    <span>Acceptance: 28.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-number" class="question-link" target="_blank">Valid Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.4%</span>
                    <span>Acceptance: 21.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/evaluate-reverse-polish-notation" class="question-link" target="_blank">Evaluate Reverse Polish Notation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.4%</span>
                    <span>Acceptance: 54.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Dynamic Programming", "Bit Manipulation", "Memoization", "Game Theory", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-i-win" class="question-link" target="_blank">Can I Win</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Game Theory</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.4%</span>
                    <span>Acceptance: 30.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/symmetric-tree" class="question-link" target="_blank">Symmetric Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 62.4%</span>
                    <span>Acceptance: 58.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/search-in-rotated-sorted-array" class="question-link" target="_blank">Search in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.6%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/paint-house" class="question-link" target="_blank">Paint House</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.6%</span>
                    <span>Acceptance: 63.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1-duplicates-allowed" class="question-link" target="_blank">Insert Delete GetRandom O(1) - Duplicates allowed</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.6%</span>
                    <span>Acceptance: 35.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sqrtx" class="question-link" target="_blank">Sqrt(x)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.3%</span>
                    <span>Acceptance: 40.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/letter-combinations-of-a-phone-number" class="question-link" target="_blank">Letter Combinations of a Phone Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.3%</span>
                    <span>Acceptance: 63.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/second-minimum-node-in-a-binary-tree" class="question-link" target="_blank">Second Minimum Node In a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.3%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber" class="question-link" target="_blank">House Robber</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.0%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/letter-combinations-of-a-phone-number" class="question-link" target="_blank">Letter Combinations of a Phone Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.0%</span>
                    <span>Acceptance: 63.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-smallest-product-of-two-sorted-arrays" class="question-link" target="_blank">Kth Smallest Product of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.0%</span>
                    <span>Acceptance: 30.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-level-order-traversal" class="question-link" target="_blank">Binary Tree Level Order Traversal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.0%</span>
                    <span>Acceptance: 69.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/exclusive-time-of-functions" class="question-link" target="_blank">Exclusive Time of Functions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.7%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.7%</span>
                    <span>Acceptance: 65.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "Stack", "Tree", "Depth-First Search", "Binary Search Tree", "Heap (Priority Queue)", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-binary-search-tree-value-ii" class="question-link" target="_blank">Closest Binary Search Tree Value II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.7%</span>
                    <span>Acceptance: 60.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Geometry"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-points-on-a-line" class="question-link" target="_blank">Max Points on a Line</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Geometry</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.7%</span>
                    <span>Acceptance: 28.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-search-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.7%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.7%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-two-sorted-lists" class="question-link" target="_blank">Merge Two Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.6%</span>
                    <span>Acceptance: 66.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.6%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/palindromic-substrings" class="question-link" target="_blank">Palindromic Substrings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.6%</span>
                    <span>Acceptance: 71.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.6%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/permutations" class="question-link" target="_blank">Permutations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.6%</span>
                    <span>Acceptance: 80.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Design", "Segment Tree", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-integers-in-intervals" class="question-link" target="_blank">Count Integers in Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Design</span><span class="topic-badge">Segment Tree</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.6%</span>
                    <span>Acceptance: 35.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Bit Manipulation", "Sliding Window", "Rolling Hash", "Hash Function"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/repeated-dna-sequences" class="question-link" target="_blank">Repeated DNA Sequences</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Rolling Hash</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.6%</span>
                    <span>Acceptance: 50.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/permutations-ii" class="question-link" target="_blank">Permutations II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 59.6%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/second-minimum-node-in-a-binary-tree" class="question-link" target="_blank">Second Minimum Node In a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance" class="question-link" target="_blank">Shortest Word Distance</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 65.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-leaves-of-binary-tree" class="question-link" target="_blank">Find Leaves of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 81.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Brainteaser"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/bulb-switcher" class="question-link" target="_blank">Bulb Switcher</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Brainteaser</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 53.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Backtracking", "Bit Manipulation", "Memoization", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-to-k-equal-sum-subsets" class="question-link" target="_blank">Partition to K Equal Sum Subsets</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 38.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-largest-element-in-an-array" class="question-link" target="_blank">Kth Largest Element in an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-area-of-island" class="question-link" target="_blank">Max Area of Island</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 72.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sqrtx" class="question-link" target="_blank">Sqrt(x)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 40.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/exclusive-time-of-functions" class="question-link" target="_blank">Exclusive Time of Functions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Two Pointers", "Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-tree-iii" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Tree III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.4%</span>
                    <span>Acceptance: 81.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-zigzag-level-order-traversal" class="question-link" target="_blank">Binary Tree Zigzag Level Order Traversal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.0%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sparse-matrix-multiplication" class="question-link" target="_blank">Sparse Matrix Multiplication</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 58.0%</span>
                    <span>Acceptance: 68.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-divisible-triplet-sums" class="question-link" target="_blank">Number of Divisible Triplet Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.6%</span>
                    <span>Acceptance: 66.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-upside-down" class="question-link" target="_blank">Binary Tree Upside Down</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.6%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-subsequence" class="question-link" target="_blank">Longest Palindromic Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.6%</span>
                    <span>Acceptance: 63.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1-duplicates-allowed" class="question-link" target="_blank">Insert Delete GetRandom O(1) - Duplicates allowed</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.6%</span>
                    <span>Acceptance: 35.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance" class="question-link" target="_blank">Shortest Word Distance</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.6%</span>
                    <span>Acceptance: 65.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-window-substring" class="question-link" target="_blank">Minimum Window Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 44.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/letter-combinations-of-a-phone-number" class="question-link" target="_blank">Letter Combinations of a Phone Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 63.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-perfect-square" class="question-link" target="_blank">Valid Perfect Square</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 44.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-upside-down" class="question-link" target="_blank">Binary Tree Upside Down</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-number" class="question-link" target="_blank">Valid Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 21.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/paint-house" class="question-link" target="_blank">Paint House</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 63.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/isomorphic-strings" class="question-link" target="_blank">Isomorphic Strings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 46.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Binary Search", "Prefix Sum", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/random-pick-with-weight" class="question-link" target="_blank">Random Pick with Weight</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Prefix Sum</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/evaluate-reverse-polish-notation" class="question-link" target="_blank">Evaluate Reverse Polish Notation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 54.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber" class="question-link" target="_blank">House Robber</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.5%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Divide and Conquer", "Heap (Priority Queue)", "Merge Sort"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-k-sorted-lists" class="question-link" target="_blank">Merge k Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Merge Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.3%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance-iii" class="question-link" target="_blank">Shortest Word Distance III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 56.3%</span>
                    <span>Acceptance: 58.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/factor-combinations" class="question-link" target="_blank">Factor Combinations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.7%</span>
                    <span>Acceptance: 50.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sqrtx" class="question-link" target="_blank">Sqrt(x)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.7%</span>
                    <span>Acceptance: 40.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Depth-First Search", "Design", "Queue", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-nested-list-iterator" class="question-link" target="_blank">Flatten Nested List Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.7%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.7%</span>
                    <span>Acceptance: 65.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-largest-element-in-an-array" class="question-link" target="_blank">Kth Largest Element in an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.7%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/possible-bipartition" class="question-link" target="_blank">Possible Bipartition</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.7%</span>
                    <span>Acceptance: 51.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Backtracking", "Interactive"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/robot-room-cleaner" class="question-link" target="_blank">Robot Room Cleaner</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Backtracking</span><span class="topic-badge">Interactive</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.7%</span>
                    <span>Acceptance: 77.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/create-binary-tree-from-descriptions" class="question-link" target="_blank">Create Binary Tree From Descriptions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 81.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/text-justification" class="question-link" target="_blank">Text Justification</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 47.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/product-of-array-except-self" class="question-link" target="_blank">Product of Array Except Self</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Design", "Binary Search Tree", "Binary Tree", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-search-tree-iterator" class="question-link" target="_blank">Binary Search Tree Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 74.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1-duplicates-allowed" class="question-link" target="_blank">Insert Delete GetRandom O(1) - Duplicates allowed</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 35.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Sorting", "Heap (Priority Queue)", "Number Theory"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-deletions-to-make-array-divisible" class="question-link" target="_blank">Minimum Deletions to Make Array Divisible</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Number Theory</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 57.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "Stack", "Tree", "Depth-First Search", "Binary Search Tree", "Heap (Priority Queue)", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-binary-search-tree-value-ii" class="question-link" target="_blank">Closest Binary Search Tree Value II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 60.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Two Pointers", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sum-of-square-numbers" class="question-link" target="_blank">Sum of Square Numbers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/exclusive-time-of-functions" class="question-link" target="_blank">Exclusive Time of Functions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "String", "Dynamic Programming", "Greedy"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-non-overlapping-palindrome-substrings" class="question-link" target="_blank">Maximum Number of Non-overlapping Palindrome Substrings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 41.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximize-total-tastiness-of-purchased-fruits" class="question-link" target="_blank">Maximize Total Tastiness of Purchased Fruits</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 64.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-interval" class="question-link" target="_blank">Insert Interval</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Two Pointers", "Design", "Data Stream"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum-iii-data-structure-design" class="question-link" target="_blank">Two Sum III - Data structure design</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Design</span><span class="topic-badge">Data Stream</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 38.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Heap (Priority Queue)", "Simulation"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/time-to-cross-a-bridge" class="question-link" target="_blank">Time to Cross a Bridge</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 43.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-largest-value-in-each-tree-row" class="question-link" target="_blank">Find Largest Value in Each Tree Row</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 54.4%</span>
                    <span>Acceptance: 66.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-level-order-traversal" class="question-link" target="_blank">Binary Tree Level Order Traversal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 69.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/factor-combinations" class="question-link" target="_blank">Factor Combinations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 50.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-triangle-number" class="question-link" target="_blank">Valid Triangle Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-sorted-array" class="question-link" target="_blank">Merge Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 52.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/graph-valid-tree" class="question-link" target="_blank">Graph Valid Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 49.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Sliding Window", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-consecutive-ones-iii" class="question-link" target="_blank">Max Consecutive Ones III</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Sliding Window", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-closest-elements" class="question-link" target="_blank">Find K Closest Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 48.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Concurrency"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-bounded-blocking-queue" class="question-link" target="_blank">Design Bounded Blocking Queue</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Concurrency</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 72.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-subsequence" class="question-link" target="_blank">Longest Palindromic Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 63.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-substring" class="question-link" target="_blank">Longest Palindromic Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 35.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shortest-word-distance" class="question-link" target="_blank">Shortest Word Distance</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.7%</span>
                    <span>Acceptance: 65.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-area-of-island" class="question-link" target="_blank">Max Area of Island</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.3%</span>
                    <span>Acceptance: 72.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-subsequence" class="question-link" target="_blank">Longest Palindromic Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 52.3%</span>
                    <span>Acceptance: 63.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Brainteaser"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/bulb-switcher" class="question-link" target="_blank">Bulb Switcher</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Brainteaser</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 53.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/powx-n" class="question-link" target="_blank">Pow(x, n)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 36.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-largest-element-in-an-array" class="question-link" target="_blank">Kth Largest Element in an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-window-substring" class="question-link" target="_blank">Minimum Window Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 44.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-pairs-with-smallest-sums" class="question-link" target="_blank">Find K Pairs with Smallest Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 40.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Dynamic Programming", "Bit Manipulation", "Memoization", "Game Theory", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-i-win" class="question-link" target="_blank">Can I Win</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Game Theory</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 30.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-list" class="question-link" target="_blank">Rotate List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 39.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-connected-components-in-an-undirected-graph" class="question-link" target="_blank">Number of Connected Components in an Undirected Graph</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 63.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 65.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-search-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/exclusive-time-of-functions" class="question-link" target="_blank">Exclusive Time of Functions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array" class="question-link" target="_blank">Find First and Last Position of Element in Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 46.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/possible-bipartition" class="question-link" target="_blank">Possible Bipartition</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.5%</span>
                    <span>Acceptance: 51.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-window-substring" class="question-link" target="_blank">Minimum Window Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 44.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sort-transformed-array" class="question-link" target="_blank">Sort Transformed Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-leaf-in-a-binary-tree" class="question-link" target="_blank">Closest Leaf in a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 46.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-connected-components-in-an-undirected-graph" class="question-link" target="_blank">Number of Connected Components in an Undirected Graph</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 63.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-divisible-triplet-sums" class="question-link" target="_blank">Number of Divisible Triplet Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 66.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-subsequence" class="question-link" target="_blank">Longest Palindromic Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 63.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-pairs-with-smallest-sums" class="question-link" target="_blank">Find K Pairs with Smallest Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 40.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/powx-n" class="question-link" target="_blank">Pow(x, n)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 36.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Brainteaser"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/bulb-switcher" class="question-link" target="_blank">Bulb Switcher</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Brainteaser</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.1%</span>
                    <span>Acceptance: 53.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Depth-First Search", "Design", "Queue", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-nested-list-iterator" class="question-link" target="_blank">Flatten Nested List Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 50.0%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/paint-house" class="question-link" target="_blank">Paint House</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 63.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Breadth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/01-matrix" class="question-link" target="_blank">01 Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 50.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-upside-down" class="question-link" target="_blank">Binary Tree Upside Down</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/second-minimum-node-in-a-binary-tree" class="question-link" target="_blank">Second Minimum Node In a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/powx-n" class="question-link" target="_blank">Pow(x, n)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 36.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/delete-node-in-a-bst" class="question-link" target="_blank">Delete Node in a BST</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 52.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-smallest-product-of-two-sorted-arrays" class="question-link" target="_blank">Kth Smallest Product of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 30.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-smallest-letter-greater-than-target" class="question-link" target="_blank">Find Smallest Letter Greater Than Target</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 53.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Dynamic Programming", "Bit Manipulation", "Memoization", "Game Theory", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-i-win" class="question-link" target="_blank">Can I Win</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Game Theory</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 30.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-search-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "Stack", "Tree", "Depth-First Search", "Binary Search Tree", "Heap (Priority Queue)", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-binary-search-tree-value-ii" class="question-link" target="_blank">Closest Binary Search Tree Value II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 60.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-list" class="question-link" target="_blank">Rotate List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 39.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-connected-components-in-an-undirected-graph" class="question-link" target="_blank">Number of Connected Components in an Undirected Graph</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 49.6%</span>
                    <span>Acceptance: 63.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Two Pointers", "Binary Search", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/intersection-of-two-arrays" class="question-link" target="_blank">Intersection of Two Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 76.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Bit Manipulation", "Sliding Window", "Rolling Hash", "Hash Function"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/repeated-dna-sequences" class="question-link" target="_blank">Repeated DNA Sequences</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Rolling Hash</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 50.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "String", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/robot-bounded-in-circle" class="question-link" target="_blank">Robot Bounded In Circle</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 56.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/palindromic-substrings" class="question-link" target="_blank">Palindromic Substrings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 71.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/edit-distance" class="question-link" target="_blank">Edit Distance</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 58.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-tree-zigzag-level-order-traversal" class="question-link" target="_blank">Binary Tree Zigzag Level Order Traversal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 61.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/powx-n" class="question-link" target="_blank">Pow(x, n)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 36.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Linked List", "Design", "Hash Function"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-hashmap" class="question-link" target="_blank">Design HashMap</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Hash Function</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 65.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Design", "Segment Tree", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-integers-in-intervals" class="question-link" target="_blank">Count Integers in Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Design</span><span class="topic-badge">Segment Tree</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 35.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "String", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/fizz-buzz" class="question-link" target="_blank">Fizz Buzz</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 73.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Dynamic Programming", "Bit Manipulation", "Memoization", "Game Theory", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-i-win" class="question-link" target="_blank">Can I Win</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Game Theory</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 30.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-list" class="question-link" target="_blank">Rotate List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 39.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/symmetric-tree" class="question-link" target="_blank">Symmetric Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 48.1%</span>
                    <span>Acceptance: 58.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-list" class="question-link" target="_blank">Rotate List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.4%</span>
                    <span>Acceptance: 39.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-triangle-number" class="question-link" target="_blank">Valid Triangle Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.4%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-pairs-with-smallest-sums" class="question-link" target="_blank">Find K Pairs with Smallest Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.4%</span>
                    <span>Acceptance: 40.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Sliding Window", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-closest-elements" class="question-link" target="_blank">Find K Closest Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.4%</span>
                    <span>Acceptance: 48.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/graph-valid-tree" class="question-link" target="_blank">Graph Valid Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.4%</span>
                    <span>Acceptance: 49.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-sorted-array" class="question-link" target="_blank">Merge Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.4%</span>
                    <span>Acceptance: 52.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-connected-components-in-an-undirected-graph" class="question-link" target="_blank">Number of Connected Components in an Undirected Graph</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.4%</span>
                    <span>Acceptance: 63.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sort-transformed-array" class="question-link" target="_blank">Sort Transformed Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.4%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/combination-sum-ii" class="question-link" target="_blank">Combination Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Dynamic Programming", "Combinatorics"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/unique-paths" class="question-link" target="_blank">Unique Paths</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Combinatorics</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 65.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/exclusive-time-of-functions" class="question-link" target="_blank">Exclusive Time of Functions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/symmetric-tree" class="question-link" target="_blank">Symmetric Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 58.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "Stack", "Tree", "Depth-First Search", "Binary Search Tree", "Heap (Priority Queue)", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-binary-search-tree-value-ii" class="question-link" target="_blank">Closest Binary Search Tree Value II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 60.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-smallest-letter-greater-than-target" class="question-link" target="_blank">Find Smallest Letter Greater Than Target</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 53.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shuffle-an-array" class="question-link" target="_blank">Shuffle an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 58.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rotate-list" class="question-link" target="_blank">Rotate List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 39.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/isomorphic-strings" class="question-link" target="_blank">Isomorphic Strings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 46.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Rejection Sampling", "Randomized", "Probability and Statistics"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/implement-rand10-using-rand7" class="question-link" target="_blank">Implement Rand10() Using Rand7()</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Rejection Sampling</span><span class="topic-badge">Randomized</span><span class="topic-badge">Probability and Statistics</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 45.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/course-schedule-ii" class="question-link" target="_blank">Course Schedule II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 52.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-smallest-product-of-two-sorted-arrays" class="question-link" target="_blank">Kth Smallest Product of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 30.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "Dynamic Programming", "Bit Manipulation", "Memoization", "Game Theory", "Bitmask"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/can-i-win" class="question-link" target="_blank">Can I Win</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Memoization</span><span class="topic-badge">Game Theory</span><span class="topic-badge">Bitmask</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 30.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/koko-eating-bananas" class="question-link" target="_blank">Koko Eating Bananas</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 44.2%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/diameter-of-binary-tree" class="question-link" target="_blank">Diameter of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 62.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-two-sorted-lists" class="question-link" target="_blank">Merge Two Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 66.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1-duplicates-allowed" class="question-link" target="_blank">Insert Delete GetRandom O(1) - Duplicates allowed</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 35.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Divide and Conquer", "Geometry", "Sorting", "Heap (Priority Queue)", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/k-closest-points-to-origin" class="question-link" target="_blank">K Closest Points to Origin</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Geometry</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 67.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-area-of-island" class="question-link" target="_blank">Max Area of Island</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 72.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/min-stack" class="question-link" target="_blank">Min Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 55.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-knight-moves" class="question-link" target="_blank">Minimum Knight Moves</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 41.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array" class="question-link" target="_blank">Find First and Last Position of Element in Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 46.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-pairs-with-smallest-sums" class="question-link" target="_blank">Find K Pairs with Smallest Sums</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 40.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Depth-First Search", "Design", "Queue", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-nested-list-iterator" class="question-link" target="_blank">Flatten Nested List Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Divide and Conquer"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/median-of-two-sorted-arrays" class="question-link" target="_blank">Median of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Divide and Conquer</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Two Pointers", "String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-labels" class="question-link" target="_blank">Partition Labels</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 80.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Math", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/integer-to-roman" class="question-link" target="_blank">Integer to Roman</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.1%</span>
                    <span>Acceptance: 67.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Two Pointers", "Stack", "Tree", "Depth-First Search", "Binary Search Tree", "Heap (Priority Queue)", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-binary-search-tree-value-ii" class="question-link" target="_blank">Closest Binary Search Tree Value II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 60.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shuffle-an-array" class="question-link" target="_blank">Shuffle an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 58.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-product-subarray" class="question-link" target="_blank">Maximum Product Subarray</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 34.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "String", "Backtracking"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/expression-add-operators" class="question-link" target="_blank">Expression Add Operators</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 41.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Depth-First Search", "Design", "Queue", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-nested-list-iterator" class="question-link" target="_blank">Flatten Nested List Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/delete-node-in-a-bst" class="question-link" target="_blank">Delete Node in a BST</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 52.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/koko-eating-bananas" class="question-link" target="_blank">Koko Eating Bananas</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-smallest-product-of-two-sorted-arrays" class="question-link" target="_blank">Kth Smallest Product of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 30.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Two Pointers", "String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-labels" class="question-link" target="_blank">Partition Labels</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 80.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Breadth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/01-matrix" class="question-link" target="_blank">01 Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 50.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Stack", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parenthesis-string" class="question-link" target="_blank">Valid Parenthesis String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 38.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/combination-sum-ii" class="question-link" target="_blank">Combination Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-smallest-letter-greater-than-target" class="question-link" target="_blank">Find Smallest Letter Greater Than Target</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 53.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Sliding Window", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-closest-elements" class="question-link" target="_blank">Find K Closest Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 48.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-depth-of-binary-tree" class="question-link" target="_blank">Maximum Depth of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 76.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting", "Counting Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/h-index" class="question-link" target="_blank">H-Index</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span><span class="topic-badge">Counting Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 39.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Backtracking", "Interactive"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/robot-room-cleaner" class="question-link" target="_blank">Robot Room Cleaner</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Backtracking</span><span class="topic-badge">Interactive</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 77.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/long-pressed-name" class="question-link" target="_blank">Long Pressed Name</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 32.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/closest-leaf-in-a-binary-tree" class="question-link" target="_blank">Closest Leaf in a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 42.0%</span>
                    <span>Acceptance: 46.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/shuffle-an-array" class="question-link" target="_blank">Shuffle an Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 40.9%</span>
                    <span>Acceptance: 58.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/min-stack" class="question-link" target="_blank">Min Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 40.9%</span>
                    <span>Acceptance: 55.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/edit-distance" class="question-link" target="_blank">Edit Distance</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 40.9%</span>
                    <span>Acceptance: 58.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-substring" class="question-link" target="_blank">Longest Palindromic Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 40.9%</span>
                    <span>Acceptance: 35.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "String", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/fizz-buzz" class="question-link" target="_blank">Fizz Buzz</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.5%</span>
                    <span>Acceptance: 73.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Math", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/integer-to-roman" class="question-link" target="_blank">Integer to Roman</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.5%</span>
                    <span>Acceptance: 67.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.5%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Two Pointers", "Binary Search", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/intersection-of-two-arrays" class="question-link" target="_blank">Intersection of Two Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.5%</span>
                    <span>Acceptance: 76.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Sliding Window", "Sorting", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-k-closest-elements" class="question-link" target="_blank">Find K Closest Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sliding Window</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 48.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Binary Search", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-triangle-number" class="question-link" target="_blank">Valid Triangle Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/min-stack" class="question-link" target="_blank">Min Stack</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 55.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Math", "Binary Search"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-perfect-square" class="question-link" target="_blank">Valid Perfect Square</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 44.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Two Pointers", "Design"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/dot-product-of-two-sparse-vectors" class="question-link" target="_blank">Dot Product of Two Sparse Vectors</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 89.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting", "Counting Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/h-index" class="question-link" target="_blank">H-Index</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span><span class="topic-badge">Counting Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 39.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/permutations" class="question-link" target="_blank">Permutations</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 80.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Two Pointers", "String", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/partition-labels" class="question-link" target="_blank">Partition Labels</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 80.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-two-sorted-lists" class="question-link" target="_blank">Merge Two Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 66.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/delete-node-in-a-bst" class="question-link" target="_blank">Delete Node in a BST</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 52.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "String", "Backtracking"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/expression-add-operators" class="question-link" target="_blank">Expression Add Operators</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 41.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming", "Breadth-First Search", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/01-matrix" class="question-link" target="_blank">01 Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 50.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum" class="question-link" target="_blank">Two Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 55.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Binary Search", "Prefix Sum", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/random-pick-with-weight" class="question-link" target="_blank">Random Pick with Weight</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Prefix Sum</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Stack", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parenthesis-string" class="question-link" target="_blank">Valid Parenthesis String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 38.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/long-pressed-name" class="question-link" target="_blank">Long Pressed Name</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 32.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-depth-of-binary-tree" class="question-link" target="_blank">Maximum Depth of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 36.0%</span>
                    <span>Acceptance: 76.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/next-permutation" class="question-link" target="_blank">Next Permutation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber-iv" class="question-link" target="_blank">House Robber IV</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 45.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-largest-value-in-each-tree-row" class="question-link" target="_blank">Find Largest Value in Each Tree Row</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 66.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Math", "Prefix Sum"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sum-of-all-odd-length-subarrays" class="question-link" target="_blank">Sum of All Odd Length Subarrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 83.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Two Pointers", "Tree", "Depth-First Search", "Breadth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum-iv-input-is-a-bst" class="question-link" target="_blank">Two Sum IV - Input is a BST</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 61.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/create-binary-tree-from-descriptions" class="question-link" target="_blank">Create Binary Tree From Descriptions</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 81.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Design", "Queue", "Data Stream"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/moving-average-from-data-stream" class="question-link" target="_blank">Moving Average from Data Stream</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Data Stream</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 79.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Depth-First Search", "Design", "Trie"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-add-and-search-words-data-structure" class="question-link" target="_blank">Design Add and Search Words Data Structure</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 46.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber-ii" class="question-link" target="_blank">House Robber II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 43.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/same-tree" class="question-link" target="_blank">Same Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 64.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Dynamic Programming", "Sorting"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-profit-in-job-scheduling" class="question-link" target="_blank">Maximum Profit in Job Scheduling</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 54.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/second-minimum-node-in-a-binary-tree" class="question-link" target="_blank">Second Minimum Node In a Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Divide and Conquer", "Heap (Priority Queue)", "Merge Sort"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-k-sorted-lists" class="question-link" target="_blank">Merge k Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Merge Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/move-zeroes" class="question-link" target="_blank">Move Zeroes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 62.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximize-total-tastiness-of-purchased-fruits" class="question-link" target="_blank">Maximize Total Tastiness of Purchased Fruits</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 64.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-connected-components-in-an-undirected-graph" class="question-link" target="_blank">Number of Connected Components in an Undirected Graph</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 63.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/validate-binary-search-tree" class="question-link" target="_blank">Validate Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 34.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Backtracking", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder-ii" class="question-link" target="_blank">Word Ladder II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 27.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-to-type-word-using-special-typewriter" class="question-link" target="_blank">Minimum Time to Type Word Using Special Typewriter</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 76.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/combination-sum" class="question-link" target="_blank">Combination Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 73.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Math", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/sort-transformed-array" class="question-link" target="_blank">Sort Transformed Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 33.9%</span>
                    <span>Acceptance: 56.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 31.1%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/diameter-of-binary-tree" class="question-link" target="_blank">Diameter of Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 31.1%</span>
                    <span>Acceptance: 62.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/combination-sum" class="question-link" target="_blank">Combination Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 31.1%</span>
                    <span>Acceptance: 73.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Hash Table", "String", "Backtracking", "Breadth-First Search"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-ladder-ii" class="question-link" target="_blank">Word Ladder II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 31.1%</span>
                    <span>Acceptance: 27.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Binary Search", "Divide and Conquer"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/median-of-two-sorted-arrays" class="question-link" target="_blank">Median of Two Sorted Arrays</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Divide and Conquer</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 31.1%</span>
                    <span>Acceptance: 42.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Greedy"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-time-to-type-word-using-special-typewriter" class="question-link" target="_blank">Minimum Time to Type Word Using Special Typewriter</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 76.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/next-permutation" class="question-link" target="_blank">Next Permutation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 42.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Depth-First Search", "Design", "Trie"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-add-and-search-words-data-structure" class="question-link" target="_blank">Design Add and Search Words Data Structure</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 46.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Two Pointers", "Tree", "Depth-First Search", "Breadth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/two-sum-iv-input-is-a-bst" class="question-link" target="_blank">Two Sum IV - Input is a BST</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 61.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Linked List", "Two Pointers", "Stack", "Recursion"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reorder-list" class="question-link" target="_blank">Reorder List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Stack</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/validate-binary-search-tree" class="question-link" target="_blank">Validate Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 34.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber-iv" class="question-link" target="_blank">House Robber IV</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 45.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/move-zeroes" class="question-link" target="_blank">Move Zeroes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 62.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber-ii" class="question-link" target="_blank">House Robber II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 43.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/same-tree" class="question-link" target="_blank">Same Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 23.6%</span>
                    <span>Acceptance: 64.4%</span>
                </div>
            </div>

        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const questionCards = document.querySelectorAll('.question-card');
        const difficultyButtons = document.querySelectorAll('.filter-btn');
        const topicTags = document.querySelectorAll('.topic-tag');
        
        function filterQuestions() {
            const searchTerm = searchInput.value.toLowerCase();
            const activeDifficulties = Array.from(difficultyButtons)
                .filter(btn => btn.classList.contains('active'))
                .map(btn => btn.dataset.difficulty);
            const activeTopics = Array.from(topicTags)
                .filter(tag => tag.classList.contains('active'))
                .map(tag => tag.dataset.topic);
            
            questionCards.forEach(card => {
                const title = card.querySelector('.question-title').textContent.toLowerCase();
                const difficulty = card.dataset.difficulty;
                const topics = JSON.parse(card.dataset.topics);
                
                const matchesSearch = title.includes(searchTerm);
                const matchesDifficulty = activeDifficulties.includes(difficulty);
                const matchesTopics = activeTopics.length === 0 || 
                    topics.some(topic => activeTopics.includes(topic));
                
                if (matchesSearch && matchesDifficulty && matchesTopics) {
                    card.style.display = 'flex';
                    card.style.opacity = '1';
                } else {
                    card.style.display = 'none';
                    card.style.opacity = '0';
                }
            });
        }
        
        // Add smooth transitions for filtering
        questionCards.forEach(card => {
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        });
        
        searchInput.addEventListener('input', filterQuestions);
        
        difficultyButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                btn.classList.toggle('active');
                filterQuestions();
            });
        });
        
        topicTags.forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('active');
                filterQuestions();
            });
        });

        // Add smooth scrolling for all links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
