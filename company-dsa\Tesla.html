
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tesla DSA Questions</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #818cf8;
            --secondary: #f1f5f9;
            --easy: #22c55e;
            --medium: #f59e0b;
            --hard: #ef4444;
            --text: #0f172a;
            --text-light: #64748b;
            --card-bg: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--secondary);
            color: var(--text);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        h1 {
            color: var(--primary-dark);
            text-align: center;
            margin: 40px 0;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
            }
            to {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
            }
        }

        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 20px;
            border: 2px solid var(--primary-light);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: var(--card-bg);
            color: var(--text);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .difficulty-filter {
            display: flex;
            gap: 12px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            opacity: 0.8;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            opacity: 1;
        }

        .filter-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: var(--shadow);
        }

        .filter-btn.easy {
            background-color: var(--easy);
            color: white;
        }

        .filter-btn.medium {
            background-color: var(--medium);
            color: white;
        }

        .filter-btn.hard {
            background-color: var(--hard);
            color: white;
        }

        .topics-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 20px 0 30px;
        }

        .topic-tag {
            padding: 8px 16px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.8;
            transition: all 0.3s ease;
            user-select: none;
        }

        .topic-tag:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        .topic-tag.active {
            opacity: 1;
            background-color: var(--primary-dark);
            box-shadow: var(--shadow);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 16px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .difficulty {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .difficulty-easy { background-color: var(--easy); color: white; }
        .difficulty-medium { background-color: var(--medium); color: white; }
        .difficulty-hard { background-color: var(--hard); color: white; }

        .question-title {
            font-size: 1.2em;
            margin: 10px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        .question-link {
            color: var(--primary-dark);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .question-link:hover {
            color: var(--primary);
            text-decoration: underline;
        }

        .question-stats {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            font-size: 0.95em;
            color: var(--text-light);
            padding-top: 15px;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
        }

        .topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .topic-badge:hover {
            transform: translateY(-2px);
            background-color: var(--primary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            h1 {
                font-size: 2rem;
                margin: 30px 0;
            }

            .filters {
                flex-direction: column;
                gap: 15px;
            }

            .difficulty-filter {
                justify-content: center;
                width: 100%;
            }

            .search-box {
                width: 100%;
            }

            .stat-card {
                padding: 20px;
            }

            .question-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 2em;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 1.75rem;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .topic-tag {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .question-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling and selection styles */
        html {
            scroll-behavior: smooth;
        }

        ::selection {
            background-color: var(--primary-light);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tesla DSA Questions</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">105</div>
                <div>Total Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">28</div>
                <div>Easy Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">69</div>
                <div>Medium Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">8</div>
                <div>Hard Questions</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" placeholder="Search questions..." id="searchInput">
            <div class="difficulty-filter">
                <button class="filter-btn easy active" data-difficulty="EASY">Easy</button>
                <button class="filter-btn medium active" data-difficulty="MEDIUM">Medium</button>
                <button class="filter-btn hard active" data-difficulty="HARD">Hard</button>
            </div>
        </div>

        <div class="topics-filter">
            <div class="topic-tag" data-topic="Array">Array</div><div class="topic-tag" data-topic="Backtracking">Backtracking</div><div class="topic-tag" data-topic="Binary Search">Binary Search</div><div class="topic-tag" data-topic="Binary Search Tree">Binary Search Tree</div><div class="topic-tag" data-topic="Binary Tree">Binary Tree</div><div class="topic-tag" data-topic="Bit Manipulation">Bit Manipulation</div><div class="topic-tag" data-topic="Breadth-First Search">Breadth-First Search</div><div class="topic-tag" data-topic="Bucket Sort">Bucket Sort</div><div class="topic-tag" data-topic="Concurrency">Concurrency</div><div class="topic-tag" data-topic="Counting">Counting</div><div class="topic-tag" data-topic="Data Stream">Data Stream</div><div class="topic-tag" data-topic="Database">Database</div><div class="topic-tag" data-topic="Depth-First Search">Depth-First Search</div><div class="topic-tag" data-topic="Design">Design</div><div class="topic-tag" data-topic="Divide and Conquer">Divide and Conquer</div><div class="topic-tag" data-topic="Doubly-Linked List">Doubly-Linked List</div><div class="topic-tag" data-topic="Dynamic Programming">Dynamic Programming</div><div class="topic-tag" data-topic="Graph">Graph</div><div class="topic-tag" data-topic="Greedy">Greedy</div><div class="topic-tag" data-topic="Hash Table">Hash Table</div><div class="topic-tag" data-topic="Heap (Priority Queue)">Heap (Priority Queue)</div><div class="topic-tag" data-topic="Iterator">Iterator</div><div class="topic-tag" data-topic="Linked List">Linked List</div><div class="topic-tag" data-topic="Math">Math</div><div class="topic-tag" data-topic="Matrix">Matrix</div><div class="topic-tag" data-topic="Memoization">Memoization</div><div class="topic-tag" data-topic="Merge Sort">Merge Sort</div><div class="topic-tag" data-topic="Monotonic Stack">Monotonic Stack</div><div class="topic-tag" data-topic="Prefix Sum">Prefix Sum</div><div class="topic-tag" data-topic="Queue">Queue</div><div class="topic-tag" data-topic="Quickselect">Quickselect</div><div class="topic-tag" data-topic="Recursion">Recursion</div><div class="topic-tag" data-topic="Simulation">Simulation</div><div class="topic-tag" data-topic="Sliding Window">Sliding Window</div><div class="topic-tag" data-topic="Sorting">Sorting</div><div class="topic-tag" data-topic="Stack">Stack</div><div class="topic-tag" data-topic="String">String</div><div class="topic-tag" data-topic="Topological Sort">Topological Sort</div><div class="topic-tag" data-topic="Tree">Tree</div><div class="topic-tag" data-topic="Trie">Trie</div><div class="topic-tag" data-topic="Two Pointers">Two Pointers</div><div class="topic-tag" data-topic="Union Find">Union Find</div>
        </div>

        <div class="question-grid">

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-intervals" class="question-link" target="_blank">Merge Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Sliding Window"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-substring-without-repeating-characters" class="question-link" target="_blank">Longest Substring Without Repeating Characters</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sliding Window</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 36.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-changes-to-make-alternating-binary-string" class="question-link" target="_blank">Minimum Changes To Make Alternating Binary String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.6%</span>
                    <span>Acceptance: 63.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Database"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-followers-count" class="question-link" target="_blank">Find Followers Count</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.6%</span>
                    <span>Acceptance: 69.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/letter-combinations-of-a-phone-number" class="question-link" target="_blank">Letter Combinations of a Phone Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.6%</span>
                    <span>Acceptance: 63.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/letter-combinations-of-a-phone-number" class="question-link" target="_blank">Letter Combinations of a Phone Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.9%</span>
                    <span>Acceptance: 63.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Linked List", "Design", "Queue"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-circular-queue" class="question-link" target="_blank">Design Circular Queue</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.1%</span>
                    <span>Acceptance: 52.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "String", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/basic-calculator-ii" class="question-link" target="_blank">Basic Calculator II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 90.1%</span>
                    <span>Acceptance: 45.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Linked List", "Design", "Queue"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-circular-queue" class="question-link" target="_blank">Design Circular Queue</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 87.8%</span>
                    <span>Acceptance: 52.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 84.7%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "String", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/basic-calculator-ii" class="question-link" target="_blank">Basic Calculator II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 82.7%</span>
                    <span>Acceptance: 45.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-islands" class="question-link" target="_blank">Number of Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 82.7%</span>
                    <span>Acceptance: 61.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 80.9%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/container-with-most-water" class="question-link" target="_blank">Container With Most Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.9%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-intervals" class="question-link" target="_blank">Merge Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.9%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.9%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.9%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Linked List", "Design", "Queue"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-circular-queue" class="question-link" target="_blank">Design Circular Queue</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.9%</span>
                    <span>Acceptance: 52.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Bucket Sort", "Counting", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/top-k-frequent-elements" class="question-link" target="_blank">Top K Frequent Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Bucket Sort</span><span class="topic-badge">Counting</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.9%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Concurrency"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/building-h2o" class="question-link" target="_blank">Building H2O</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Concurrency</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 78.2%</span>
                    <span>Acceptance: 57.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-nodes-in-k-group" class="question-link" target="_blank">Reverse Nodes in k-Group</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.4%</span>
                    <span>Acceptance: 62.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Divide and Conquer", "Sorting", "Heap (Priority Queue)", "Bucket Sort", "Counting", "Quickselect"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/top-k-frequent-elements" class="question-link" target="_blank">Top K Frequent Elements</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Bucket Sort</span><span class="topic-badge">Counting</span><span class="topic-badge">Quickselect</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.4%</span>
                    <span>Acceptance: 64.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/container-with-most-water" class="question-link" target="_blank">Container With Most Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.4%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Matrix", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-winner-on-a-tic-tac-toe-game" class="question-link" target="_blank">Find Winner on a Tic Tac Toe Game</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Matrix</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.4%</span>
                    <span>Acceptance: 54.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 76.4%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-nodes-in-k-group" class="question-link" target="_blank">Reverse Nodes in k-Group</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.3%</span>
                    <span>Acceptance: 62.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Matrix", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-winner-on-a-tic-tac-toe-game" class="question-link" target="_blank">Find Winner on a Tic Tac Toe Game</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Matrix</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.3%</span>
                    <span>Acceptance: 54.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 74.3%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Concurrency"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/building-h2o" class="question-link" target="_blank">Building H2O</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Concurrency</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 73.2%</span>
                    <span>Acceptance: 57.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.9%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/decode-the-message" class="question-link" target="_blank">Decode the Message</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.9%</span>
                    <span>Acceptance: 85.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/3sum" class="question-link" target="_blank">3Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.9%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-sum-equals-k" class="question-link" target="_blank">Subarray Sum Equals K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.9%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/move-zeroes" class="question-link" target="_blank">Move Zeroes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.9%</span>
                    <span>Acceptance: 62.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Dynamic Programming", "Trie", "Memoization"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-break" class="question-link" target="_blank">Word Break</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Trie</span><span class="topic-badge">Memoization</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 70.9%</span>
                    <span>Acceptance: 47.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-time-to-buy-and-sell-stock" class="question-link" target="_blank">Best Time to Buy and Sell Stock</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.8%</span>
                    <span>Acceptance: 54.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/subarray-sum-equals-k" class="question-link" target="_blank">Subarray Sum Equals K</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.8%</span>
                    <span>Acceptance: 44.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Counting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-balloons" class="question-link" target="_blank">Maximum Number of Balloons</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.8%</span>
                    <span>Acceptance: 59.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Dynamic Programming", "Trie", "Memoization"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-break" class="question-link" target="_blank">Word Break</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Trie</span><span class="topic-badge">Memoization</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.8%</span>
                    <span>Acceptance: 47.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/move-zeroes" class="question-link" target="_blank">Move Zeroes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.8%</span>
                    <span>Acceptance: 62.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/decode-the-message" class="question-link" target="_blank">Decode the Message</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 68.8%</span>
                    <span>Acceptance: 85.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/course-schedule-ii" class="question-link" target="_blank">Course Schedule II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.9%</span>
                    <span>Acceptance: 52.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Concurrency"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-bounded-blocking-queue" class="question-link" target="_blank">Design Bounded Blocking Queue</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Concurrency</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.9%</span>
                    <span>Acceptance: 72.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "String", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/basic-calculator-ii" class="question-link" target="_blank">Basic Calculator II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.9%</span>
                    <span>Acceptance: 45.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Divide and Conquer", "Heap (Priority Queue)", "Merge Sort"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-k-sorted-lists" class="question-link" target="_blank">Merge k Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Merge Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.9%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/generate-parentheses" class="question-link" target="_blank">Generate Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.9%</span>
                    <span>Acceptance: 76.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Tree", "Depth-First Search", "Breadth-First Search", "Design", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/serialize-and-deserialize-binary-tree" class="question-link" target="_blank">Serialize and Deserialize Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.9%</span>
                    <span>Acceptance: 58.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Tree", "Depth-First Search", "Breadth-First Search", "Design", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/serialize-and-deserialize-bst" class="question-link" target="_blank">Serialize and Deserialize BST</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.9%</span>
                    <span>Acceptance: 58.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Counting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximum-number-of-balloons" class="question-link" target="_blank">Maximum Number of Balloons</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 63.9%</span>
                    <span>Acceptance: 59.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-area-of-island" class="question-link" target="_blank">Max Area of Island</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 72.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Divide and Conquer", "Heap (Priority Queue)", "Merge Sort"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-k-sorted-lists" class="question-link" target="_blank">Merge k Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Merge Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Concurrency"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-bounded-blocking-queue" class="question-link" target="_blank">Design Bounded Blocking Queue</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Concurrency</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 72.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/generate-parentheses" class="question-link" target="_blank">Generate Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 76.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Tree", "Depth-First Search", "Breadth-First Search", "Design", "Binary Tree"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/serialize-and-deserialize-binary-tree" class="question-link" target="_blank">Serialize and Deserialize Binary Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 58.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Greedy", "Sorting", "Heap (Priority Queue)", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reorganize-string" class="question-link" target="_blank">Reorganize String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Tree", "Depth-First Search", "Breadth-First Search", "Design", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/serialize-and-deserialize-bst" class="question-link" target="_blank">Serialize and Deserialize BST</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 58.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/course-schedule-ii" class="question-link" target="_blank">Course Schedule II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 52.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Concurrency"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/building-h2o" class="question-link" target="_blank">Building H2O</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Concurrency</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 61.7%</span>
                    <span>Acceptance: 57.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/is-subsequence" class="question-link" target="_blank">Is Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Greedy", "Sorting", "Heap (Priority Queue)", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reorganize-string" class="question-link" target="_blank">Reorganize String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-minimum-in-rotated-sorted-array" class="question-link" target="_blank">Find Minimum in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 52.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-linked-list" class="question-link" target="_blank">Reverse Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 78.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-anagram" class="question-link" target="_blank">Valid Anagram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 66.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/intersection-of-two-linked-lists" class="question-link" target="_blank">Intersection of Two Linked Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 60.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Linked List", "Design", "Queue"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-circular-queue" class="question-link" target="_blank">Design Circular Queue</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 52.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Database"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-followers-count" class="question-link" target="_blank">Find Followers Count</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 69.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/simplify-path" class="question-link" target="_blank">Simplify Path</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 46.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Divide and Conquer", "Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/construct-binary-tree-from-preorder-and-inorder-traversal" class="question-link" target="_blank">Construct Binary Tree from Preorder and Inorder Traversal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 66.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Trie", "Sorting", "Heap (Priority Queue)", "Bucket Sort", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/top-k-frequent-words" class="question-link" target="_blank">Top K Frequent Words</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Trie</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Bucket Sort</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 58.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/combination-sum-ii" class="question-link" target="_blank">Combination Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximal-network-rank" class="question-link" target="_blank">Maximal Network Rank</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 65.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber" class="question-link" target="_blank">House Robber</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/decode-ways" class="question-link" target="_blank">Decode Ways</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 36.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-changes-to-make-alternating-binary-string" class="question-link" target="_blank">Minimum Changes To Make Alternating Binary String</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 63.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/consecutive-numbers" class="question-link" target="_blank">Consecutive Numbers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 45.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-area-of-island" class="question-link" target="_blank">Max Area of Island</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 72.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy", "Sorting", "Heap (Priority Queue)", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/meeting-rooms-ii" class="question-link" target="_blank">Meeting Rooms II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 51.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Depth-First Search", "Design", "Queue", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-nested-list-iterator" class="question-link" target="_blank">Flatten Nested List Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 53.9%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-linked-list" class="question-link" target="_blank">Reverse Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 78.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/longest-palindromic-substring" class="question-link" target="_blank">Longest Palindromic Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 35.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/consecutive-numbers" class="question-link" target="_blank">Consecutive Numbers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 45.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Depth-First Search", "Design", "Queue", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-nested-list-iterator" class="question-link" target="_blank">Flatten Nested List Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/group-anagrams" class="question-link" target="_blank">Group Anagrams</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/decode-ways" class="question-link" target="_blank">Decode Ways</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 36.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Linked List", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/intersection-of-two-linked-lists" class="question-link" target="_blank">Intersection of Two Linked Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 60.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/is-subsequence" class="question-link" target="_blank">Is Subsequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 48.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "String", "Trie", "Sorting", "Heap (Priority Queue)", "Bucket Sort", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/top-k-frequent-words" class="question-link" target="_blank">Top K Frequent Words</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Trie</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Bucket Sort</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 58.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/house-robber" class="question-link" target="_blank">House Robber</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 52.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/maximal-network-rank" class="question-link" target="_blank">Maximal Network Rank</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 65.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Math", "Greedy", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/largest-perimeter-triangle" class="question-link" target="_blank">Largest Perimeter Triangle</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 57.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Divide and Conquer", "Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/construct-binary-tree-from-preorder-and-inorder-traversal" class="question-link" target="_blank">Construct Binary Tree from Preorder and Inorder Traversal</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 66.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Hash Table", "Math", "Binary Search", "Bit Manipulation", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/missing-number" class="question-link" target="_blank">Missing Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Bit Manipulation</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 69.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Greedy", "Sorting", "Heap (Priority Queue)", "Prefix Sum"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/meeting-rooms-ii" class="question-link" target="_blank">Meeting Rooms II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Greedy</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Prefix Sum</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 51.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Design", "Matrix", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-tic-tac-toe" class="question-link" target="_blank">Design Tic-Tac-Toe</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Design</span><span class="topic-badge">Matrix</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 58.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/simplify-path" class="question-link" target="_blank">Simplify Path</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 46.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-palindrome" class="question-link" target="_blank">Valid Palindrome</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 50.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "String", "Sorting"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-anagram" class="question-link" target="_blank">Valid Anagram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 66.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Design", "Queue", "Data Stream"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/moving-average-from-data-stream" class="question-link" target="_blank">Moving Average from Data Stream</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Data Stream</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 79.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Depth-First Search", "Design", "Trie"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-add-and-search-words-data-structure" class="question-link" target="_blank">Design Add and Search Words Data Structure</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 46.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/find-minimum-in-rotated-sorted-array" class="question-link" target="_blank">Find Minimum in Rotated Sorted Array</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 52.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Backtracking"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/combination-sum-ii" class="question-link" target="_blank">Combination Sum II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Backtracking</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 51.7%</span>
                    <span>Acceptance: 57.2%</span>
                </div>
            </div>

        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const questionCards = document.querySelectorAll('.question-card');
        const difficultyButtons = document.querySelectorAll('.filter-btn');
        const topicTags = document.querySelectorAll('.topic-tag');
        
        function filterQuestions() {
            const searchTerm = searchInput.value.toLowerCase();
            const activeDifficulties = Array.from(difficultyButtons)
                .filter(btn => btn.classList.contains('active'))
                .map(btn => btn.dataset.difficulty);
            const activeTopics = Array.from(topicTags)
                .filter(tag => tag.classList.contains('active'))
                .map(tag => tag.dataset.topic);
            
            questionCards.forEach(card => {
                const title = card.querySelector('.question-title').textContent.toLowerCase();
                const difficulty = card.dataset.difficulty;
                const topics = JSON.parse(card.dataset.topics);
                
                const matchesSearch = title.includes(searchTerm);
                const matchesDifficulty = activeDifficulties.includes(difficulty);
                const matchesTopics = activeTopics.length === 0 || 
                    topics.some(topic => activeTopics.includes(topic));
                
                if (matchesSearch && matchesDifficulty && matchesTopics) {
                    card.style.display = 'flex';
                    card.style.opacity = '1';
                } else {
                    card.style.display = 'none';
                    card.style.opacity = '0';
                }
            });
        }
        
        // Add smooth transitions for filtering
        questionCards.forEach(card => {
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        });
        
        searchInput.addEventListener('input', filterQuestions);
        
        difficultyButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                btn.classList.toggle('active');
                filterQuestions();
            });
        });
        
        topicTags.forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('active');
                filterQuestions();
            });
        });

        // Add smooth scrolling for all links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
