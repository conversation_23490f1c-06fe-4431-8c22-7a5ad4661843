{"ast": null, "code": "// Note: types exported from `index.d.ts`\nexport { CONTINUE, EXIT, SKIP, visitParents } from './lib/index.js';", "map": {"version": 3, "names": ["CONTINUE", "EXIT", "SKIP", "visitParents"], "sources": ["C:/Users/<USER>/Downloads/quiz/aich (4)/aich (3)/aich(5)/node_modules/unist-util-visit-parents/index.js"], "sourcesContent": ["// Note: types exported from `index.d.ts`\nexport {CONTINUE, EXIT, SKIP, visitParents} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,YAAY,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}