
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X DSA Questions</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #818cf8;
            --secondary: #f1f5f9;
            --easy: #22c55e;
            --medium: #f59e0b;
            --hard: #ef4444;
            --text: #0f172a;
            --text-light: #64748b;
            --card-bg: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--secondary);
            color: var(--text);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        h1 {
            color: var(--primary-dark);
            text-align: center;
            margin: 40px 0;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
            }
            to {
                text-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
            }
        }

        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 20px;
            border: 2px solid var(--primary-light);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: var(--card-bg);
            color: var(--text);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .difficulty-filter {
            display: flex;
            gap: 12px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            opacity: 0.8;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            opacity: 1;
        }

        .filter-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: var(--shadow);
        }

        .filter-btn.easy {
            background-color: var(--easy);
            color: white;
        }

        .filter-btn.medium {
            background-color: var(--medium);
            color: white;
        }

        .filter-btn.hard {
            background-color: var(--hard);
            color: white;
        }

        .topics-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 20px 0 30px;
        }

        .topic-tag {
            padding: 8px 16px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.8;
            transition: all 0.3s ease;
            user-select: none;
        }

        .topic-tag:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        .topic-tag.active {
            opacity: 1;
            background-color: var(--primary-dark);
            box-shadow: var(--shadow);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 16px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid rgba(99, 102, 241, 0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .difficulty {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .difficulty-easy { background-color: var(--easy); color: white; }
        .difficulty-medium { background-color: var(--medium); color: white; }
        .difficulty-hard { background-color: var(--hard); color: white; }

        .question-title {
            font-size: 1.2em;
            margin: 10px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        .question-link {
            color: var(--primary-dark);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .question-link:hover {
            color: var(--primary);
            text-decoration: underline;
        }

        .question-stats {
            display: flex;
            justify-content: space-between;
            margin-top: auto;
            font-size: 0.95em;
            color: var(--text-light);
            padding-top: 15px;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
        }

        .topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-badge {
            display: inline-block;
            padding: 4px 12px;
            background-color: var(--primary-light);
            color: white;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .topic-badge:hover {
            transform: translateY(-2px);
            background-color: var(--primary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            h1 {
                font-size: 2rem;
                margin: 30px 0;
            }

            .filters {
                flex-direction: column;
                gap: 15px;
            }

            .difficulty-filter {
                justify-content: center;
                width: 100%;
            }

            .search-box {
                width: 100%;
            }

            .stat-card {
                padding: 20px;
            }

            .question-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 2em;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 1.75rem;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .topic-tag {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .question-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling and selection styles */
        html {
            scroll-behavior: smooth;
        }

        ::selection {
            background-color: var(--primary-light);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>X DSA Questions</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">99</div>
                <div>Total Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">16</div>
                <div>Easy Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">59</div>
                <div>Medium Questions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">24</div>
                <div>Hard Questions</div>
            </div>
        </div>

        <div class="filters">
            <input type="text" class="search-box" placeholder="Search questions..." id="searchInput">
            <div class="difficulty-filter">
                <button class="filter-btn easy active" data-difficulty="EASY">Easy</button>
                <button class="filter-btn medium active" data-difficulty="MEDIUM">Medium</button>
                <button class="filter-btn hard active" data-difficulty="HARD">Hard</button>
            </div>
        </div>

        <div class="topics-filter">
            <div class="topic-tag" data-topic="Array">Array</div><div class="topic-tag" data-topic="Backtracking">Backtracking</div><div class="topic-tag" data-topic="Binary Indexed Tree">Binary Indexed Tree</div><div class="topic-tag" data-topic="Binary Search">Binary Search</div><div class="topic-tag" data-topic="Binary Search Tree">Binary Search Tree</div><div class="topic-tag" data-topic="Binary Tree">Binary Tree</div><div class="topic-tag" data-topic="Bit Manipulation">Bit Manipulation</div><div class="topic-tag" data-topic="Breadth-First Search">Breadth-First Search</div><div class="topic-tag" data-topic="Counting">Counting</div><div class="topic-tag" data-topic="Database">Database</div><div class="topic-tag" data-topic="Depth-First Search">Depth-First Search</div><div class="topic-tag" data-topic="Design">Design</div><div class="topic-tag" data-topic="Divide and Conquer">Divide and Conquer</div><div class="topic-tag" data-topic="Doubly-Linked List">Doubly-Linked List</div><div class="topic-tag" data-topic="Dynamic Programming">Dynamic Programming</div><div class="topic-tag" data-topic="Enumeration">Enumeration</div><div class="topic-tag" data-topic="Geometry">Geometry</div><div class="topic-tag" data-topic="Graph">Graph</div><div class="topic-tag" data-topic="Greedy">Greedy</div><div class="topic-tag" data-topic="Hash Table">Hash Table</div><div class="topic-tag" data-topic="Heap (Priority Queue)">Heap (Priority Queue)</div><div class="topic-tag" data-topic="Iterator">Iterator</div><div class="topic-tag" data-topic="Line Sweep">Line Sweep</div><div class="topic-tag" data-topic="Linked List">Linked List</div><div class="topic-tag" data-topic="Math">Math</div><div class="topic-tag" data-topic="Matrix">Matrix</div><div class="topic-tag" data-topic="Memoization">Memoization</div><div class="topic-tag" data-topic="Merge Sort">Merge Sort</div><div class="topic-tag" data-topic="Monotonic Stack">Monotonic Stack</div><div class="topic-tag" data-topic="Ordered Set">Ordered Set</div><div class="topic-tag" data-topic="Queue">Queue</div><div class="topic-tag" data-topic="Randomized">Randomized</div><div class="topic-tag" data-topic="Recursion">Recursion</div><div class="topic-tag" data-topic="Segment Tree">Segment Tree</div><div class="topic-tag" data-topic="Simulation">Simulation</div><div class="topic-tag" data-topic="Sorting">Sorting</div><div class="topic-tag" data-topic="Stack">Stack</div><div class="topic-tag" data-topic="String">String</div><div class="topic-tag" data-topic="Topological Sort">Topological Sort</div><div class="topic-tag" data-topic="Tree">Tree</div><div class="topic-tag" data-topic="Trie">Trie</div><div class="topic-tag" data-topic="Two Pointers">Two Pointers</div><div class="topic-tag" data-topic="Union Find">Union Find</div>
        </div>

        <div class="question-grid">

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Math", "Design", "Randomized"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/insert-delete-getrandom-o1" class="question-link" target="_blank">Insert Delete GetRandom O(1)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Design</span><span class="topic-badge">Randomized</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 100.0%</span>
                    <span>Acceptance: 54.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Depth-First Search", "Design", "Queue", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-nested-list-iterator" class="question-link" target="_blank">Flatten Nested List Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 96.3%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-intervals" class="question-link" target="_blank">Merge Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 96.3%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 96.3%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Binary Search", "Design", "Sorting", "Ordered Set"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/tweet-counts-per-frequency" class="question-link" target="_blank">Tweet Counts Per Frequency</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Design</span><span class="topic-badge">Sorting</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 94.2%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Design", "Trie"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/implement-trie-prefix-tree" class="question-link" target="_blank">Implement Trie (Prefix Tree)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Design</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 94.2%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-twitter" class="question-link" target="_blank">Design Twitter</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 94.2%</span>
                    <span>Acceptance: 41.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Sorting", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-meeting-point" class="question-link" target="_blank">Best Meeting Point</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Sorting</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.9%</span>
                    <span>Acceptance: 61.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/investments-in-2016" class="question-link" target="_blank">Investments in 2016</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.9%</span>
                    <span>Acceptance: 49.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Stack", "Tree", "Depth-First Search", "Design", "Queue", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-nested-list-iterator" class="question-link" target="_blank">Flatten Nested List Iterator</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Stack</span><span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Design</span><span class="topic-badge">Queue</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.0%</span>
                    <span>Acceptance: 65.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Sorting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-intervals" class="question-link" target="_blank">Merge Intervals</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Sorting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.0%</span>
                    <span>Acceptance: 48.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Two Pointers", "Dynamic Programming", "Stack", "Monotonic Stack"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water" class="question-link" target="_blank">Trapping Rain Water</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Stack</span><span class="topic-badge">Monotonic Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 91.0%</span>
                    <span>Acceptance: 64.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-genetic-mutation" class="question-link" target="_blank">Minimum Genetic Mutation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 55.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-authentication-manager" class="question-link" target="_blank">Design Authentication Manager</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 58.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/one-edit-distance" class="question-link" target="_blank">One Edit Distance</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 34.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Dynamic Programming", "Greedy", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/wildcard-matching" class="question-link" target="_blank">Wildcard Matching</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 29.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Database"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/customer-placing-the-largest-number-of-orders" class="question-link" target="_blank">Customer Placing the Largest Number of Orders</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 64.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/validate-ip-address" class="question-link" target="_blank">Validate IP Address</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 27.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "String", "Dynamic Programming", "Backtracking", "Trie", "Memoization"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-break-ii" class="question-link" target="_blank">Word Break II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Trie</span><span class="topic-badge">Memoization</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 52.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Divide and Conquer", "Binary Indexed Tree", "Segment Tree", "Line Sweep", "Heap (Priority Queue)", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/the-skyline-problem" class="question-link" target="_blank">The Skyline Problem</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Binary Indexed Tree</span><span class="topic-badge">Segment Tree</span><span class="topic-badge">Line Sweep</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 43.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Depth-First Search", "Breadth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/alien-dictionary" class="question-link" target="_blank">Alien Dictionary</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Divide and Conquer", "Heap (Priority Queue)", "Merge Sort"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-k-sorted-lists" class="question-link" target="_blank">Merge k Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Merge Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-elements-to-add-to-form-a-given-sum" class="question-link" target="_blank">Minimum Elements to Add to Form a Given Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 44.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-connected-components-in-an-undirected-graph" class="question-link" target="_blank">Number of Connected Components in an Undirected Graph</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 63.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Math", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/integer-to-roman" class="question-link" target="_blank">Integer to Roman</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 67.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Design", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-2d-vector" class="question-link" target="_blank">Flatten 2D Vector</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Design</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 50.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Stack", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-all-occurrences-of-a-substring" class="question-link" target="_blank">Remove All Occurrences of a Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 78.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Sorting", "Heap (Priority Queue)", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-smallest-element-in-a-sorted-matrix" class="question-link" target="_blank">Kth Smallest Element in a Sorted Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 63.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Design"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-skiplist" class="question-link" target="_blank">Design Skiplist</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 61.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-linked-list" class="question-link" target="_blank">Reverse Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 78.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/pascals-triangle" class="question-link" target="_blank">Pascal's Triangle</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 76.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Geometry"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-points-on-a-line" class="question-link" target="_blank">Max Points on a Line</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Geometry</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 28.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/permutation-sequence" class="question-link" target="_blank">Permutation Sequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 49.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/finding-the-users-active-minutes" class="question-link" target="_blank">Finding the Users Active Minutes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 80.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rectangles-area" class="question-link" target="_blank">Rectangles Area</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 68.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "String", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/multiply-strings" class="question-link" target="_blank">Multiply Strings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Breadth-First Search", "Heap (Priority Queue)", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water-ii" class="question-link" target="_blank">Trapping Rain Water II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 58.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Enumeration"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-black-blocks" class="question-link" target="_blank">Number of Black Blocks</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 37.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/tree-node" class="question-link" target="_blank">Tree Node</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 73.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-number-of-steps-to-make-two-strings-anagram" class="question-link" target="_blank">Minimum Number of Steps to Make Two Strings Anagram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 82.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/masking-personal-information" class="question-link" target="_blank">Masking Personal Information</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 50.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-sub-islands" class="question-link" target="_blank">Count Sub Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 72.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Bit Manipulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-gap" class="question-link" target="_blank">Binary Gap</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Bit Manipulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 64.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Dynamic Programming", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/regular-expression-matching" class="question-link" target="_blank">Regular Expression Matching</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 29.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-student-number-in-departments" class="question-link" target="_blank">Count Student Number in Departments</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 59.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Math", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/happy-number" class="question-link" target="_blank">Happy Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 57.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Database"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/invalid-tweets" class="question-link" target="_blank">Invalid Tweets</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 85.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-search-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/process-tasks-using-servers" class="question-link" target="_blank">Process Tasks Using Servers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 40.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Queue", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/time-needed-to-buy-tickets" class="question-link" target="_blank">Time Needed to Buy Tickets</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 89.4%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Design", "Trie"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/implement-trie-prefix-tree" class="question-link" target="_blank">Implement Trie (Prefix Tree)</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Design</span><span class="topic-badge">Trie</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 85.0%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Binary Search", "Design", "Sorting", "Ordered Set"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/tweet-counts-per-frequency" class="question-link" target="_blank">Tweet Counts Per Frequency</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Design</span><span class="topic-badge">Sorting</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 85.0%</span>
                    <span>Acceptance: 45.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-twitter" class="question-link" target="_blank">Design Twitter</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 85.0%</span>
                    <span>Acceptance: 41.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/investments-in-2016" class="question-link" target="_blank">Investments in 2016</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 77.3%</span>
                    <span>Acceptance: 49.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Math", "Sorting", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/best-meeting-point" class="question-link" target="_blank">Best Meeting Point</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Math</span><span class="topic-badge">Sorting</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 77.3%</span>
                    <span>Acceptance: 61.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Math", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/integer-to-roman" class="question-link" target="_blank">Integer to Roman</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 67.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Breadth-First Search"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-genetic-mutation" class="question-link" target="_blank">Minimum Genetic Mutation</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Breadth-First Search</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 55.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Dynamic Programming"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/pascals-triangle" class="question-link" target="_blank">Pascal's Triangle</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Dynamic Programming</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 76.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/tree-node" class="question-link" target="_blank">Tree Node</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 73.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lru-cache" class="question-link" target="_blank">LRU Cache</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 44.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Linked List", "Recursion"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/reverse-linked-list" class="question-link" target="_blank">Reverse Linked List</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 78.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-student-number-in-departments" class="question-link" target="_blank">Count Student Number in Departments</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 59.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/masking-personal-information" class="question-link" target="_blank">Masking Personal Information</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 50.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Binary Search", "Sorting", "Heap (Priority Queue)", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/kth-smallest-element-in-a-sorted-matrix" class="question-link" target="_blank">Kth Smallest Element in a Sorted Matrix</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Binary Search</span><span class="topic-badge">Sorting</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 63.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Divide and Conquer", "Binary Indexed Tree", "Segment Tree", "Line Sweep", "Heap (Priority Queue)", "Ordered Set"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/the-skyline-problem" class="question-link" target="_blank">The Skyline Problem</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Binary Indexed Tree</span><span class="topic-badge">Segment Tree</span><span class="topic-badge">Line Sweep</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Ordered Set</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 43.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Database"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/invalid-tweets" class="question-link" target="_blank">Invalid Tweets</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 85.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Depth-First Search", "Breadth-First Search", "Union Find", "Matrix"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/count-sub-islands" class="question-link" target="_blank">Count Sub Islands</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 72.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-elements-to-add-to-form-a-given-sum" class="question-link" target="_blank">Minimum Elements to Add to Form a Given Sum</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 44.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Bit Manipulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/binary-gap" class="question-link" target="_blank">Binary Gap</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Bit Manipulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 64.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "String", "Depth-First Search", "Breadth-First Search", "Graph", "Topological Sort"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/alien-dictionary" class="question-link" target="_blank">Alien Dictionary</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">String</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Graph</span><span class="topic-badge">Topological Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 36.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Array", "Queue", "Simulation"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/time-needed-to-buy-tickets" class="question-link" target="_blank">Time Needed to Buy Tickets</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Queue</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 70.3%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String", "Stack", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/remove-all-occurrences-of-a-substring" class="question-link" target="_blank">Remove All Occurrences of a Substring</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 78.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Breadth-First Search", "Heap (Priority Queue)", "Matrix"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/trapping-rain-water-ii" class="question-link" target="_blank">Trapping Rain Water II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Matrix</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 58.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/finding-the-users-active-minutes" class="question-link" target="_blank">Finding the Users Active Minutes</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 80.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "String", "Dynamic Programming", "Backtracking", "Trie", "Memoization"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/word-break-ii" class="question-link" target="_blank">Word Break II</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Backtracking</span><span class="topic-badge">Trie</span><span class="topic-badge">Memoization</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 52.9%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Math", "String", "Simulation"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/multiply-strings" class="question-link" target="_blank">Multiply Strings</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">String</span><span class="topic-badge">Simulation</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Two Pointers", "String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/one-edit-distance" class="question-link" target="_blank">One Edit Distance</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Two Pointers</span><span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 34.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Database"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/rectangles-area" class="question-link" target="_blank">Rectangles Area</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 68.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/gas-station" class="question-link" target="_blank">Gas Station</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 46.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Database"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/customer-placing-the-largest-number-of-orders" class="question-link" target="_blank">Customer Placing the Largest Number of Orders</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Database</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 64.5%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Hash Table", "Enumeration"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-black-blocks" class="question-link" target="_blank">Number of Black Blocks</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Enumeration</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 37.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "Linked List", "Design", "Doubly-Linked List"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-authentication-manager" class="question-link" target="_blank">Design Authentication Manager</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Linked List</span><span class="topic-badge">Design</span><span class="topic-badge">Doubly-Linked List</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 58.1%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Hash Table", "String", "Counting"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/minimum-number-of-steps-to-make-two-strings-anagram" class="question-link" target="_blank">Minimum Number of Steps to Make Two Strings Anagram</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">String</span><span class="topic-badge">Counting</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 82.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["Hash Table", "Math", "Two Pointers"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/happy-number" class="question-link" target="_blank">Happy Number</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Two Pointers</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 57.6%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/lowest-common-ancestor-of-a-binary-search-tree" class="question-link" target="_blank">Lowest Common Ancestor of a Binary Search Tree</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Tree</span><span class="topic-badge">Depth-First Search</span><span class="topic-badge">Binary Search Tree</span><span class="topic-badge">Binary Tree</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 67.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="EASY" data-topics='["String", "Stack"]'>
                <span class="difficulty difficulty-easy">EASY</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/valid-parentheses" class="question-link" target="_blank">Valid Parentheses</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Stack</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 41.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Divide and Conquer", "Heap (Priority Queue)", "Merge Sort"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/merge-k-sorted-lists" class="question-link" target="_blank">Merge k Sorted Lists</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Divide and Conquer</span><span class="topic-badge">Heap (Priority Queue)</span><span class="topic-badge">Merge Sort</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 55.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["String"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/validate-ip-address" class="question-link" target="_blank">Validate IP Address</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 27.7%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Depth-First Search", "Breadth-First Search", "Union Find", "Graph"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/number-of-connected-components-in-an-undirected-graph" class="question-link" target="_blank">Number of Connected Components in an Undirected Graph</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Depth-First Search</span><span class="topic-badge">Breadth-First Search</span><span class="topic-badge">Union Find</span><span class="topic-badge">Graph</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 63.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Heap (Priority Queue)"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/process-tasks-using-servers" class="question-link" target="_blank">Process Tasks Using Servers</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Heap (Priority Queue)</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 40.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Array", "Hash Table", "Math", "Geometry"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/max-points-on-a-line" class="question-link" target="_blank">Max Points on a Line</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Hash Table</span><span class="topic-badge">Math</span><span class="topic-badge">Geometry</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 28.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Dynamic Programming", "Greedy", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/wildcard-matching" class="question-link" target="_blank">Wildcard Matching</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Greedy</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 29.4%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Math", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/permutation-sequence" class="question-link" target="_blank">Permutation Sequence</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Math</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 49.2%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["String", "Dynamic Programming", "Recursion"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/regular-expression-matching" class="question-link" target="_blank">Regular Expression Matching</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">String</span><span class="topic-badge">Dynamic Programming</span><span class="topic-badge">Recursion</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 29.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="HARD" data-topics='["Linked List", "Design"]'>
                <span class="difficulty difficulty-hard">HARD</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/design-skiplist" class="question-link" target="_blank">Design Skiplist</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Linked List</span><span class="topic-badge">Design</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 61.8%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Two Pointers", "Design", "Iterator"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/flatten-2d-vector" class="question-link" target="_blank">Flatten 2D Vector</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Two Pointers</span><span class="topic-badge">Design</span><span class="topic-badge">Iterator</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 66.4%</span>
                    <span>Acceptance: 50.0%</span>
                </div>
            </div>

            <div class="question-card" data-difficulty="MEDIUM" data-topics='["Array", "Greedy"]'>
                <span class="difficulty difficulty-medium">MEDIUM</span>
                <h2 class="question-title">
                    <a href="https://leetcode.com/problems/gas-station" class="question-link" target="_blank">Gas Station</a>
                </h2>
                <div class="topics">
                    <span class="topic-badge">Array</span><span class="topic-badge">Greedy</span>
                </div>
                <div class="question-stats">
                    <span>Frequency: 47.7%</span>
                    <span>Acceptance: 46.0%</span>
                </div>
            </div>

        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const questionCards = document.querySelectorAll('.question-card');
        const difficultyButtons = document.querySelectorAll('.filter-btn');
        const topicTags = document.querySelectorAll('.topic-tag');
        
        function filterQuestions() {
            const searchTerm = searchInput.value.toLowerCase();
            const activeDifficulties = Array.from(difficultyButtons)
                .filter(btn => btn.classList.contains('active'))
                .map(btn => btn.dataset.difficulty);
            const activeTopics = Array.from(topicTags)
                .filter(tag => tag.classList.contains('active'))
                .map(tag => tag.dataset.topic);
            
            questionCards.forEach(card => {
                const title = card.querySelector('.question-title').textContent.toLowerCase();
                const difficulty = card.dataset.difficulty;
                const topics = JSON.parse(card.dataset.topics);
                
                const matchesSearch = title.includes(searchTerm);
                const matchesDifficulty = activeDifficulties.includes(difficulty);
                const matchesTopics = activeTopics.length === 0 || 
                    topics.some(topic => activeTopics.includes(topic));
                
                if (matchesSearch && matchesDifficulty && matchesTopics) {
                    card.style.display = 'flex';
                    card.style.opacity = '1';
                } else {
                    card.style.display = 'none';
                    card.style.opacity = '0';
                }
            });
        }
        
        // Add smooth transitions for filtering
        questionCards.forEach(card => {
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        });
        
        searchInput.addEventListener('input', filterQuestions);
        
        difficultyButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                btn.classList.toggle('active');
                filterQuestions();
            });
        });
        
        topicTags.forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('active');
                filterQuestions();
            });
        });

        // Add smooth scrolling for all links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
